package com.das.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.List;


@Data
public class SysUserRole extends Model<SysUserRole> {

    private static final long serialVersionUID = 1L;

    /**
     * userid
     */
    private Long userId;

    /**
     * roleid
     */

    private Long roleId;

    @TableField(exist = false)
    private List<SysUserRole> sysUserRoleList;
}
