package com.das.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.system.entity.SysCode;
import com.das.system.entity.entitywrap.CodeWrap;
import com.das.system.entity.entitywrap.PageEntity;

import java.util.List;
import java.util.Map;

/**
 * 系统字典管理接口
 *
 * <AUTHOR>
 * @since 2020-7-11
 */
public interface ISysCodeService extends IService<SysCode> {
    /**
     * 获取字典类型列表
     *
     * @return List
     */

    /**
     * 根据类型获取字典列表List
     *
     * @param field 字典实体
     * @return List
     */
    List<SysCode> getSysCodeList(String field);


    List<CodeWrap> getSysCodeTree();

    IPage<SysCode> getAll(PageEntity pageEntity);

    List<SysCode> getCodeDetail(SysCode sysCode);

    boolean saveCode(SysCode sysCode);

    boolean saveCodeDetail(SysCode sysCode);

    boolean deleteCode(SysCode sysCode);

    /**
     * 根据id查询字典
     * @param sysCode
     * @return
     */
    List<CodeWrap> getCodeById(SysCode sysCode);

    /**
     * 根据类型查询字典
     * @param sysCode
     * @return
     */
    List<CodeWrap> getCodeByField(SysCode sysCode);

    /**
     * 通过field集合查询字典
     */
    List<Map>getCodeByMap(Map map);

    SysCode getByFieldAndDes(String field, String description);

    List<SysCode> getByFiledAndIsChild(String field, Integer isLastChild);

    String getTextureByIds(String ids);

    List<Map> getCodeToMap(Map map);
}
