package com.das.system.entity.entitywrap;

import com.das.system.entity.SysDept;
import lombok.Data;

import java.util.List;

/**
 * @author: SL
 * @create: 2020-08-24 09:43
 **/
@Data
public class DeptWrap extends SysDept {
    /**
     * 下级部门
     */
    private List<DeptWrap> children;
    public DeptWrap(SysDept sysDept){
        setId(sysDept.getId());
        setName(sysDept.getName());
        setParentId(sysDept.getParentId());
        setCreateTime(sysDept.getCreateTime());
        setRemark(sysDept.getRemark());
        setSortNo(sysDept.getSortNo());
        setType(sysDept.getType());
    }
}
