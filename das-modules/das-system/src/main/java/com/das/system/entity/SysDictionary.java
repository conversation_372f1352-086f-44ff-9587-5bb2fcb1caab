package com.das.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-04-21
 */
@Data
public class SysDictionary extends Model<SysDictionary> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 字典名称
     */
    private String value;

    /**
     * 字典类型
     */
    private String type;

    /**
     * 字典编号
     */
    private String number;

    /**
     * 父级id
     */
    private Long parentId;

    /**
     * 字典排序
     */
    private Integer sort;
    /**
     * 海拔
     */
    @TableField(exist = false)
    private float altitude;
    /**
     * 偏转角度
     */
    @TableField(exist = false)
    private float angle;



    /**
     * 字典层级
     */
    private Integer level;

    @TableField(exist = false)
    private List<SysDictionary> dictionaries;

    @TableField(exist = false)
    private String parent;

}
