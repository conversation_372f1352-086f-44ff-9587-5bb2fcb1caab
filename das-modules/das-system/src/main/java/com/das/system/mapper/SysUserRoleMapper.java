package com.das.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.das.system.entity.SysUserRole;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
public interface SysUserRoleMapper extends BaseMapper<SysUserRole> {

    /**
     *根据管理员角色ID批量添加权限
     */
    boolean insertBatchPermissionsByRoleId(@Param("user_id") Long user_id, @Param("permission") String[] permission);

    List<Map> getUserRoleInfo(@Param("user_id") String user_id);

    List<Map> getAllUser();

}
