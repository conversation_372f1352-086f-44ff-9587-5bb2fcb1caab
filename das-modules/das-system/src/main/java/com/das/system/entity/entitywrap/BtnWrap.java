package com.das.system.entity.entitywrap;

import com.das.system.entity.SysButton;
import lombok.Data;

import java.util.List;

/**
 * @Title: BtnWrap
 * @Package com.das.controller.entitywrap
 * @Description: TODO
 * <AUTHOR>
 * @date 2021/6/15 13:14
 */
@Data
public class BtnWrap extends SysButton {
    /**
     * 下级按钮
     */
    private List<BtnWrap> children;
    public BtnWrap(SysButton btn){
        setId(btn.getId());
        setName(btn.getName());
        setIcon(btn.getIcon());
        setMethod(btn.getMethod());
        setParentId(btn.getParentId());
    }
}
