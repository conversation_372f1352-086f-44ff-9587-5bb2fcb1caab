package com.das.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.system.entity.SysResCheck;
import com.das.system.entity.entitywrap.PageEntity;

import java.util.List;

/**
 * @author: SL
 * @create: 2021-06-22 15:05
 **/
public interface ISysResCheckService extends IService<SysResCheck> {

    /**
     * 保存或者更新审核流程
     * @param sysResCheck
     * @return
     */
    boolean saveResCheck(SysResCheck sysResCheck);

    /**
     * 查询审核流程
     */
    List<SysResCheck> getResCheckList(SysResCheck sysResCheck);

    /**
     * 删除审核流程
     */
    boolean delResCheckList(SysResCheck sysResCheck);

    //判断当前审核是否为最后一个
    boolean isLsatOne(Long type,String flag);

}
