package com.das.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.das.system.entity.entitywrap.MenuWrap;
import com.das.system.entity.SysMenu;

import java.util.List;

/**
 * 菜单权限接口
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
public interface ISysMenuService extends IService<SysMenu> {
    /**
     * 获取用户菜单权限列表
     *
     * @return list
     */
    List<MenuWrap> getUserMenus();

    /**
     * 获取菜单
     * @return
     */
    List<MenuWrap> getMenu();
    List<MenuWrap> getManagerMenu();

    /**
     * 获取角色菜单
     * @param UserId
     * @return
     */
    List getRoleMenu(String UserId);

    SysMenu getMenuById(SysMenu sysMenu);

    boolean deleteMenuById(SysMenu sysMenu);

    boolean saveMenu(SysMenu sysMenu);

    List<MenuWrap> getAllMenus();

    List<SysMenu> getCenterMeun();

    /**
     * 获取用户首页菜单权限列表
     *
     * @return
     */
    List<MenuWrap> getUserHomeMenus();
}
