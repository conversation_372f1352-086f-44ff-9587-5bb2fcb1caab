package com.das.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.system.entity.SysUserLog;
import com.das.system.entity.entitywrap.PageEntity;

/**
 * 用户记录以及统计管理接口
 *
 * <AUTHOR>
 * @since 2020-07-09
 */
public interface ISysUserLogService extends IService<SysUserLog> {

    IPage<SysUserLog> getSysUserLog(PageEntity pageEntity);

}
