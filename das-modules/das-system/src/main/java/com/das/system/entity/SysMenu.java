package com.das.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 系统菜单工具类
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@Data
public class SysMenu extends Model<SysMenu> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 菜单名称
     */
    @NotBlank(message = "菜单名称不能为空")
    private String name;

    /**
     * 菜单图标
     */
    @NotBlank(message = "菜单图标不能为空")
    private String icon;

    /**
     * 菜单路径
     */
    @NotBlank(message = "菜单路径不能为空")
    private String path;

    /**
     * 菜单父id
     */
    @NotNull(message = "菜单父id不能为空")
    private Long parentId;

    /**
     * 菜单层级
     */
    @NotNull(message = "菜单层级不能为空")
    private Integer level;

    /**
     * 菜单排序
     */
    private Integer sortNo;

    /**
     * 菜单状态(0:启用；1：禁用)
     */
    private Integer status;

    /**
     * 是否开启权限管理(0:启用；1：禁用)
     */
    private Integer isLock;

    @TableField(exist = false)
    private List<SysButton> sysMenuList;

}
