package com.das.system.controller;

import com.das.annotation.RequestSecurity;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.service.IUserLogService;
import com.das.utils.response.ResultMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 查询用户行为日志
 *
 * <AUTHOR>
 * @since 2020-07-09
 */
@RestController
@RequestMapping("/log")
@Slf4j
public class UserLogController {

    @Autowired
    private IUserLogService userLogService;

    //获取用户日志
    @RequiresPermissions(value = {"/system/log/userlog"}, logical = Logical.OR)
    @RequestSecurity(value = "/getUserlog", method = RequestMethod.POST)
    public ResultMsg getStatistics(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(userLogService.getUserlog(pageEntity));
    }

}
