package com.das.system.handle;

import com.das.register.ApplicationContextRegister;
import com.das.utils.common.StringHandle;
import com.das.yml.DbBackConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020-07-15 11:28
 */
@Slf4j
public class DownLoadSql {
    private static ApplicationContext applicationContext = ApplicationContextRegister.getApplicationContext();
    private static DbBackConfig dbBackConfig = applicationContext.getBean(DbBackConfig.class);

    public static void downloadSqlFile(HttpServletResponse response) {
        File fileOne = null;
        // 备份逻辑先把流写成文件然后在读取
        try {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");//设置日期格式
            String filenameOne = df.format(new Date()) + "1_" + ".sql";//当没有设置文件名时，以当前时间命名数据库备份文件
            String filename = df.format(new Date()) + "_" + ".sql";//当没有设置文件名时，以当前时间命名数据库备份文件
            //获取当前系统类型
            BackupHandler backupHandler  = new BackupHandler();
//            String pathWindow = "src/main/resources/mysqldump -h";
            String System_path = backupHandler.getOS();
            String cmd = System_path + dbBackConfig.getIp() +" -P"+dbBackConfig.getPort()+
                    " -u" + dbBackConfig.getUsername() + " -p" + dbBackConfig.getPassword() +
                    " --set-charset=UTF8 " + dbBackConfig.getName();
            log.info("cmd命令:"+cmd);
            Process process = Runtime.getRuntime().exec(cmd);
            InputStream fisFile = process.getInputStream();
            fileOne = new File(filenameOne);
            OutputStream os = new FileOutputStream(fileOne);
            int bytesRead = 0;
            byte[] buffers = new byte[8192];
            while ((bytesRead = fisFile.read(buffers, 0, 8192)) != -1) {
                os.write(buffers, 0, bytesRead);
            }
            os.close();
            fisFile.close();
            String path = fileOne.getPath();
            InputStream fis = new BufferedInputStream(new FileInputStream(path));
            // 以流的形式下载文件。
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();
            // 清空response
            response.reset();
            // 设置response的Header
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(filename.getBytes()));
            response.addHeader("Content-Length", "" + fileOne.length());
            OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            toClient.write(buffer);
            toClient.flush();
            toClient.close();
        } catch (IOException ex) {
            ex.printStackTrace();
        } finally {
            try {
                if (fileOne != null) {
                    fileOne.delete();
                }
            } catch (Exception e) {
                log.error(StringHandle.getExceptionMessage("读取视频长度失败", e));
            }
        }
    }

}
