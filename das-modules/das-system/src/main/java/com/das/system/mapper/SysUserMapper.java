package com.das.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.system.entity.SysUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 用户业务Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
public interface SysUserMapper extends BaseMapper<SysUser> {

    SysUser loginByUserName(String userName);

    /**
     * 分页获取用户信息
     *
     * @return IPage
     */
    IPage<SysUser> getAllUserByRole(@Param(value = "page") Page<SysUser> page,
                                 @Param(value = "params") Map<String, Object> params,
                                 @Param(value = "sort") String sort, @Param(value = "order") String order);

    /**
     * 根据用户名校验用户是否存在
     *
     * @param id       用户id
     * @param userName 用户名
     * @return user
     */
    SysUser checkByUserName(@Param(value = "id") Long id, @Param(value = "userName") String userName);

    /**
     * 根据用户昵称校验用户是否存在
     *
     * @param id   用户id
     * @param name 用户昵称
     * @return user
     */
    SysUser checkByName(@Param(value = "id") Long id, @Param(value = "name") String name);

    /**
     * 根据用户手机号校验用户是否存在
     *
     * @param id    用户id
     * @param phone 用户手机号
     * @return user
     */
    SysUser checkByPhone(@Param(value = "id") Long id, @Param(value = "phone") String phone);

    /**
     * 获取管理员
     */

    List<SysUser> getApproveUser();

}
