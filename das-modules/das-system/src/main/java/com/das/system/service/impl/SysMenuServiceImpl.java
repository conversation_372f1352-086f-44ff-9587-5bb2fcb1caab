package com.das.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.system.entity.SysMenu;
import com.das.system.entity.SysRole;
import com.das.system.entity.SysRoleMenu;
import com.das.system.entity.SysUser;
import com.das.system.entity.entitywrap.MenuWrap;
import com.das.system.mapper.SysMenuMapper;
import com.das.system.service.ISysMenuService;
import com.das.system.service.ISysRoleService;
import com.das.utils.common.Constants;
import com.das.utils.common.SystemUtil;
import lombok.val;
import org.apache.shiro.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 菜单权限接口实现类
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@Service
@Transactional
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements ISysMenuService {

    @Resource
    private SysMenuMapper menuMapper;

    @Resource
    private SysRoleMenuServiceImpl roleMenuService;

    @Resource
    private ISysRoleService iSysRoleService;

    @Override
    public List<MenuWrap> getUserMenus() {
        SysUser user = (SysUser) SecurityUtils.getSubject().getSession().getAttribute(Constants.MANAGER_USER);
        return formatMenus(menuMapper.getUserMenus(user.getGuid()));
    }

    @Override
    public List<MenuWrap> getAllMenus() {
        return formatMenus(menuMapper.getAllMenus());
    }

    @Override
    public List<SysMenu> getCenterMeun() {
        SysUser sysUser = (SysUser) SystemUtil.getLoginUser();
        List<SysRole> list = sysUser.getRoleList();
        ArrayList<String> roleIds = new ArrayList<>();
        for (SysRole sysRole : list) {
            roleIds.add(sysRole.getId().toString());
        }
        return menuMapper.getCenterMeun("(" + String.join(",", roleIds) + ")");
    }

    @Override
    public List<MenuWrap> getUserHomeMenus() {
        SysUser user = (SysUser) SecurityUtils.getSubject().getSession().getAttribute(Constants.MANAGER_USER);
        if (user == null) {
            return formatMenus(menuMapper.getUserHomeMenus(null));
        }
        return formatMenus(menuMapper.getUserHomeMenus(user.getGuid()));
    }

    @Override
    public List<MenuWrap> getMenu() {
        return formatMenus(menuMapper.getMenu());
    }

    @Override
    public List<MenuWrap> getManagerMenu() {
        List<MenuWrap> list = new ArrayList<>();
        for (SysMenu managerMenu : menuMapper.getManagerMenu()) {
            list.add(new MenuWrap(managerMenu));
        }
        return list;
    }

    @Override
    public List getRoleMenu(String role_id){
        List<String> list= menuMapper.getRoleMenu(role_id);
        SysUser user = (SysUser) SecurityUtils.getSubject().getSession().getAttribute(Constants.MANAGER_USER);
        List<SysMenu> menuList = menuMapper.getUserMenus(user.getGuid());
        List<String> lst = new ArrayList(list);
        for(String str :list){
            if(str.length()>4){
                String s = str.substring(0,str.length()-4).trim();
                if(lst.contains(s)){
                    lst.remove(s);
                }
            }
        }
        List<String> list1 = new ArrayList(lst);
        for(String str :lst){
            if(str.length()<4){
                for(SysMenu sysMenu :menuList){
                    if(Long.parseLong(str) == sysMenu.getParentId()){
                        list1.remove(str);
                        break;
                    }
                }
            }
        }
        return lst;
    }

    public static int getUserType(List<SysRole> roleList){
        int userType = 1;
        for(SysRole sr:roleList){
            if (sr.getType() == 0) {
                userType = 0;
            }
        }
        return userType;
    }


    @Override
    public SysMenu getMenuById(SysMenu sysMenu) {
        return menuMapper.selectById(sysMenu.getId());
    }

    @Override
    public boolean deleteMenuById(SysMenu sysMenu) {
        val qw =new QueryWrapper<SysMenu>();
        qw.eq("parent_id",sysMenu.getId());
        List<SysMenu> list =list(qw);
        if(list.size()>0){
            return false;
        }else{
            return removeById(sysMenu.getId());
        }
    }

    @Override
    public boolean saveMenu(SysMenu sysMenu) {
        if(saveOrUpdate(sysMenu)){
            val qw = new QueryWrapper<SysRoleMenu>();
            qw.eq("menu_id",sysMenu.getId());
            List<SysRoleMenu> list=roleMenuService.list(qw);
            if(list.size() ==0 ){
                SysRoleMenu roleMenu = new SysRoleMenu();
                roleMenu.setRoleId(Long.parseLong("1"));
                roleMenu.setMenuId(sysMenu.getId());
                roleMenu.setType(0);
                roleMenuService.save(roleMenu);
            }
            return true;
        }else{
            return false;
        }
    }


    private List<MenuWrap> formatMenus(List<SysMenu> sysMenus) {
        /*List<SysMenu> t_Menus = new ArrayList<>();
        if(true){
            for(SysMenu sm:sysMenus){
                if(sm.getName().equals("我的资源")){
                    sm.setParentId(0L);
                    sm.setIcon("dataM");
                }
                if(!sm.getName().equals("数字资源")){
                    t_Menus.add(sm);
                }
            }
        }else{
            t_Menus = sysMenus;
        }*/
        List<MenuWrap> menuList = new ArrayList<>();
        for (SysMenu menu : sysMenus) {
            // 一级菜单parentId为0
            if (menu.getParentId().equals(0L)) {
                menuList.add(new MenuWrap(menu));
            }
        }
        // 为一级菜单设置子菜单，getChild是递归调用的
        for (MenuWrap menu : menuList) {
            menu.setChildren(getChild(menu.getId(), sysMenus));
        }
        return menuList;
    }

    private List<MenuWrap> getChild(Long id, List<SysMenu> rootMenu) {
        // 子菜单
        List<MenuWrap> childList = new ArrayList<>();
        for (SysMenu menu : rootMenu) {
            // 遍历所有节点，将父菜单id与传过来的id比较
            if (menu.getParentId().equals(id)) {
                childList.add(new MenuWrap(menu));
            }
        }
        // 把子菜单的子菜单再循环一遍
        for (MenuWrap menu : childList) {
            menu.setChildren(getChild(menu.getId(), rootMenu));
        }
        if (childList.size() == 0) {
            return null;
        }
        return childList;
    }



}
