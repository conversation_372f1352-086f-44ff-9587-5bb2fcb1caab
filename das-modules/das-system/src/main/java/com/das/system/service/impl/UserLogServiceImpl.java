package com.das.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.system.entity.UserLog;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.mapper.UserLogMapper;
import com.das.system.service.IUserLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 用户记录以及统计管理业务
 *
 * <AUTHOR>
 * @since 2020-07-09
 */
@Service
public class UserLogServiceImpl extends ServiceImpl<UserLogMapper, UserLog> implements IUserLogService {

    @Autowired
    private UserLogMapper userLogMapper;

    public IPage<UserLog> getUserlog(PageEntity pageEntity) {
        return userLogMapper.getUserlog(new Page<>(pageEntity.getCurrent(), pageEntity.getSize()),
                pageEntity.getParams(), pageEntity.getSort(), pageEntity.getOrder());
    }

}
