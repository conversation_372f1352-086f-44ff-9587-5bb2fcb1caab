package com.das.system.controller;

import com.das.annotation.DasController;
import com.das.annotation.RequestSecurity;
import com.das.system.entity.SysBackup;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.entity.entitywrap.SysBackupDbEntity;
import com.das.system.handle.DownLoadSql;
import com.das.system.service.ISysBackupService;
import com.das.utils.response.ResultMsg;
import com.das.yml.DbBackConfig;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 备份数据库实体类
 *
 * <AUTHOR>
 * @since 2021-06-24
 */
@DasController(value = "/database")
public class SysBackupController {

    @Resource
    private ISysBackupService backupInfoService;

    @Resource
    DbBackConfig dbBackConfig;

    //获取数据库列表
    @RequiresPermissions(value = "/system/dataBackup", logical = Logical.OR)
    @RequestSecurity(value = "/getList", method = RequestMethod.POST)
    public ResultMsg getList(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(backupInfoService.getList(pageEntity));
    }

    /**
     * 删除数据库信息
     */
    @RequiresPermissions(value = "/system/dataBackup", logical = Logical.OR)
    @RequestSecurity(value = "/deleteRecordsInfo", method = RequestMethod.POST)
    public ResultMsg deleteRecordsInfo(@RequestBody SysBackup backupInfo) {
        return new ResultMsg(backupInfoService.deleteRecordsInfo(backupInfo));
    }

    /**
     * 增加数据库备份
     */
    @RequiresPermissions(value = "/system/dataBackup", logical = Logical.OR)
    @RequestSecurity(value = "/addBackUpDb", method = RequestMethod.POST)
    public ResultMsg addBackUpDb(@Valid @RequestBody SysBackupDbEntity entity) {
        return new ResultMsg(backupInfoService.addBackUpDb(entity));
    }

    /**
     * 异地数据库备份
     *
     * @param response HttpServletResponse
     */
    @RequiresPermissions(value = "/system/dataBackup", logical = Logical.OR)
    @RequestSecurity(value = "/remoteBackup", method = RequestMethod.POST)
    public void remoteBackup(HttpServletResponse response) {
        DownLoadSql.downloadSqlFile(response);
    }

    /**
     * 获取配置
     */
    @RequiresPermissions(value = "/system/dataBackup", logical = Logical.OR)
    @RequestSecurity(value = "/getConfig", method = RequestMethod.GET)
    public ResultMsg getBackConfig() {
        return new ResultMsg(dbBackConfig);
    }
}
