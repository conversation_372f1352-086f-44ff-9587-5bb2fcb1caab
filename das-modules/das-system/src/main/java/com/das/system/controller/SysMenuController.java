package com.das.system.controller;

import com.das.annotation.RequestSecurity;
import com.das.system.entity.SysMenu;
import com.das.system.entity.SysUser;
import com.das.system.service.ISysMenuService;
import com.das.utils.common.Constants;
import com.das.utils.common.SystemUtil;
import com.das.utils.response.ResultCode;
import com.das.utils.response.ResultMsg;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 平台菜单权限管理类
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@RestController
@RequestMapping("/sysMenu")
public class SysMenuController {
    @Autowired
    private ISysMenuService menuService;

    @RequestSecurity(value = "/getUserMenus", method = RequestMethod.GET)
    public ResultMsg getUserMenus() {
        SysUser user = (SysUser) SecurityUtils.getSubject().getSession().getAttribute(Constants.MANAGER_USER);
        if (user == null) {
            return new ResultMsg(ResultCode.CODE_10001, null);

        }
        return new ResultMsg(menuService.getUserMenus());
    }

    @RequestSecurity(value = "/getMenu", method = RequestMethod.GET)
    public ResultMsg getMenu(@RequestParam Integer type) {
        if (type == 1) {
            return new ResultMsg(menuService.getManagerMenu());
        } else {
            return new ResultMsg(menuService.getMenu());
        }
    }


    @RequestSecurity(value = "/getRoleMenu", method = RequestMethod.POST)
    public ResultMsg getRoleMenu(@RequestBody Map UserId) {
        return new ResultMsg(menuService.getRoleMenu(UserId.get("UserId").toString()));
    }

    @RequestSecurity(value = "/getMenuById", method = RequestMethod.POST)
    public ResultMsg getMenuById(@RequestBody SysMenu sysMenu) {
        return new ResultMsg(menuService.getMenuById(sysMenu));
    }

    @RequestSecurity(value = "/deleteMenuById", method = RequestMethod.POST)
    public ResultMsg deleteMenuById(@RequestBody SysMenu sysMenu) {
        return new ResultMsg(menuService.deleteMenuById(sysMenu));
    }

    @RequestSecurity(value = "/saveMenu", method = RequestMethod.POST)
    public ResultMsg saveMenu(@Valid @RequestBody SysMenu sysMenu) {
        return new ResultMsg(menuService.saveMenu(sysMenu));
    }

    @RequestSecurity(value = "/getAllMenus", method = RequestMethod.GET)
    public ResultMsg getAllMenus() {
        return new ResultMsg(menuService.getAllMenus());
    }


    /**
     * 获取个人中心菜单
     */
    @RequestSecurity(value = "/getCenterMeun", method = RequestMethod.GET)
    public ResultMsg getCenterMeun() {
        SysUser user = (SysUser) SystemUtil.getLoginUser();
        if (user == null) {
            return new ResultMsg(ResultCode.CODE_10001, ResultCode.CODE_10001.getDesc(), false);
        }
        return new ResultMsg(menuService.getCenterMeun());
    }

    /**
     * 获取首页菜单
     *
     * @return
     */
    @RequestSecurity(value = "/getUserHomeMenus", method = RequestMethod.GET)
    public ResultMsg getUserHomeMenus() {
        HashMap<String, Object> map = new HashMap<>();
        map.put("userHomeMenus", menuService.getUserHomeMenus());
        SysUser user = (SysUser) SystemUtil.getLoginUser();
        if (user == null) {
            map.put("isLogin", false);
        } else {
            map.put("isLogin", true);
        }
        return new ResultMsg(map);
    }
}
