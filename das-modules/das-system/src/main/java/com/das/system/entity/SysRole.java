package com.das.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.List;

/**
 * 系统角色工具类
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@Data
public class SysRole extends Model<SysRole> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 角色名称
     */
    private String Name;

    /**
     * 角色类型（0：超级管理员；1：管理员；2：其它）
     */
    private Integer type;

    /**
     * 角色描述
     */
    private String roleInfo;

    /**
     * 角色排序
     */
    private Integer sortNo;

    /**
     * 创建时间
     */
    private String createTime;

    @TableField(exist = false)
    private List<SysRole> sysRoleList;

    @TableField(exist = false)
    private List menuGroup;
}
