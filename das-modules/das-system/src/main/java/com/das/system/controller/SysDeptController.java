package com.das.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.das.annotation.RequestSecurity;
import com.das.annotation.SecurityParameter;
import com.das.system.entity.SysDept;
import com.das.system.entity.SysUser;
import com.das.system.service.impl.SysDeptServiceImpl;
import com.das.system.service.impl.SysUserServiceImpl;
import com.das.utils.response.ResultCode;
import com.das.utils.response.ResultMsg;
import lombok.val;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @author: SL
 * @create: 2020-08-24 09:29
 **/
@RestController
@RequestMapping("/dept")
public class SysDeptController {

    @Resource
    private SysDeptServiceImpl sysDeptService;

    @Resource
    private SysUserServiceImpl userService;

    //获取部门信息
    @RequiresPermissions(value = {"/system/depart","/system/user"}, logical = Logical.OR)
    @RequestSecurity(value = "/getDept",method = RequestMethod.GET)
//    @RequestMapping(value = "/getDept", method = RequestMethod.GET)
    public ResultMsg getDept() {
        return new ResultMsg(sysDeptService.getDept());
    }

    //新增or修改部门
    @RequiresPermissions(value = "/system/depart", logical = Logical.OR)
    @RequestSecurity(value = "/setDept", method = RequestMethod.POST)
    public ResultMsg setDept(@Valid @RequestBody SysDept sysDept) {
        boolean flag = sysDeptService.setDept(sysDept);
        return flag?new ResultMsg(flag):new ResultMsg(ResultCode.CODE_10009,null);
    }

    //删除部门
    @RequiresPermissions(value = "/system/depart", logical = Logical.OR)
    @RequestSecurity(value = "/delDept", method = RequestMethod.POST)
    public ResultMsg delDept(@RequestBody SysDept sysDept) {
        //判断该部门下是否有人员
        val q = new QueryWrapper<SysUser>();
        q.eq("dept_id",sysDept.getId()).ne("status",2);
        List list =userService.list(q);
        if(list.size() == 0){
            return new ResultMsg(sysDeptService.removeById(sysDept.getId()));
        }else{
            return  new ResultMsg(false);
        }
    }

    @RequiresPermissions(value = "/system/workflow", logical = Logical.OR)
    @RequestSecurity(value = "/getDeptUser", method = RequestMethod.GET)
    public ResultMsg getDeptUser(){
        return new ResultMsg(sysDeptService.getDeptUser());
    }
}
