package com.das.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.system.entity.SysBackup;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 菜单权限Mapper 接口
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
public interface SysBackupMapper extends BaseMapper<SysBackup> {

    /**
     * 获取数据库备份列表信息
     *
     * @param page   分页实体
     * @param params 参数map
     * @param sort   排序字段
     * @param order  排序规则
     * @return IPage
     */
    IPage<SysBackup> getList(@Param(value = "page") Page<SysBackup> page,
                              @Param(value = "params") Map<String, Object> params,
                              @Param(value = "sort") String sort, @Param(value = "order") String order);
}
