package com.das.system.controller;

import com.das.annotation.RequestSecurity;
import com.das.system.entity.SysRole;
import com.das.system.entity.SysUser;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.service.ISysRoleService;
import com.das.system.service.impl.SysUserRoleServiceImpl;
import com.das.utils.response.ResultMsg;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.MapUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Map;

/**
 * 平台角色管理
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@RestController
@Slf4j
@RequestMapping("/sysUserRole")
public class SysUserRoleController {

    @Autowired
    private SysUserRoleServiceImpl sysUserRoleService;


    @Autowired
    private ISysRoleService roleService;

    //新增or授权
//    @RequiresPermissions(value = {"/system/role"}, logical = Logical.OR)
    @RequestSecurity(value = "/addRole", method = RequestMethod.POST)
    public ResultMsg addRole(@Valid @RequestBody SysRole sysRole) {
        return new ResultMsg(roleService.addRole(sysRole));
    }

    //获取角色权限信息
    @RequestSecurity(value = "/getRole", method = RequestMethod.POST)
    public ResultMsg getRole() {
        return new ResultMsg(roleService.getRole()) ;
    }

    //分页获取角色信息
//    @RequiresPermissions(value = {"/system/role"}, logical = Logical.OR)
    @RequestSecurity(value = "/getUserRole", method = RequestMethod.POST)
    public ResultMsg getUserRole(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(roleService.getUserRole(pageEntity)) ;
    }

    //删除角色
//    @RequiresPermissions(value = {"/system/role"}, logical = Logical.OR)
    @RequestSecurity(value = "/deleteUserRole", method = RequestMethod.POST)
    public ResultMsg deleteUserRole(@RequestBody SysRole sysRole) {
        return new ResultMsg(roleService.deleteUserRole(sysRole)) ;
    }

    //获取已有的任务权限
    @RequestSecurity(value = "/getPermissions", method = RequestMethod.POST)
    public ResultMsg getPermissions(@RequestBody SysUser user) {
        return new ResultMsg(roleService.queryUserRole(user));
    }

    //角色分配菜单权限
    @RequestSecurity(value = "/getReviewList", method = RequestMethod.POST)
    public ResultMsg getReviewList(){
        return new ResultMsg(roleService.getReviewList());
    }

    //设置分配任务权限
    @RequestSecurity(value = "/setAdminPermissions", method = RequestMethod.POST)
    public ResultMsg setAdminPermissions(@RequestBody Map<String, Object> params) {
        val userid = MapUtils.getLong(params,"id");
        val permissions = MapUtils.getString(params,"permissions","").split(",");
        return new ResultMsg(sysUserRoleService.insertBatchPermissionsByRoleId(userid,permissions));
    }
}
