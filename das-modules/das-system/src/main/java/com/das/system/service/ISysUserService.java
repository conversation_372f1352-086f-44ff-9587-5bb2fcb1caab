package com.das.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.entity.entitywrap.SysUserWrap;
import com.das.system.entity.SysUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户业务接口
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
public interface ISysUserService extends IService<SysUser> {

    /**
     * 获取登录用户信息
     *
     * @param userName 用户名
     * @return User
     */
    SysUser loginByUserName(String userName);


    /**
     * 分页获取用户信息
     *
     * @param pageEntity 分页实体
     * @return IPage
     */
    IPage<SysUser> getAllUserByRole(PageEntity pageEntity);

    /**
     * 批量更新用户状态等信息
     *
     * @param user 用户实体
     * @return Boolean
     */
    boolean updateUserInfo(SysUserWrap user);

    /**
     * 获取审批用户信息
     *
     * @return List
     */
    List<SysUser> getUser();

    /**
     * 根据用户名校验用户是否存在
     *
     * @param id       用户id
     * @param userName 用户名
     * @return user
     */
    SysUser checkByUserName(@Param(value = "id") Long id, @Param(value = "userName") String userName);

    /**
     * 根据用户昵称校验用户是否存在
     *
     * @param id   用户id
     * @param name 用户昵称
     * @return user
     */
    SysUser checkByName(@Param(value = "id") Long id, @Param(value = "name") String name);

    /**
     * 根据用户手机号校验用户是否存在
     *
     * @param id    用户id
     * @param phone 用户手机号
     * @return user
     */
    SysUser checkByPhone(@Param(value = "id") Long id, @Param(value = "phone") String phone);
}
