package com.das.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.system.entity.*;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.entity.entitywrap.SysBackupDbEntity;
import com.das.system.handle.BackupHandler;
import com.das.system.handle.ScheduleTask;
import com.das.system.mapper.SysBackupMapper;
import com.das.system.service.ISysBackupService;
import com.das.system.service.ISysTaskConfigService;
import com.das.system.service.ISysUserLogService;
import com.das.utils.common.Constants;
import com.das.utils.common.DateUtil;
import com.das.yml.DbBackConfig;
import net.sf.json.JSONObject;
import org.apache.shiro.SecurityUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * @author: SL
 * @create: 2021-06-24 09:44
 **/
@Service
@Transactional
public class SysBackupServiceImpl extends ServiceImpl<SysBackupMapper, SysBackup> implements ISysBackupService {


    @Resource
    private SysBackupMapper backupInfoMapper;
    @Resource
    private ThreadPoolTaskScheduler threadPoolTaskScheduler;
    @Resource
    private ISysBackupService backupInfoService;
    @Resource
    private DbBackConfig dbBackConfig;
    @Resource
    private ISysTaskConfigService taskConfigService;
    @Resource
    private ISysUserLogService userLogService;

    /**
     * 获取数据库备份列表信息
     *
     * @param pageEntity 分页实体
     * @return IPage
     */
    @Override
    public IPage<SysBackup> getList(PageEntity pageEntity) {
        if(pageEntity.getParams().containsKey("address")){
            String s = pageEntity.getParams().get("address").toString();
            if(s.contains("\\")){
                pageEntity.getParams().put("address",s.replaceAll("\\\\","\\\\\\\\"));//将单引号\转为\\
            }
        }
        return backupInfoMapper.getList(new Page<>(pageEntity.getCurrent(), pageEntity.getSize()),
                pageEntity.getParams(), pageEntity.getSort(), pageEntity.getOrder());
    }

    @Override
    public boolean deleteRecordsInfo(SysBackup backupInfo) {
        boolean result = false;
        if (backupInfo.getBackups().size() > 0) {
            for (SysBackup back : backupInfo.getBackups()) {
                try {
                    boolean isRecordDel;
                    SysBackup info = backupInfoService.getById(back.getId());
                    isRecordDel = backupInfoService.removeById(back.getId());
                    SysTaskConfig taskConfig = taskConfigService.getTaskConfigById(back.getId());
                    //关联定时任务的时候删除定时任务
                    if (!StringUtils.isEmpty(taskConfig)) {
                        taskConfigService.deleteByBackId(back.getId());
                    }
                    if (isRecordDel) {
                        if (info != null) {
                            //中个地方当没有数据文件的时候也回显示删除失败
                            BackupHandler.delFile(info.getAddress() + '/' + info.getName());
                            result = true;
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("删除备份失败");
                }
            }
        }
        return result;
    }

    @Override
    public boolean addBackUpDb(SysBackupDbEntity entity) {
        getBackupDbEntity(entity);
        SysUserLog logOne = new SysUserLog();
        logOne.setParam(JSONObject.fromObject(entity).toString());
        logOne.setMethod("/backupInfo/addBackUpDb");
        JSONObject fromObjectNew = JSONObject.fromObject(entity);
        JSONObject fromObjectOld = JSONObject.fromObject(null);
        JSONObject fromObject = new JSONObject();
        fromObject.put("new", fromObjectNew.toString());
        fromObject.put("old", fromObjectOld.toString());
        logOne.setNewOld(fromObject.toString());
        JSONObject fromObjectUser = new JSONObject();
        fromObjectUser.put("保存的文件名：", entity.getFileName());
        fromObjectUser.put("保存路径：", entity.getSavePath());
        fromObjectUser.put("保存数据库表：", entity.getDatabaseName());
        //设置请求参数
        logOne.setDatetime(DateUtil.getDay());
        if (entity.getType() == 0) {
            logOne.setInfo("手动备份数据库列表");
            fromObjectUser.put("备份类型：", "手动备份数据库列表");
            logOne.setDescribes(fromObjectUser.toString());
            // 手动备份
            logOne.setMethodId(15L);
            //类型（1：新增；2：更新；3：删除；4：撤销；5：更改状态；6:查询；7.下载8.登录9.退出10.其他；11.）
            logOne.setTypes(2L);
            SysUserLog userLog = setLogUser(logOne);
            userLogService.save(userLog);
            try {
                return backupInfoService.manualBackup(entity);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("手动备份mysql数据库失败！");
            }
        } else {
            // 自动备份
            fromObjectUser.put("备份类型：", "自动备份数据库列表");
            logOne.setInfo("自动备份数据库列表");
            logOne.setDescribes(fromObjectUser.toString());
            logOne.setMethodId(15L);
            //类型（1：新增；2：更新；3：删除；4：撤销；5：更改状态；6:查询；7.下载8.登录9.退出10.其他；11.）
            logOne.setTypes(2L);

            SysUserLog userLog = setLogUser(logOne);
            userLogService.save(userLog);
            if (entity.getFrequency() == 1) {
                entity.setDate(entity.getWeek());
            }
            return backupInfoService.autoBackup(entity);
        }
        return false;
    }

    /**
     * 手动备份
     * @param entity
     * @return
     */
    @Override
    public boolean manualBackup(SysBackupDbEntity entity) {
        boolean result = false;
        try {
            BackupResult backupResult = BackupHandler.exportData(entity);
            if (backupResult.getResult()) {
                SysBackup backupDb = new SysBackup();
                backupDb.setAddress(!"".equals(entity.getSavePath()) ?entity.getSavePath():dbBackConfig.getSavePath());
                backupDb.setName(backupResult.getFileName());
                backupDb.setBackupTime(DateUtil.getDay());
                backupDb.setBackupType("手动备份");
                backupDb.setType("mysql数据库");
                backupDb.setIsLocalAddress("本地备份");
                result = backupInfoMapper.insert(backupDb) == 1;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 自动备份
     * @param entity
     * @return
     */
    @Override
    public boolean autoBackup(SysBackupDbEntity entity) {
        // 判断是否存在进行中任务
        SysTaskConfig config = new SysTaskConfig();
        config.setType(0);
        config.setIsClose(0);
        SysTaskConfig taskConfig = taskConfigService.getBackUpTask(config);
        if (taskConfig == null) {
            taskConfig = new SysTaskConfig();
        }
        taskConfig.setFrequency(entity.getFrequency());
        taskConfig.setDate(entity.getDate());
        taskConfig.setTime(entity.getTime());
        //这个是我加的 修改定时任务有问题
        taskConfig.setType(0);
        SysBackup backupInfo = new SysBackup();
        backupInfo.setName(entity.getFileName()+".sql");
        backupInfo.setAddress(entity.getSavePath());
        backupInfo.setBackupTime(DateUtil.getDay());
        backupInfo.setBackupType("自动备份");
        backupInfo.setType("mysql数据库");
        backupInfo.setIsLocalAddress("本地备份");
        if (backupInfoMapper.insert(backupInfo) != 1) {
            return false;
        }
        taskConfig.setBackId(backupInfo.getId());
        if (taskConfigService.saveOrUpdate(taskConfig)) {
            // 开始执行定时计划
            ScheduleTask.startCronTask(threadPoolTaskScheduler, entity);
            return true;
        }
        return false;
    }


    private void getBackupDbEntity(SysBackupDbEntity dbEntity) {
        dbEntity.setHostIP(dbBackConfig.getIp());
        dbEntity.setPort(dbBackConfig.getPort());
        dbEntity.setDatabaseName(dbBackConfig.getName());
        dbEntity.setUserName(dbBackConfig.getUsername());
        dbEntity.setPassword(dbBackConfig.getPassword());
    }

    public static SysUserLog setLogUser(SysUserLog log) {
        SysUser users = (SysUser) SecurityUtils.getSubject().getSession().getAttribute(Constants.MANAGER_USER);
        if (!StringUtils.isEmpty(users)) {
            log.setName(users.getName() + "(" + users.getUserName() + ")");
        } else {
            log.setUserGuid(null);
            log.setName("游客");
        }
        return log;
    }

}
