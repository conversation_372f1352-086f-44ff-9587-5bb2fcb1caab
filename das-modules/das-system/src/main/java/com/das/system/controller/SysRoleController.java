package com.das.system.controller;

import com.das.annotation.RequestSecurity;
import com.das.system.entity.SysRole;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.service.ISysRoleService;
import com.das.utils.response.ResultCode;
import com.das.utils.response.ResultMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * 平台角色管理
 *
 * <AUTHOR>
 * @since 2020-7-20
 */
@RestController
@RequestMapping("/sysRole")
@Slf4j
public class SysRoleController {

    @Resource
    private ISysRoleService roleService;

    //新增or修改角色
//    @RequiresPermissions(value = "/system/role", logical = Logical.OR)
    @RequestSecurity(value = "/addRole", method = RequestMethod.POST)
    public ResultMsg addRole(@Valid @RequestBody SysRole sysRole) {
        boolean flag = roleService.addRole(sysRole);
        return flag?new ResultMsg(flag):new ResultMsg(ResultCode.CODE_10010,null);
    }

    //获取角色信息
    @RequestSecurity(value = "/getRole", method = RequestMethod.POST)
    public ResultMsg getRole() {
        return new ResultMsg(roleService.getRole()) ;
    }

    //分页获取角色列表信息
//    @RequiresPermissions(value = "/system/role", logical = Logical.OR)
    @RequestSecurity(value = "/getUserRole", method = RequestMethod.POST)
    public ResultMsg getUserRole(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(roleService.getUserRole(pageEntity)) ;
    }

    //删除角色
//    @RequiresPermissions(value = "/system/role", logical = Logical.OR)
    @RequestSecurity(value = "/deleteUserRole", method = RequestMethod.POST)
    public ResultMsg deleteUserRole(@RequestBody SysRole sysRole) {
        return new ResultMsg(roleService.deleteUserRole(sysRole)) ;
    }

    //角色授权
//    @RequiresPermissions(value = "/system/role", logical = Logical.OR)
    @RequestSecurity(value = "/setRole", method = RequestMethod.POST)
    public ResultMsg setRole(@RequestBody Map<String,Object> childrens) {
        return new ResultMsg(roleService.setRole(childrens));
    }

    /**
     * 获取管理人员
     */
    @RequestSecurity(value = "/getAdminUser", method = RequestMethod.POST)
    public ResultMsg getAdminUser(@RequestBody Map<String,Object> name) {
        return new ResultMsg(roleService.getAdminUser(name.get("name").toString()));
    }

}
