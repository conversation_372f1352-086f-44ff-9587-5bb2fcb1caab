package com.das.system.controller;

import com.das.annotation.RequestSecurity;
import com.das.system.entity.SysUser;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.entity.entitywrap.PasswordWrap;
import com.das.system.entity.entitywrap.SysUserWrap;
import com.das.system.service.ISysUserService;
import com.das.utils.common.Constants;
import com.das.utils.common.StringHandle;
import com.das.utils.common.SystemUtil;
import com.das.utils.response.ResultCode;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.LockedAccountException;
import org.apache.shiro.authc.UnknownAccountException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 平台用户信息管理
 *
 * <AUTHOR>
 * @since 2020-04-19
 */
@Slf4j
@RestController
@RequestMapping("/user")
@Api(value = "系统用户管理接口", description = "系统用户管理接口")
public class SysUserController {

    @Autowired
    private ISysUserService userService;

    @ApiOperation(value = "用户登录")
    @RequestMapping(value = "/loginUser", method = RequestMethod.POST)
    public ResultMsg loginUser(@RequestBody SysUserWrap user) {
        // 可以做user的参数判断
        if (user.getUserName() == null || user.getPassword() == null) {
            return new ResultMsg(ResultCode.CODE_20001,  null);

        }
        Subject subject = SecurityUtils.getSubject();
        String password = StringHandle.createMD5(user.getPassword(), user.getUserName());
        UsernamePasswordToken usernamePasswordToken =
                new UsernamePasswordToken(user.getUserName(), password, user.isRemember());
        try {
            subject.login(usernamePasswordToken);
        } catch (UnknownAccountException ex) {
            return new ResultMsg(ResultCode.CODE_10002,  null);

        } catch (LockedAccountException ex) {
            return new ResultMsg(ResultCode.CODE_10005,  null);
        } catch (AuthenticationException ex) {
            return new ResultMsg(ResultCode.CODE_10003,  null);
        }
        SysUser u = (SysUser) subject.getSession().getAttribute(Constants.MANAGER_USER);
        if(u.getRoleList().size() == 0){
            return new ResultMsg(ResultCode.CODE_10006,null);
        }
        return new ResultMsg(u);
    }

    @ApiOperation(value = "用户退出")
    @RequestSecurity(value = "/logout", method = RequestMethod.GET)
    public ResultMsg logout() {
        Subject subject = SecurityUtils.getSubject();
        subject.logout();
        return new ResultMsg(true);
    }

    @ApiOperation(value = "分页获取用户信息")
    @RequestSecurity(value = "/getAllUserByRole", method = RequestMethod.POST)
    public ResultMsg getAllUserByRole(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(userService.getAllUserByRole(pageEntity));
    }

    @RequestSecurity(value = "/getUserSession", method = RequestMethod.GET)
    public ResultMsg getUserSession() {
        SysUser user = (SysUser) SecurityUtils.getSubject().getSession().getAttribute(Constants.MANAGER_USER);
        return new ResultMsg(user);
    }

    @ApiOperation(value = "新增or修改用户信息")
    @RequiresPermissions(value = {"/system/user", "/user/info/"}, logical = Logical.OR)
    @RequestSecurity(value = "/addUserInfo", method = RequestMethod.POST)
    public ResultMsg addUserInfo(@Valid @RequestBody SysUserWrap user) {
        // 判断账号是否可注册
        if (userService.checkByUserName(user.getId(), user.getUserName()) != null) {
            return new ResultMsg(ResultCode.CODE_10004, false);
        }

        if (userService.checkByName(user.getId(), user.getName()) != null) {
            return new ResultMsg(ResultCode.CODE_10007, false);
        }

        if (userService.checkByPhone(user.getId(), user.getPhone()) != null) {
            return new ResultMsg(ResultCode.CODE_10008, false);
        }
        if (StringHandle.isNotEmpty(user.getPassword())) {
            String password = StringHandle.createMD5(user.getPassword(), user.getUserName());
            user.setPassword(password);
        }
        if (user.getId() == null) {
            user.setGuid(StringHandle.createUUID());
        }
        return new ResultMsg(userService.saveOrUpdate(user));
    }

    @ApiOperation(value = "获取审批用户信息")
    @RequestSecurity(value = "/getUser", method = RequestMethod.GET)
    public ResultMsg getUser() {
        return new ResultMsg(userService.getUser());
    }

    @ApiOperation(value = "批量更新用户状态等信息")
    @RequiresPermissions(value = {"/system/user"}, logical = Logical.OR)
    @RequestSecurity(value = "/updateUsersInfo", method = RequestMethod.POST)
    public ResultMsg updateUserInfo(@RequestBody SysUserWrap user) {
        if (user.getUserWraps() == null || user.getUserWraps().size() == 0) {
            return new ResultMsg(ResultCode.CODE_20001,  null);
        }
        boolean flag = userService.updateUserInfo(user);
        return flag?new ResultMsg(flag):new ResultMsg(ResultCode.CODE_30006,null);
    }

    /**
     * 重新设置用户密码
     *
     * @param passwordWrap 修改密码实体
     * @return ResultMsg
     */
    @ApiOperation(value = "重新设置用户密码")
    @RequestSecurity(value = "/resetPassword",method = RequestMethod.POST)
    public ResultMsg resetPassword(@RequestBody PasswordWrap passwordWrap) {
        if (StringHandle.isEmpty(passwordWrap.getOldPassword()) || StringHandle.isEmpty(passwordWrap.getNewPassword())) {
            return new ResultMsg(ResultCode.CODE_20001, false);
        }
        SysUser loginUser = (SysUser) SystemUtil.getLoginUser();
        // 判断旧密码是否正确
        SysUser user = userService.getById(loginUser.getId());
        String passWord = StringHandle.createMD5(passwordWrap.getOldPassword(), loginUser.getUserName());
        if (!passWord.equals(user.getPassword())) {
            return new ResultMsg(ResultCode.CODE_10003, false);
        }

        String newPassWord = StringHandle.createMD5(passwordWrap.getNewPassword(), loginUser.getUserName());
        user.setPassword(newPassWord);

        return new ResultMsg(userService.updateById(user));
    }

    /**
     * 根据id获取用户信息
     */
    @ApiOperation(value = "根据id获取用户信息")
    @RequestSecurity(value = "/getUserById",method = RequestMethod.POST)
    public ResultMsg getUserById(@RequestParam Long id){
        return new ResultMsg(userService.getById(id));
    }
}