package com.das.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.das.system.entity.SysTaskConfig;
import org.apache.ibatis.annotations.Param;

/**
 * @author: SL
 * @create: 2021-06-24 13:50
 **/
public interface SysTaskConfigMapper extends BaseMapper<SysTaskConfig> {

    /**
     * 删除定时任务
     * @param id
     * @return
     */
    boolean deleteByBackId(@Param(value = "id") long id);

    /**
     * 获取定时任务
     * @param id
     * @return
     */
    SysTaskConfig getTaskConfigById(@Param(value = "id") long id);

}
