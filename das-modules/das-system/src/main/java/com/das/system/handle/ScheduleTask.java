package com.das.system.handle;

import com.das.system.entity.SysBackup;
import com.das.system.entity.SysTaskConfig;
import com.das.system.entity.entitywrap.SysBackupDbEntity;
import com.das.system.service.ISysBackupService;
import com.das.system.service.ISysTaskConfigService;
import com.das.utils.common.DateUtil;
import com.das.yml.DbBackConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.ScheduledFuture;

/**
 * GwkTimer
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@Component
public class ScheduleTask {
    private static final Logger logger = LoggerFactory.getLogger(ScheduleTask.class);

    @Resource
    private ThreadPoolTaskScheduler threadPoolTaskScheduler;

    @Resource
    private ISysTaskConfigService taskConfigService;

    @Resource
    private ISysBackupService iBackupInfoService;

    @Resource
    private DbBackConfig dbBackConfig;

    private static ScheduledFuture<?> future;

    private static class Task implements Runnable {
        private SysBackupDbEntity entity;

        Task(SysBackupDbEntity entity) {
            this.entity = entity;
        }

        @Override
        public void run() {
            logger.debug("开始执行备份任务");
            System.out.println("启动定时任务" + DateUtil.getDay() + " hello");
            BackupHandler.exportData(entity);
        }
    }

    @PostConstruct
    public void initialize() {
        logger.debug("初始化备份定时任务");
        SysBackupDbEntity entity = getEntity();
        if (entity != null) {
            entity.setHostIP(dbBackConfig.getIp());
            entity.setDatabaseName(dbBackConfig.getName());
            entity.setUserName(dbBackConfig.getUsername());
            entity.setPassword(dbBackConfig.getPassword());
            String cron = getTaskCronForBackup(entity);
            //   System.out.println("初始化备份定时任务1"+cron);
            logger.debug("任务周期: " + cron);
            future = threadPoolTaskScheduler.schedule(new Task(entity), new CronTrigger(cron));
        }
    }

    public static void startCronTask(ThreadPoolTaskScheduler scheduler, SysBackupDbEntity entity) {
        if (future != null) {
            future.cancel(true);
        }
        String cron = getTaskCronForBackup(entity);
        logger.debug("任务周期: " + cron);
        future = scheduler.schedule(new Task(entity), new CronTrigger(cron));
    }

    private SysBackupDbEntity getEntity() {
        SysTaskConfig config = new SysTaskConfig();
        SysBackupDbEntity entity = new SysBackupDbEntity();
        config.setType(0);
        config.setIsClose(0);
        SysTaskConfig taskConfig = taskConfigService.getBackUpTask(config);
        if (taskConfig == null) {
            return null;
        } else {
            SysBackup backupInfo = iBackupInfoService.getById(taskConfig.getBackId());
            if (backupInfo == null) {
                backupInfo = new SysBackup();
                backupInfo.setAddress(dbBackConfig.getAddress());
            }
            entity.setFrequency(taskConfig.getFrequency());
            entity.setDate(taskConfig.getDate());
            entity.setTime(taskConfig.getTime());
            entity.setSavePath(backupInfo.getAddress());
            entity.setFileName(backupInfo.getName());
            entity.setType(1);
            return entity;
        }
    }

    private static String getTaskCronForBackup(SysBackupDbEntity entity) {
        if (entity.getFrequency() == 0) {
            // 按照每日定时
            return "0 0 " + entity.getTime() + " * * ? ";
        } else if (entity.getFrequency() == 1) {
            // 按照每周定时
            return "0 0 " + entity.getTime() + " ? * " + getWeak(entity.getDate());
        } else {
            // 按照每月定时
            return "0 0 " + entity.getTime() + " " + entity.getDate() + " * ?";
        }
    }

    private static String getWeak(int data) {
        switch (data) {
            case 1:
                return "MON";
            case 2:
                return "TUE";
            case 3:
                return "WED";
            case 4:
                return "THU";
            case 5:
                return "FRI";
            case 6:
                return "SAT";
            case 7:
                return "SUN";
            default:
                break;
        }
        return "SUN";
    }
}
