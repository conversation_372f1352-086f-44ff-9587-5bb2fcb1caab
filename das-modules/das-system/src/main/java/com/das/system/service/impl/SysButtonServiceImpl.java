package com.das.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.system.entity.SysButton;
import com.das.system.entity.entitywrap.BtnWrap;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.mapper.SysButtonMapper;
import com.das.system.service.ISysButtonService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 菜单权限接口实现类
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@Service
@Transactional
public class SysButtonServiceImpl extends ServiceImpl<SysButtonMapper, SysButton> implements ISysButtonService {

    @Resource
    private SysButtonMapper sysButtonMapper;

    @Override
    public List<BtnWrap> getBtn(String userId, String menuId) {
        return formatMenus(sysButtonMapper.getBtn(userId,menuId));
    }

    @Override
    public IPage<SysButton> getBtnList(PageEntity pageEntity) {
        return sysButtonMapper.getBtnList(new Page<>(pageEntity.getCurrent(),pageEntity.getSize()),
                pageEntity.getParams(),pageEntity.getSort(),pageEntity.getOrder());
    }


    private List<BtnWrap> formatMenus(List<SysButton> sysButtons) {
        List<BtnWrap> menuList = new ArrayList<>();
        for (SysButton btn : sysButtons) {
            // 一级菜单parentId为0
            if (btn.getParentId().equals(0L)) {
                menuList.add(new BtnWrap(btn));
            }
        }
        // 为一级菜单设置子菜单，getChild是递归调用的
        for (BtnWrap btn : menuList) {
            btn.setChildren(getChild(btn.getId(), sysButtons));
        }
        return menuList;
    }

    private List<BtnWrap> getChild(Long id, List<SysButton> rootBtn) {
        // 子菜单
        List<BtnWrap> childList = new ArrayList<>();
        for (SysButton btn : rootBtn) {
            // 遍历所有节点，将父菜单id与传过来的id比较
            if (btn.getParentId().equals(id)) {
                childList.add(new BtnWrap(btn));
            }
        }
        // 把子菜单的子菜单再循环一遍
        for (BtnWrap btn : childList) {
            btn.setChildren(getChild(btn.getId(), rootBtn));
        }
        if (childList.size() == 0) {
            return null;
        }
        return childList;
    }
}
