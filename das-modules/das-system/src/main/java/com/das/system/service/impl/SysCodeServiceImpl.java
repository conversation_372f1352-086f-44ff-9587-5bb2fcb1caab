package com.das.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.system.entity.SysCode;
import com.das.system.entity.entitywrap.CodeWrap;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.mapper.SysCodeMapper;
import com.das.system.service.ISysCodeService;
import com.das.utils.common.CangPConstant;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.xmlbeans.impl.common.QNameHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户业务接口实现类
 *
 * <AUTHOR>
 * @since 2020-07-11
 */
@Slf4j
@Service
@Transactional
public class SysCodeServiceImpl extends ServiceImpl<SysCodeMapper, SysCode> implements ISysCodeService {

    @Autowired
    private SysCodeMapper sysCodeMapper;

    @Override
    public List<SysCode> getSysCodeList(String field) {
        return sysCodeMapper.getSysCodeList(field);
    }

    @Override
    public List<CodeWrap> getSysCodeTree() {
        return formatMenus(sysCodeMapper.getSysCodeTree());
    }

    @Override
    public IPage<SysCode> getAll(PageEntity pageEntity) {
        IPage<SysCode> page = sysCodeMapper.getAll(new Page<>(pageEntity.getCurrent(),pageEntity.getSize()),
                pageEntity.getParams(),pageEntity.getSort(),pageEntity.getOrder());
        if(!pageEntity.getParams().get("id").toString().equals("1000")){
            page = sysCodeMapper.getAllById(new Page<>(pageEntity.getCurrent(),pageEntity.getSize()),
                    pageEntity.getParams(),pageEntity.getSort(),pageEntity.getOrder());
        }
        return page;
    }

    @Override
    public List<SysCode> getCodeDetail(SysCode sysCode) {
        return sysCodeMapper.getCodeDetail(sysCode.getId());
    }

    @Override
    public boolean saveCode(SysCode sysCode) {
        List<SysCode> list = sysCodeMapper.getSysCodeType(sysCode.getField(),0);
        if(list.size()>1){
            return false;
        }
        //新增父节点
        if(sysCode.getDescription().substring(0,2).equals("新增")){
            sysCode.setDescription(sysCode.getName());
            sysCode.setParentId(sysCode.getParentId());
            sysCode.setCode("1");
            sysCode.setSortNo(1);
            return this.save(sysCode);
        }else{//修改父节点
            sysCode.setDescription(sysCode.getName());
            return sysCodeMapper.updateByField(sysCode)>0;
        }
    }

    @Override
    public boolean saveCodeDetail(SysCode sysCode) {
        Integer sortno = sysCodeMapper.getMax(sysCode.getParentId());
        sysCode.setCode((String.valueOf(sortno+1)));
        sysCode.setSortNo(sortno+1);
        sysCode.setIsLastChild(1);
        return this.saveOrUpdate(sysCode);
    }

    @Override
    public boolean deleteCode(SysCode sysCode) {
        val qw = new QueryWrapper<SysCode>();
        qw.eq("parent_id",sysCode.getId());
        List list = sysCodeMapper.selectList(qw);
        if(list.size() == 0){
            val q = new QueryWrapper<SysCode>();
            q.eq("id", sysCode.getId());
            return remove(q);
        }else{
            return false;
        }
    }

    @Override
    public List<CodeWrap> getCodeById(SysCode sysCode) {
        return formatMenus(sysCodeMapper.getCodeById(sysCode.getParentId()));
    }

    @Override
    public List<CodeWrap> getCodeByField(SysCode sysCode) {
        List<SysCode> list = sysCodeMapper.getCodeByField(sysCode);
        //对查询质地进行特殊处理
        if("MCR_MATTERTYPE".equals(list.get(0).getField())){
            List<SysCode> lst = sysCodeMapper.getCodeById(list.get(0).getId());
            List<SysCode> list_new = new ArrayList<>();
            List<SysCode> lista = new ArrayList<>();
            try{
                list_new = deepCopy(lst);
                lista = deepCopy(lst);

            }catch (Exception e){
                log.error("list转换失败 {}",e.getMessage());
            }
            //获取无机复合或组合id
            List<SysCode> listW = list_new.stream().filter(s->s.getField().equals("MCR_MATTERTYPE_W_F_OPTION")).collect(Collectors.toList());
            //获取有机复合或组合id
            List<SysCode> listY = list_new.stream().filter(s->s.getField().equals("MCR_MATTERTYPE_Y_F_OPTION")).collect(Collectors.toList());
            //获取有机无机复合或组合id
            List<SysCode> listA = list_new.stream().filter(s->s.getField().equals("MCR_MATTERTYPE_A_F_OPTION")).collect(Collectors.toList());

            //新增无机复合或组合
            List<SysCode> list_W = list_new.stream().filter(s->s.getField().equals("MCR_MATTERTYPE_D_W_OPTION") && s.getIsLastChild() == 1).collect(Collectors.toList());
            list_W.forEach(a->a.setParentId(listW.get(0).getId()));
            lst.addAll(list_W);
            //新增有机复合或组合
            List<SysCode> list_Y = list_new.stream().filter(s->s.getField().equals("MCR_MATTERTYPE_D_Y_OPTION") && s.getIsLastChild() == 1).collect(Collectors.toList());
            list_Y.forEach(a->a.setParentId(listY.get(0).getId()));
            lst.addAll(list_Y);
            //新增有机无机复合或组合
            List<SysCode> list_A = lista.stream().filter(s->(s.getField().equals("MCR_MATTERTYPE_D_Y_OPTION") || s.getField().equals("MCR_MATTERTYPE_D_W_OPTION")) && s.getIsLastChild() == 1).collect(Collectors.toList());
            list_A.forEach(a->a.setParentId(listA.get(0).getId()));
            lst.addAll(list_A);
            return formatMenus(lst);
        }
        return formatMenus(sysCodeMapper.getCodeById(list.get(0).getId()));
    }

    /**
     * list对象复制
     * @param src
     * @param <T>
     * @return
     * @throws IOException
     * @throws ClassNotFoundException
     */
    public static <T> List<T> deepCopy(List<T> src) throws IOException, ClassNotFoundException {
        ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
        ObjectOutputStream out = new ObjectOutputStream(byteOut);
        out.writeObject(src);

        ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
        ObjectInputStream in = new ObjectInputStream(byteIn);
        @SuppressWarnings("unchecked")
        List<T> dest = (List<T>) in.readObject();
        return dest;
    }

    @Override
    public List<Map> getCodeByMap(Map map) {
        val permissions = MapUtils.getString(map,"field","").substring(1,map.get("field").toString().length()-1).split(",");
        List<Map> list = new ArrayList<>();
        for(int i=0;i<permissions.length;i++){
            Map<String,Object> map1 = new HashMap<>();
            SysCode sysCode = new SysCode();
            sysCode.setField(permissions[i]);
            map1.put(permissions[i],getCodeByField(sysCode));
            list.add(map1);
        }
        return list;
    }

    @Override
    public SysCode getByFieldAndDes(String field, String description) {
        val condition = new QueryWrapper<SysCode>();
        condition.eq("field",field).eq("description",description);
        List<SysCode> list = list(condition);
        return list==null||list.isEmpty()?null:list.get(0);
    }

    @Override
    public List<SysCode> getByFiledAndIsChild(String field, Integer isLastChild) {
        val condition = new QueryWrapper<SysCode>();
        condition.eq("field",field).eq("is_last_child",isLastChild);
        List<SysCode> list = list(condition);
        return list==null||list.isEmpty()?null:list;
    }

    @Override
    public String getTextureByIds(String ids) {
        String texture = "";
        if (StringUtils.isEmpty(ids)) {return texture;}
        String[] split = ids.split(",");
        for (int i = 0; i < split.length; i++) {
            texture += getById(split[i]).getDescription();
            if (i != split.length-1){
                texture += "，";
            }
        }
        return texture;
    }

    @Override
    public List<Map> getCodeToMap(Map map) {
        val permissions = MapUtils.getString(map, "field", "").substring(1, map.get("field").toString().length() - 1).split(",");
        List<Map> list = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        for (int i = 0; i < permissions.length; i++) {
            Map<String, Object> map1 = new HashMap<>();
            map1.put(permissions[i], codeToMap(permissions[i]));
            list.add(map1);
        }
        return list;
    }

    private List<Map<String,Object>> codeToMap(String field){
        val q = new QueryWrapper<SysCode>();
        if(CangPConstant.TEXTURE.equals(field)){
            q.eq("is_last_child",1).eq("enabled",1)
                    .in("field",CangPConstant.TEXTURE_ORGANIC_FIELD, CangPConstant.TEXTURE_INORGANIC_FIELD).orderByAsc("sort_no");
            return listMaps(q);
        }
        val wrapper = new QueryWrapper<SysCode>();
        wrapper.eq("field",field).eq("is_last_child",0).eq("enabled",1);
        List<SysCode> list = list(wrapper);
        System.out.println(list.get(0));
        q.select("id,description").eq("parent_id",list.get(0).getId()).eq("enabled",1)
                .eq("is_last_child",CangPConstant.MCR_AGE_LISHI_OPTION.equals(list.get(0).getId().toString())?0:1);
        return listMaps(q);
    }


    //构造字典树结构
    private List<CodeWrap> formatMenus(List<SysCode> sysCodes) {
        List<CodeWrap> codeList = new ArrayList<>();
        for (SysCode code : sysCodes) {
            // 一级节点parentId为0
            if (code.getParentId() == 0) {
                codeList.add(new CodeWrap(code));
            }
        }
        // 设置一级节点，getChild是递归调用的
        for (CodeWrap code : codeList) {
            code.setChildren(getChild(code.getId(), sysCodes));
        }
        return codeList;
    }

    private List<CodeWrap> getChild(Long id , List<SysCode> rootCode) {
        // 子节点
        List<CodeWrap> childList = new ArrayList<>();
        for (SysCode code : rootCode) {
            // 遍历所有节点，将父节点id与传过来的id比较
            if (code.getParentId().equals(id)) {
                childList.add(new CodeWrap(code));
            }
        }
        // 把子节点的子节点再循环一遍
        for (CodeWrap code : childList) {
            code.setChildren(getChild(code.getId(), rootCode));
        }
        if (childList.size() == 0) {
            return null;
        }
        return childList;
    }
}
