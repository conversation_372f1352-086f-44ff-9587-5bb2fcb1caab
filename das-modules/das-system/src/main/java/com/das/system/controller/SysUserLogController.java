package com.das.system.controller;

import com.das.annotation.RequestSecurity;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.service.ISysUserLogService;
import com.das.utils.response.ResultMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 查询用户行为日志
 *
 * <AUTHOR>
 * @since 2020-07-09
 */
@RestController
@RequestMapping("/log")
@Slf4j
public class SysUserLogController {

    @Autowired
    private ISysUserLogService sysUserLogService;

    @RequestSecurity(value = "/getSysUserLog", method = RequestMethod.POST)
    public ResultMsg getStatistics(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(sysUserLogService.getSysUserLog(pageEntity));
    }

}
