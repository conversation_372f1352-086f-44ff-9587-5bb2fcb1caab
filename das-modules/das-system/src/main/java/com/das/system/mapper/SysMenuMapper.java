package com.das.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.das.system.entity.SysMenu;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 菜单权限Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
public interface SysMenuMapper extends BaseMapper<SysMenu> {

    List<SysMenu> getUserMenus(String guid);

    Set<String> getPermissionByGuid(@Param("userGuid") String userGuid);

    /**
     * 根据管理员角色ID批量添加权限
     */
    boolean insertBatchMenuId(@Param("role_id") Integer user_id, @Param("children") String[] children);

    List<String> getRoleMenu(@Param("role_id") String role_id);

    List<SysMenu> getMenu();
    List<SysMenu> getManagerMenu();

    List<SysMenu> getAllMenus();

    List<SysMenu> getCenterMeun(@Param("type") String type);

    List<SysMenu> getUserHomeMenus(@Param("guid") String guid);

}
