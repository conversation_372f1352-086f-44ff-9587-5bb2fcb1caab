package com.das.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: SL
 * @create: 2021-06-22 14:58
 **/
@Data
public class SysResCheck {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 审批类型
     */
    @NotNull(message = "审批类型不能为空")
    private Long type;

    /**
     * 审批流名称
     */
    @NotBlank(message = "审批流名称不能为空")
    private String name;

    /**
     * 审批顺序
     */
    @NotNull(message = "审批流顺序")
    private int store;

    /**
     * 审批角色（1按角色审批2按人审批）
     */
    private int role;

    /**
     * 审批者（审批角色编号或审批人员编号）
     */
    @NotNull(message = "审批者不能为空")
    private String no;

    /**
     * 审批标志（000000000000）
     */
    private String flag;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String updateTime;

    @TableField(exist = false)
    private List<SysResCheck> sysResCheckList;

    /**
     * 审批人名称
     */
    @TableField(exist = false)
    private String userName;

    public SysResCheck(){}
}
