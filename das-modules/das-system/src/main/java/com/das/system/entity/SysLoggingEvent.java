package com.das.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 异常日志类
 *
 * <AUTHOR>
 * @since 2020-07-09
 */
@Data
public class SysLoggingEvent extends Model<SysLoggingEvent> {

    private static final long serialVersionUID = 1L;

    /**
     * 序列号
     */
    @TableId(value = "event_id", type = IdType.AUTO)
    @JsonProperty(value = "id")
    private Long eventId;

    /**
     * 异常时间
     */
    private Long timestmp;

    @TableField(exist = false)
    private String timestmpStr;

    /**
     * 异常名称
     */
    private String formattedMessage;

    /**
     * 异常内容
     */
    private String loggerName;

    private String levelString;

    private String threadName;

    private Integer referenceFlag;

    private String arg0;

    private String arg1;

    private String arg2;

    private String arg3;

    private String callerFilename;

    /**
     * 异常所在类
     */
    private String callerClass;

    /**
     * 异常所在方法
     */
    private String callerMethod;

    /**
     * 异常所在行数
     */
    private String callerLine;
}
