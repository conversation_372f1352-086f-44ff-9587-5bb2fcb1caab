package com.das.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.entity.SysTodo;

/**
 * @Title: ISysTodoService
 * @Package com.dszh.grottoes.base.service
 * @Description: TODO
 * <AUTHOR>
 * @date 2020/8/8 0008 18:18
 */
public interface ISysTodoService extends IService<SysTodo> {

    IPage<SysTodo> getSysTodoList(PageEntity pageEntity);

}
