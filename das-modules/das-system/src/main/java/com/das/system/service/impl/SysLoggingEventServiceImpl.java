package com.das.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.system.entity.SysLoggingEvent;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.mapper.SysLoggingEventMapper;
import com.das.system.service.ISysLoggingEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@Service
@Transactional
public class SysLoggingEventServiceImpl extends ServiceImpl<SysLoggingEventMapper, SysLoggingEvent> implements ISysLoggingEventService {
    @Autowired
    private SysLoggingEventMapper loggingEventMapper;

    @Override
    public IPage<SysLoggingEvent> getLoggingEvent(PageEntity pageEntity) {
        return loggingEventMapper.getLoggingEvent(new Page<>(pageEntity.getCurrent(), pageEntity.getSize()),
                pageEntity.getParams(), pageEntity.getSort(), pageEntity.getOrder());
    }
}
