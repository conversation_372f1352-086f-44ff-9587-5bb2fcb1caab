package com.das.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.system.entity.SysTaskConfig;
import com.das.system.mapper.SysTaskConfigMapper;
import com.das.system.service.ISysTaskConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 自动备份 服务实现类
 *
 * <AUTHOR>
 * @since 2021-06-24
 */
@Service
@Transactional
public class SysTaskConfigServiceImpl extends ServiceImpl<SysTaskConfigMapper, SysTaskConfig> implements ISysTaskConfigService {
    @Resource
    private SysTaskConfigMapper taskConfigMapper;

    /**
     * 获取备份定时任务
     * @param taskConfig
     * @return
     */
    @Override
    public SysTaskConfig getBackUpTask(SysTaskConfig taskConfig) {

        return this.getOne(
                new QueryWrapper<SysTaskConfig>().eq("type", taskConfig.getType())
                        .eq("isClose", taskConfig.getIsClose()));
    }

    /**
     * 删除定时任务
     * @param id
     * @return
     */
    @Override
    public boolean deleteByBackId(long id) {
        return taskConfigMapper.deleteByBackId(id);
    }

    /**
     * 获取定时任务
     * @param id
     * @return
     */
    @Override
    public SysTaskConfig getTaskConfigById(long id) {
        return taskConfigMapper.getTaskConfigById(id);
    }
}
