package com.das.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.das.annotation.DasController;
import com.das.annotation.RequestSecurity;
import com.das.system.entity.SysResCheck;
import com.das.system.entity.SysResCheckLog;
import com.das.system.entity.SysUser;
import com.das.system.service.impl.SysResCheckLogServiceImpl;
import com.das.system.service.impl.SysResCheckServiceImpl;
import com.das.utils.common.Constants;
import com.das.utils.common.SystemUtil;
import com.das.utils.response.ResultCode;
import com.das.utils.response.ResultMsg;
import lombok.val;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @author: SL
 * @create: 2021-06-22 15:09
 **/
@DasController("/sysResCheck")
public class SysResCheckController {

    @Resource
    private SysResCheckServiceImpl sysResCheckService;

    @Resource
    private SysResCheckLogServiceImpl sysResCheckLogService;

    //新增or修改审批流
    @RequiresPermissions(value = "/system/workflow", logical = Logical.OR)
    @RequestSecurity(value = "/saveResCheck",method = RequestMethod.POST)
    public ResultMsg saveResCheck(@Valid @RequestBody SysResCheck sysResCheck){
        boolean flag = sysResCheckService.saveResCheck(sysResCheck);
        return flag?new ResultMsg(true):new ResultMsg(ResultCode.CODE_30005,null);
    }

    //获取审批流信息
    @RequestSecurity(value = "/getResCheckList",method = RequestMethod.POST)
    public ResultMsg getResCheckList(@RequestBody SysResCheck sysResCheck){
        return new ResultMsg(sysResCheckService.getResCheckList(sysResCheck));
    }

    //删除审批流
    @RequiresPermissions(value = "/system/workflow", logical = Logical.OR)
    @RequestSecurity(value = "/delResCheckList",method = RequestMethod.POST)
    public ResultMsg delResCheckList(@RequestBody SysResCheck sysResCheck){
        boolean flag =sysResCheckService.delResCheckList(sysResCheck);
        return flag?new ResultMsg(true):new ResultMsg(ResultCode.CODE_30002,null);
    }

    /**
     * 查询用户菜单审核标志
     * @param type
     * @return
     */
    @RequestSecurity(value = "/getResCheckFlag",method = RequestMethod.POST)
    public ResultMsg getResCheckFlag(@RequestParam("type") long type){
        SysResCheck sysResCheck = new SysResCheck();
        sysResCheck.setType(type);
        SysUser sysUser = (SysUser) SystemUtil.getLoginUser();
        sysResCheck.setNo(sysUser.getId().toString());
        List<SysResCheck> res = sysResCheckService.getResCheckList(sysResCheck);
        return new ResultMsg(res.size()>0?res.get(0):"");
    }

    /**
     * 根据菜单查询所有审核流
     * @param type
     * @return
     */
    @RequestSecurity(value = "/getResCheckAllFlow",method = RequestMethod.POST)
    public ResultMsg getResCheckAllFlow(@RequestParam("type") long type){
        SysResCheck sysResCheck = new SysResCheck();
        sysResCheck.setType(type);
        return new ResultMsg(sysResCheckService.getResCheckList(sysResCheck));
    }

    /**
     * 根据菜单查询所有审核流
     * @param type
     * @return
     */
    @RequestSecurity(value = "/getResCheckLog", method =  RequestMethod.GET)
    public ResultMsg getResCheckLog(@RequestParam("type") long type, @RequestParam("resId") long resId , @RequestParam(value = "checkId" ,required = false,defaultValue = "")Long checkId){
        val param = new QueryWrapper<SysResCheckLog>();
        param.eq("check_function", type).eq("res_id", resId);
        if(checkId!=null){
            param.eq("check_id", checkId);
        }
        param.orderByAsc("id");
        return new ResultMsg(sysResCheckLogService.list(param));
    }
}
