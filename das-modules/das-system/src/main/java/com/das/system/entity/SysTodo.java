package com.das.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * @Title: SysTodo
 * @Package com.dszh.grottoes.base.entity
 * @Description: TODO
 * <AUTHOR>
 * @date 2020/8/8 0008 18:03
 */
@Data
public class SysTodo extends Model<SysRole> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    /**
     * 主键
     */
    private Long id;

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 资源编号(可为空)
     */
    private String resId;

    /**
     * 代表事项标题
     */
    private String title;

    /**
     * 跳转地址
     */
    private String path;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 是否作废(0正常1作废)
     */
    private int enabled;

    @TableField(exist = false)
    private String updateContent;

    public SysTodo() {
    }

    public SysTodo(Long userId, String resId, String title, String path, String createTime, int enabled) {
        this.userId = userId;
        this.resId = resId;
        this.title = title;
        this.path = path;
        this.createTime = createTime;
        this.enabled = enabled;
    }
}
