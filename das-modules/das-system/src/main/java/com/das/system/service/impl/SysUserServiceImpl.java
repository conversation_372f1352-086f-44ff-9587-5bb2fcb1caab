package com.das.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.system.entity.SysResCheck;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.entity.entitywrap.SysUserWrap;
import com.das.system.entity.SysButton;
import com.das.system.entity.SysMenu;
import com.das.system.entity.SysUser;
import com.das.system.mapper.SysButtonMapper;
import com.das.system.mapper.SysMenuMapper;
import com.das.system.mapper.SysUserRoleMapper;
import com.das.system.mapper.SysUserMapper;
import com.das.system.service.ISysRoleService;
import com.das.system.service.ISysUserService;
import com.das.utils.common.StringHandle;
import com.das.utils.common.SystemUtil;
import com.das.yml.CommonConfig;
import com.das.yml.FtpConfig;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户业务接口实现类
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@Slf4j
@Service
@Transactional
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService {

    @Resource
    private SysUserMapper sysUserMapper;
    @Autowired
    private ISysUserService userService;

    @Resource
    private SysMenuMapper menuMapper;

    @Autowired
    private CommonConfig commonConfig;

    @Resource
    private SysButtonMapper sysButtonMapper;

    @Resource
    private SysUserRoleMapper sysUserRoleMapper;

    @Resource
    private SysResCheckServiceImpl sysResCheckService;

    @Override
    public SysUser loginByUserName(String userName) {
        SysUser user = sysUserMapper.loginByUserName(userName);
        if (user != null) {
            user.setPermissions(menuMapper.getPermissionByGuid(user.getGuid()));
            List<SysMenu> lst = menuMapper.getUserMenus(user.getGuid());
            List<SysButton> list = sysButtonMapper.getPermissionById(user.getId());
            Set<String> set = new HashSet();
            for (SysMenu sysMenu : lst) {
                StringBuffer btn = new StringBuffer();
                for (SysButton sysButton : list) {
                    if (sysMenu.getId().toString().equals(sysButton.getMenuId().toString())) {
                        btn.append("/" + sysButton.getMethod());
                    }
                }
                if (!btn.toString().equals("")) {
                    set.add(sysMenu.getPath().toString() + btn);
                }
            }
            user.setBtn(set);
        }
        return user;
    }


    @Override
    public IPage<SysUser> getAllUserByRole(PageEntity pageEntity) {
        IPage iPage = sysUserMapper.getAllUserByRole(new Page<>(pageEntity.getCurrent(), pageEntity.getSize()),
                pageEntity.getParams(), pageEntity.getSort(), pageEntity.getOrder());
        List<SysUser> userList = iPage.getRecords();
        List<Map> list = sysUserRoleMapper.getAllUser();
        for (SysUser user : userList) {
            String s = "";
            for (Map map : list) {
                if (map.get("userId").equals(user.getId())) {
                    s = s + "," + map.get("name").toString();
                }
            }
            user.setRoleName(s != "" ? s.substring(1) : "");
        }
        return iPage;
    }

    @Override
    public boolean updateUserInfo(SysUserWrap user) {
        List<SysUser> userList = new ArrayList<>();
        for (SysUser u : user.getUserWraps()) {
            u.setPassword(null);
            userList.add(u);
//            Map map = new HashMap();
//            map.put("user_id",u.getId());
//            //删除当前用户绑定的角色权限
//            sysUserRoleMapper.deleteByMap(map);
        }
        List<Long> ids = userList.stream().map(s->s.getId()).collect(Collectors.toList());
        val q = new QueryWrapper<SysResCheck>();
        q.in("no",ids);
         List<SysResCheck> resCheckList = sysResCheckService.list(q);
        if(resCheckList.size() > 0){
            return false;
        }
        return updateBatchById(userList);
    }

    @Override
    public List<SysUser> getUser() {
        return userService.list();
    }

    @Override
    public SysUser checkByUserName(Long id, String userName) {
        return sysUserMapper.checkByUserName(id, userName);
    }

    @Override
    public SysUser checkByName(Long id, String name) {
        return sysUserMapper.checkByName(id, name);
    }

    @Override
    public SysUser checkByPhone(Long id, String phone) {
        if (StringHandle.isEmpty(phone)) {
            return null;
        }
        return sysUserMapper.checkByPhone(id, phone);
    }

}
