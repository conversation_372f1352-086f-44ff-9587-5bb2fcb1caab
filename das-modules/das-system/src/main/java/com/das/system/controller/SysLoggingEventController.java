package com.das.system.controller;

import com.das.annotation.RequestSecurity;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.service.ISysLoggingEventService;
import com.das.utils.response.ResultMsg;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 日志管理
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@RestController
@RequestMapping("/log")
public class SysLoggingEventController {

    @Autowired
    private ISysLoggingEventService iLoggingEventService;

    @RequiresPermissions(value = {"/system/syslog"}, logical = Logical.OR)
    @RequestSecurity(value = "/getLoggingEvent", method = RequestMethod.POST)
    public ResultMsg getLoggingEvent(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(iLoggingEventService.getLoggingEvent(pageEntity));
    }
}
