package com.das.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.das.system.entity.SysTaskConfig;

/**
 * @author: SL
 * @create: 2021-06-24 13:48
 **/
public interface ISysTaskConfigService extends IService<SysTaskConfig> {

    /**
     * 获取备份定时任务
     * @param taskConfig
     * @return
     */
    SysTaskConfig getBackUpTask(SysTaskConfig taskConfig);

    /**
     * 删除定时任务
     * @param id
     * @return
     */
    boolean deleteByBackId(long id);

    /**
     * 获取定时任务
     * @param id
     * @return
     */
    SysTaskConfig getTaskConfigById(long id);

}
