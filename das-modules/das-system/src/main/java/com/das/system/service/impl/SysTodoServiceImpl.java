package com.das.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.system.entity.SysTodo;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.mapper.SysTodoMapper;
import com.das.system.service.ISysTodoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Title: SysTodoServiceImpl
 * @Package com.dszh.grottoes.base.service.impl
 * @Description: TODO
 * <AUTHOR>
 * @date 2020/8/8 0008 18:20
 */
@Service
public class SysTodoServiceImpl extends ServiceImpl<SysTodoMapper, SysTodo> implements ISysTodoService {

    @Autowired
    SysTodoMapper sysTodoMapper;

    /**
     * 查询待办事项
     * @return
     */
    public IPage<SysTodo> getSysTodoList(PageEntity pageEntity) {
//        pageEntity.getParams().put("userId", SystemUtil.getLoginUser().getId());
        pageEntity.getParams().put("userId", "01");
        return sysTodoMapper.getSysTodoList(new Page<>(pageEntity.getCurrent(), pageEntity.getSize()),
                pageEntity.getParams());
    }

}
