package com.das.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.List;

/**
 * 用户行为日志记录表
 *
 * <AUTHOR>
 * @since 2020-04-21
 */
@Data
public class SysUserActions extends Model<SysUserActions> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户guid
     */
    private String userGuid;

    /**
     * 相关id
     */
    private String relatedId;

    /**
     * 字典表id
     */
    private Long action;

    /**
     * 小类型
     */
    private String type;

    /**
     * 停留时长
     */
    private Integer stayTime;

    /**
     * 总时长
     */
    private Integer allTime;

    /**
     * 开始时间
     */
    private String createTime;

    /**
     * 日志
     */
    @TableField(exist = false)
    private Long logs;

    /**
     * 景点编码
     */
    @TableField(exist = false)
    private Integer number;
    @TableField(exist = false)
    private String name;
    @TableField(exist = false)
    private String avatar;
    @TableField(exist = false)
    private int sex;
    @TableField(exist = false)
    private String sportName;
    @TableField(exist = false)
    private String image;
    @TableField(exist = false)
    private String startTime;
    @TableField(exist = false)
    private String endTime;
    @TableField(exist = false)
    private int visitCount;
    @TableField(exist = false)
    private int userCount;
    @TableField(exist = false)
    private List<SysUserActions> visitList;
    @TableField(exist = false)
    private List<SysUserActions> visitorList;
}
