package com.das.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.das.system.entity.SysDept;
import com.das.system.entity.entitywrap.DeptWrap;

import java.util.List;

/**
 * @author: SL
 * @create: 2020-08-24 09:22
 **/
public interface ISysDeptService extends IService<SysDept> {

    List<DeptWrap> getDept();

    boolean setDept(SysDept sysDept);

    List<SysDept> getDeptById(Long id);

    List<DeptWrap> getDeptUser();
}
