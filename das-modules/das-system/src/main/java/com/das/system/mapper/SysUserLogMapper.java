package com.das.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.system.entity.SysUserLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-07-09
 */
@Repository
public interface SysUserLogMapper extends BaseMapper<SysUserLog> {

    /**
     * 获取日志列表
     *
     * @param page 分页实体
     * @return IPage
     */
    IPage<SysUserLog> getSysUserLog(@Param(value = "page") Page<SysUserLog> page,
                              @Param(value = "params") Map<String, Object> params,
                              @Param(value = "sort") String sort, @Param(value = "order") String order);


}
