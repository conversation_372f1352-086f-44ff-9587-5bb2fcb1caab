package com.das.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @author: Yj
 * @create: 2021-06-25 09:35
 **/
@Data
public class SysResCheckLog extends Model<SysResCheckLog> {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private long id;

    /**
     * 申请表ID
     */
    private long checkId;

    /**
     * 资源编号
     */
    @NotNull
    private long resId;

    /**
     * 操作类型（1申请查看2提交审核）
     */
    @NotNull
    private Long checkFunction;

    /**
     * 审批人
     */
    @NotNull
    private String checkUser;

    /**
     * 审核结果
     */
    private String checkResult;

    /**
     * 审批状态（2通过3不通过）
     */
    @NotNull
    @Min(0)
    private byte checkFlag;

    /**
     * 审批备注
     */
    private String checkLog;

    /**
     * 审批时间
     */
    @NotNull
    private String checkTime;

    /**
     * 申请人ID
     */
    @NotNull
    private long userId;

    /**
     * 是否作废（0正常1作废）
     */
    private byte enabled;

    /**
     * 修改时间
     */
    private String updateTime;

}
