package com.das.system.controller;

import com.das.annotation.RequestSecurity;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.service.ISysTodoService;
import com.das.utils.response.ResultMsg;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * 平台角色管理
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
//@DasController(value = "/sysTodo")
@RestController
@RequestMapping("/sysTodo")
public class SysTodoController {

    @Resource
    private ISysTodoService todoS;

    //获取待办信息
    @RequestSecurity(value = "/getSysTodoList",method = RequestMethod.POST)
    public ResultMsg getSysTodoList(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(todoS.getSysTodoList(pageEntity));
    }


}
