package com.das.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.entity.entitywrap.SysUserWrap;
import com.das.system.entity.*;
import com.das.system.mapper.*;
import com.das.system.service.ISysRoleService;
import lombok.val;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * 平台角色管理实现类
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@Service
@Transactional
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements ISysRoleService {

    @Resource
    private SysRoleMapper sysRoleMapper;

    @Resource
    private SysUserRoleMapper sysUserRoleMapper;

    @Resource
    private SysMenuMapper sysMenuMapper;

    @Resource
    private SysRoleMenuMapper roleMenuMapper;

    @Resource
    private SysRoleMenuButtonMapper roleMenuButtonMapper;

    @Resource
    private SysRoleMenuButtonServiceImpl roleMenuButtonService;

    @Autowired
    private SysRoleMenuServiceImpl roleMenuService;
    /**
     * 新增角色
     * @param sysRole
     * @return
     */
    @Override
    public boolean addRole(SysRole sysRole) {
        val q = new QueryWrapper<SysRole>();
        q.eq("name",sysRole.getName());
        SysRole role = sysRoleMapper.selectOne(q);
        if(role != null){
            if(sysRole.getId() == null || (!sysRole.getId().equals(role.getId()))){
                return false;
            }
        }
        //新增或授权
        return saveOrUpdate(sysRole);
    }

    /**
     * 获取角色权限
     * @return
     */
    @Override
    public List<SysRole> getRole() {
        return sysRoleMapper.getRole();
    }

    /**
     * 分页获取角色列表
     * @param pageEntity
     * @return
     */
    @Override
    public IPage<SysRole> getUserRole(PageEntity pageEntity) {
        return sysRoleMapper.getUserRole(new Page<>(pageEntity.getCurrent(),pageEntity.getSize()),
                pageEntity.getParams(),pageEntity.getSort(),pageEntity.getOrder());
    }

    /**
     * 删除角色
     * @param sysRole
     * @return
     */
    @Override
    public boolean deleteUserRole(SysRole sysRole) {
        List<Long> list = new ArrayList();
        for(SysRole sysRole1:sysRole.getSysRoleList()){
            //如果有用户含有此角色权限，要先删除用户，再删除权限
            SysUserWrap userWrap = new SysUserWrap();
            QueryWrapper<SysUserRole> sysUserRoleQueryWrapper = new QueryWrapper<SysUserRole>();
            sysUserRoleQueryWrapper.eq("role_id",sysRole1.getId());
            List lst= sysUserRoleMapper.selectList(sysUserRoleQueryWrapper);
            if(lst.size() != 0){
                return false;
            }
            list.add(sysRole1.getId());
        }
        return removeByIds(list);
    }

    @Override
    public List<SysRole> queryUserRole(SysUser user) {
        return sysRoleMapper.getPermissions(user);
    }

    /**
     * 授予角色菜单权限
     * @param Children
     * @return
     */
    @Override
    public boolean setRole(Map Children) {
        String s = Children.get("children").toString();
        Children.put("children",s.substring(1,s.length()-1));
        val role_id = MapUtils.getInteger(Children,"id");
//        val child = MapUtils.getString(Children,"children","").split(",");
        HashSet set = new HashSet();
        String[] arr = Children.get("children").toString().split(",");
        List lst = new ArrayList();
        List btnlist = new ArrayList();
        List list1 = new ArrayList();
        SysRoleMenuButton roleMenuButton ;
        for(String str:arr){
            if(str.length()>4){//带了按钮id
                roleMenuButton = new SysRoleMenuButton();
                roleMenuButton.setButtonId(Integer.parseInt((str.substring(str.length()-4).replaceAll("^(0+)","").trim())));
                roleMenuButton.setMenuId(Long.parseLong(str.substring(0,str.length()-4).trim()));
                roleMenuButton.setRoleId(Long.parseLong(String.valueOf(role_id)));
                btnlist.add(roleMenuButton);
                list1.add(str.substring(str.length()-4).replaceAll("^(0+)","").trim());
                set.add(str.substring(0,str.length()-4).trim());
            }else{//没有按钮id
                set.add(str.trim());
            }
        };



        SysRole sysRole = this.getById(role_id);
        if (sysRole.getType() == 1) {
            set.add("44"); // 考古遗址
            set.add("45"); // 湖北地区史前遗址
            set.add("46"); // 茶马古道
            set.add("47"); // 湖北省夏商西周时期遗址
            set.add("48"); // 时空演变
            set.add("49"); // 文物信息
            set.add("50"); // 考古报告
            set.add("51"); // 考古辞典
            // 增加默认菜单权限
            set.add("35"); // 基础信息
            set.add("37"); // 我的标注
            set.add("38"); // 我的收藏
            lst.addAll(set);
        }else {
            set.remove("18");
            set.remove("35");
            set.remove("36");
            set.remove("37");
            set.remove("38");
            set.remove("39");
            set.remove("40");
            set.remove("41");
            set.remove("42");
            lst.addAll(getParent(set, set));
            // 增加默认菜单权限
            lst.add("35");
            lst.add("37");
            lst.add("38");
        }

        String[] children = (String[]) lst.toArray(new String[lst.size()]);
        val q = new QueryWrapper<SysRoleMenu>();
        q.eq("role_id",role_id).eq("type",0);
        val btn = new QueryWrapper<SysRoleMenuButton>();
        btn.eq("role_id",role_id);
        roleMenuButtonMapper.delete(btn);
        if(btnlist.size()>0){
            roleMenuButtonService.saveOrUpdateBatch(btnlist);
        }
        roleMenuMapper.delete(q);
        return sysMenuMapper.insertBatchMenuId(role_id,children);
//        return false;
    }

    private HashSet getParent(HashSet set,HashSet s1) {
        HashSet set2 = new HashSet();
        val qw = new QueryWrapper<SysMenu>();
        qw.in("id",set);
        List<SysMenu> list = sysMenuMapper.selectList(qw);
        for(SysMenu sysMenu:list){
            if(0 == Integer.valueOf(sysMenu.getParentId().toString())){
                continue;
            }
            set2.add(String.valueOf(sysMenu.getParentId()));//下一级父节点
            s1.add(String.valueOf(sysMenu.getParentId()));//所有节点
        }
        if(set2.size()>0){
            getParent(set2,s1);
        }
        return s1;
    }


    /**
     * 获取可授角色
     * 普通管理员需要将超级管理员和普通管理员过滤掉
     * @return
     */
    public List<SysRole> getReviewList(){
        return sysRoleMapper.getReviewList();
    }

    @Override
    public List<SysRole> getAdminUser(String name) {
        return sysRoleMapper.getAdminUser(name);
    }

}
