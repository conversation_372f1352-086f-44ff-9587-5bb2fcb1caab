package com.das.system.handle;

import cn.hutool.core.util.RuntimeUtil;
import com.das.register.ApplicationContextRegister;
import com.das.system.entity.BackupResult;
import com.das.system.entity.entitywrap.SysBackupDbEntity;
import com.das.yml.DbBackConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * MySQL数据库备份(导出和还原)
 *
 * <AUTHOR>
 * @since 2021-06-24
 */
@Slf4j
public class BackupHandler {

    private static ApplicationContext applicationContext = ApplicationContextRegister.getApplicationContext();
    private static DbBackConfig dbBackConfig = applicationContext.getBean(DbBackConfig.class);

    /**
     * 备份数据库
     *
     * @param entity 备份信息
     * @return BackupResult
     */
    public static BackupResult exportData(SysBackupDbEntity entity) {
        BackupResult result = new BackupResult();
        String savePath = entity.getSavePath();
        if ("".equals(savePath) || savePath == null) {
            //从配置中获取保存地址
            savePath = dbBackConfig.getSavePath();
        }
        String port = entity.getPort();
        if (null == port) {
            //从配置中获取端口
            port = dbBackConfig.getPort();
        }
        File saveFile = new File(savePath);
        // 如果目录不存在
        if (!saveFile.exists()) {
            saveFile.mkdirs();
        }
        if (!savePath.endsWith(File.separator)) {
            savePath += "/";
        }
        String fileName = entity.getFileName();
        if (entity.getType() == 1) {
            //设置日期格式
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");
            //以当前时间加name命名数据库备份文件
            fileName = df.format(new Date()) + "_" + fileName;
        }
        result.setFileName(fileName+".sql");
        try {
            String path = getOS();
            String cmd = path + entity.getHostIP() + " -P" + port +
                    " -u" + entity.getUserName() + " -p" + entity.getPassword() + " "
                    + entity.getDatabaseName() + " >" + savePath + fileName;
            log.info("cmd命令:" + cmd);
            if (System.getProperty("os.name").toLowerCase().startsWith("win")) {
                RuntimeUtil.exec(cmd);
                result.setResult(true);
            } else if (System.getProperty("os.name").toLowerCase().contains("linux")) {
                String[] cmd1 = new String[]{"/bin/sh", "-c", cmd};
                Runtime.getRuntime().exec(cmd1);
                result.setResult(true);
            } else {
                result.setResult(false);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("数据库备份错误！" + e.toString());
        }
        return result;
    }

    public static String getOS() {
        //本地的配置
        String pathWindow = "cmd /c mysqldump -h";
//        String pathWindow = "mysqldump  -h";
        //服务上的配置
//        String pathLiunx = "/usr/bin/mysqldump -h";
        String pathLiunx = dbBackConfig.getMysqldumpPath() + "/mysqldump -h";
//            //判断当前系统是否为windows系统
        String System_path = "";
        String system = System.getProperty("os.name");
        if (system.toLowerCase().startsWith("win")) {
            System_path = pathWindow;
        } else if (system.toLowerCase().contains("linux")) {
            System_path = pathLiunx;
        }
        log.info("当前系统为:" + system);
        return System_path;
    }
//    /**
//     * 实现MySQL数据库导出
//     *
//     * @return 返回true表示导出成功，否则返回false。
//     */
//    public static BackupResult exportDatabaseTool(BackupDbEntity entity) throws InterruptedException {
//        BackupResult result = new BackupResult();
////        String savePath = entity.getSavePath();
////        File saveFile = new File(savePath);
////        if (!saveFile.exists()) {// 如果目录不存在
////            saveFile.mkdirs();// 创建文件夹
////        }
////        if(!savePath.endsWith(File.separator)){
////            savePath = savePath + File.separator;
////        }
////        String fileName = entity.getFileName();
////        if (fileName == null || fileName.isEmpty()) {
////            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");//设置日期格式
////            fileName = df.format(new Date());//当没有设置文件名时，以当前时间命名数据库备份文件
////        }
////        fileName += ".sql";
////
////        result.setFileName(fileName);
////
////        PrintWriter printWriter = null;
////        InputStream error = null;
////        BufferedReader bufferedReader = null;
////        try {
////            printWriter = new PrintWriter(new OutputStreamWriter(new FileOutputStream(savePath + fileName), "utf8"));
////            String cmd = "mysqldump -h" + entity.getHostIP() +
////                    " -u" + entity.getUserName() + " -p" + entity.getPassword() +
////                    " --set-charset=UTF8 " + entity.getDatabaseName();
////            /*String cmd = entity.getAppDirection() + "mysqldump -h" + entity.getHostIP() +
////                    " -u" + entity.getUserName() + " -p" + entity.getPassword() +
////                    " --set-charset=UTF8 " + entity.getDatabaseName();
////*/
////            log.debug("[Process] CMD: "+cmd);
////            Process process = Runtime.getRuntime().exec(cmd);
////            InputStreamReader inputStreamReader = new InputStreamReader(process.getInputStream(), "utf8");
////            bufferedReader = new BufferedReader(inputStreamReader);
////            String line;
////            while((line = bufferedReader.readLine())!= null){
////                printWriter.println(line);
////            }
////            printWriter.flush();
////
////            error = process.getErrorStream();
////            BufferedReader errorBufferedReader = new BufferedReader(new InputStreamReader(error));
////            StringBuilder buffer = new StringBuilder();
////            String s;
////            while ((s = errorBufferedReader.readLine()) != null) {
////                buffer.append(s);
////            }
////            errorBufferedReader.close();
////            process.waitFor();
////            if(process.exitValue() == 0){//0 表示线程正常终止。
////                result.setResult(true);
////                return result;
////            } else {
////                log.error(buffer.toString());
////            }
////        }catch (IOException e) {
////            e.printStackTrace();
////            log.error("数据库备份错误！"+e.toString());
////        } finally {
////            try {
////                if (bufferedReader != null) {
////                    bufferedReader.close();
////                }
////                if (printWriter != null) {
////                    printWriter.close();
////                }
////            } catch (IOException e) {
////                e.printStackTrace();
////                log.error("数据库备份错误！"+e.toString());
////            }
////        }
////        result.setResult(false);
//        return result;
//    }
//
//    /**
//     * 实现MySQL数据库还原
//     *
//     * @param hostIP MySQL数据库所在服务器地址IP
//     * @param userName 进入数据库所需要的用户名
//     * @param password 进入数据库所需要的密码
//     * @param filePath 还原需要的数据库备份文件保存路径
//     * @param fileName 还原需要的数据库备份文件文件名
//     * @param databaseName 要还原的数据库名
//     * @param appDirection 数据库还原程序 mysql.exe 路径
//     * @return 返回true表示导出成功，否则返回false。
//     */
//    public static boolean revertDatabaseTool(String hostIP, String userName, String password, String filePath, String fileName, String databaseName, String appDirection)
//            throws InterruptedException {
//        Runtime runtime = Runtime.getRuntime();
////        BufferedReader bufferedReader = null;
//        String fileLocation = filePath + fileName; // sql文件路径
////        String resultStr = "";
////        try {
//////            String cmd = appDirection + "mysql -h" + hostIP + " -u" + userName + " -p" + password + " --set-charset=UTF8 " + databaseName +"< " + fileLocation;
//////            String cmd = appDirection + "mysql " + databaseName +" < " + fileLocation;
////            String cmd = appDirection + "mysqldump " + databaseName +" < " + fileLocation;
//////            String cmd = "cmd /c " + appDirection + "mysql " + databaseName +" < ";
////            Process process = runtime.exec(cmd);
//////            File datafile = new File(appDirection);
//////            Process process = runtime.exec("cmd /c mysql " + databaseName + " < "+datafile);
//////            Process process = runtime.exec(cmd + datafile);
////            InputStreamReader inputStreamReader = new InputStreamReader(process.getErrorStream(), "utf8");
////            bufferedReader = new BufferedReader(inputStreamReader);
////            String line;
////            while((line = bufferedReader.readLine())!= null){
////                resultStr += line;
////            }
////            if (process.waitFor() == 0) {
////                return true;
////            }
////        } catch (Exception e) {
////            e.printStackTrace();
////            log.error("数据库还原错误:"+ resultStr +e.toString());
////        } finally {
////            try {
////                if (bufferedReader != null) {
////                    bufferedReader.close();
////                }
////            } catch (Exception e) {
////                e.printStackTrace();
////                log.error("数据库还原错误:"+ resultStr +e.toString());
////            }
////        }
//        OutputStream outputStream = null;
//        BufferedReader br = null;
//        OutputStreamWriter writer = null;
//        try {
////            process = runtime.exec("mysql -h192.168.64.129 -P3306 -uroot -p123 test");
//            String cmd = appDirection + "mysql -h" + hostIP + " -P" + "3306 -u" + userName + " -p" + password + " " + databaseName;
//            Process process = runtime.exec(cmd);
//            outputStream = process.getOutputStream();
//            br = new BufferedReader(new InputStreamReader(new FileInputStream(fileLocation)));
//            String str = null;
//            StringBuffer sb = new StringBuffer();
//            while((str = br.readLine()) != null){
//                sb.append(str+"\r\n");
//            }
//            str = sb.toString();
//            writer = new OutputStreamWriter(outputStream,"utf-8");
//            writer.write(str);
//            writer.flush();
//            if (process.waitFor() == 0) {
//                return true;
//            }
//
//        } catch (IOException e) {
//            e.printStackTrace();
//        }finally {
//            try {
//                outputStream.close();
//                br.close();
//                writer.close();
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
//        return false;
//    }

    /**
     * 删除文件
     *
     * @param filePathAndName String 文件路径及名称 如c:/fqf.txt
     * @return boolean
     */
    public static boolean delFile(String filePathAndName) {
        boolean result = false;
        try {
            File myDelFile = new File(filePathAndName);
            result = myDelFile.delete();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("数据库备份删除出错！" + e.toString());
        }
        return result;
    }

}
