package com.das.system.entity.entitywrap;

import lombok.Data;

/**
 * 备份实体
 *
 * <AUTHOR>
 * @since 2020-04-19
 */
@Data
public class SysBackupDbEntity {
    /**
     * MySQL数据库导出方法
     *
     * <AUTHOR>
     * @param hostIP MySQL数据库所在服务器地址IP
     * @param userName 进入数据库所需要的用户名
     * @param password 进入数据库所需要的密码
     * @param savePath 数据库导出文件保存路径
     * @param fileName 数据库导出文件文件名
     * @param databaseName 要导出的数据库名
     * @param appDirection 数据库备份程序 mysqldump.exe 路径
     * @return 返回true表示导出成功，否则返回false。
     */
    private String hostIP;
    private String port;
    private String userName;
    private String password;
    private String savePath;
    private String fileName;
    private String databaseName;
    private String appDirection;
    private int type;
    private int frequency;
    /**
     * 任务调度日期
     */
    private int date;
    private int week;

    /**
     * 任务调度时间
     */
    private int time;
}
