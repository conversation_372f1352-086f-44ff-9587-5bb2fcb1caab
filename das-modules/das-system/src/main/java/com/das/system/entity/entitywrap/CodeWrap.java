package com.das.system.entity.entitywrap;

import com.das.system.entity.SysCode;
import lombok.Data;

import java.util.List;

/**
 * @author: SL
 * @create: 2021-06-21 11:24
 **/
@Data
public class CodeWrap extends SysCode {

    private List<CodeWrap> children;

    public CodeWrap(SysCode sysCode){
        setId(sysCode.getId());
        setField(sysCode.getField());
        setName(sysCode.getName());
        setCode(sysCode.getCode());
        setDescription(sysCode.getDescription());
        setEditMode(sysCode.getEditMode());
        setEnabled(sysCode.getEnabled());
        setParentId(sysCode.getParentId());
        setRemark(sysCode.getRemark());
        setIsLastChild(sysCode.getIsLastChild());
    }

}
