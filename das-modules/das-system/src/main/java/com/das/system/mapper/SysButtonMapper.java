package com.das.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.system.entity.SysButton;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 菜单权限Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
public interface SysButtonMapper extends BaseMapper<SysButton> {

    List<SysButton> getBtn(@Param("userId") String userId, @Param("menuId") String menuId);

    List<SysButton> getPermissionById(@Param("id") Long id);

    IPage<SysButton> getBtnList(@Param(value = "page") Page<SysButton> page,
                                @Param(value = "params") Map<String, Object> params,
                                @Param(value = "sort") String sort, @Param(value = "order") String order);
}
