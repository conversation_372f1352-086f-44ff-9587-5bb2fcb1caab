package com.das.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.entity.SysRole;
import com.das.system.entity.SysUser;
import java.util.List;
import java.util.Map;

/**
 * 平台角色管理接口
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
public interface ISysRoleService extends IService<SysRole> {

    /**
     *新增角色
     */
    boolean addRole(SysRole sysRole);

    /**
     * 获取角色权限
     */
    List<SysRole> getRole();

    /**
     * 分页获取角色
     * @param pageEntity
     * @return
     */
    IPage<SysRole> getUserRole(PageEntity pageEntity);

    /**
     * 删除角色
     */
    boolean deleteUserRole(SysRole sysRole);

    /**
     *获取已有的任务权限
     */
    List<SysRole> queryUserRole(SysUser user);

    /**
     * 角色分配菜单权限
     */
    boolean setRole(Map children);

    List<SysRole> getReviewList();

    List<SysRole> getAdminUser(String name);

}
