package com.das.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-07-11
 */
@Data
public class SysCode extends Model<SysCode> {

    private static final long serialVersionUID = 1L;

    /**
     * 对照id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     *对照字段
     */
    @NotBlank(message = "对照字段不能为空")
    private String field;

    /**
     * 对照字段名称
     */
    @NotBlank(message = "对照字段名称不能为空")
    private String name;

    /**
     * 代码
     */
    private String code;

    /**
     * 代码描述
     */
    @NotBlank(message = "代码描述不能为空")
    private String description;

    /**
     * 启用状态(0:禁用;1:启用)
     */
    private int enabled;

    /**
     * 编辑模式(0:只读;1:可编辑)
     */
    private int editMode;

    /**
     * 排序号
     */
    private int sortNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 父节点
     */
    @NotNull(message = "父节点不能为空")
    private Long parentId;

    /**
     * 是否为末节点
     */
    @NotNull(message = "是否为末节点不能为空")
    private int isLastChild;

}
