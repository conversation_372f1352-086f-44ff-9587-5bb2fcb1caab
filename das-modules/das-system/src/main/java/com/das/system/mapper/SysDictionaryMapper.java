package com.das.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.system.entity.SysDictionary;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-04-21
 */
public interface SysDictionaryMapper extends BaseMapper<SysDictionary> {
    /**
     * 获取字典类型列表
     *
     * @return List
     */
    List<SysDictionary> getDictionaryType();

    /**
     * 获取二级联动下拉框去重复
     *
     * @return List
     */
    List<SysDictionary> getSpotListRemoval();

    /**
     * 分页获取不同类型字典信息
     *
     * @param page   分页组件
     * @param params 参数
     * @return IPage
     */
    IPage<SysDictionary> getDictionaryInfos(@Param(value = "page") Page<SysDictionary> page,
                                            @Param(value = "params") Map<String, Object> params);

    /**
     * 根据类型获取字典列表List
     *
     * @param dictionary 字典实体
     * @return List
     */
    List<SysDictionary> getDictionary(@Param("dictionary") SysDictionary dictionary);

    /**
     * 获取二级联动下拉框
     *
     * @return List
     */
    List<SysDictionary> getSpotList();

    /**
     * 获取二级联动下拉框
     *
     * @return List
     */
    List<SysDictionary> getAttribute();

    /**
     * 获取字典中反馈状态列表
     *
     * @return List
     */
    List<SysDictionary> getBackList();

    /**
     * 获取字典中反馈属性列表
     *
     * @return List
     */
    List<SysDictionary> getFeedList();


    /**
     * 获取字典景点id
     *
     * @return List
     */
    SysDictionary getStructures();

    /**
     * 获取字典中景点列表
     *
     * @return SysDictionary
     */
    SysDictionary getPlace(String place);

    /*****************************************************************************************************************
     * ****************************************************************************************************************
     * ***************************************** 以下是小程序Api接口代码*/
    /**
     * 获取设施的列表
     *
     * @return List
     */
    List<SysDictionary> getStructure();

    /**
     * 获取景点的列表
     *
     * @return List
     */
    List<SysDictionary> getPlaces();
}
