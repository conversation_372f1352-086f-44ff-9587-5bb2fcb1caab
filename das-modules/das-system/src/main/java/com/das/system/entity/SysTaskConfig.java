package com.das.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * @author: SL
 * @create: 2021-06-24 13:46
 **/
@Data
public class SysTaskConfig extends Model<SysTaskConfig> {


    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 备份id
     */
    private Long backId;
    /**
     * 任务类型
     */
    private Integer type;

    /**
     * 任务调度频次
     */
    private Integer frequency;

    /**
     * 任务调度日期
     */
    private Integer date;

    /**
     * 任务调度时间
     */
    private Integer time;

    /**
     * 是否开启（0：开启，1：关闭）
     */
    @TableField("isClose")
    private Integer isClose;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 创建时间
     */
    private String createTime;

}
