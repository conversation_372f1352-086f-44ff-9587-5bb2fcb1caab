package com.das.system.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.system.entity.SysUserRole;
import com.das.system.mapper.SysUserRoleMapper;
import com.das.system.service.ISysUserRoleService;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 平台角色管理实现类
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@Slf4j
@Service
@Transactional
public class SysUserRoleServiceImpl extends ServiceImpl<SysUserRoleMapper, SysUserRole> implements ISysUserRoleService {

    @Resource
    private SysUserRoleMapper sysUserRoleMapper;
    /**
     * 设置分配任务权限
     * @param user_id
     * @param permissions
     * @return
     */
    @Override
    public boolean insertBatchPermissionsByRoleId(Long user_id, String[] permissions) {
        val qw = new QueryWrapper<SysUserRole>();
        qw.eq("user_id", user_id);
        remove(qw);
        if(permissions[0].equals("")){
            return true;
        }else{
            return sysUserRoleMapper.insertBatchPermissionsByRoleId(user_id, permissions);
        }
    }
}