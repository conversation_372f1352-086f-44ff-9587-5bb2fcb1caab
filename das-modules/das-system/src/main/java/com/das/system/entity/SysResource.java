package com.das.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * 系统文件保存信息
 *
 * <AUTHOR>
 * @since 2020-04-21
 */
@Data
public class SysResource extends Model<SysResource> {

    private static final long serialVersionUID = 1L;

    public SysResource(){}
    public SysResource(String name, String type, String path){
        super();
        this.name = name;
        this.type = type;
        this.path = path;
    }
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 文件名
     */
    private String name;

    /**
     * 文件类型
     */
    private String type;

    /**
     * 文件路径
     */
    private String path;

    /**
     * 是否被引用（0：是；1：否）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private String createTime;
}
