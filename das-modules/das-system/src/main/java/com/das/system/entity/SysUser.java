package com.das.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * @Title: SysUser
 * @Package com.das.system.entity
 * @Description: TODO
 * <AUTHOR>
 * @date 2021/6/15 12:55
 */
@Data
public class SysUser extends Model<SysUser> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户gid
     */
    private String guid;

    /**
     * 用户名称
     */
    private String name;

    /**
     * 用户名
     */
    @JsonProperty( value = "username")
    @NotBlank(message = "用户名不能为空")
    private String userName;

    /**
     * 用户头像
     */
    private String headImg;

    /**
     * 用户密码
     */
    private String password;

    /**
     * 用户电话
     */
    private String phone;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 部门id
     */
    private Integer deptId;

    /**
     * 职务
     */
    private String duties;

    /**
     * 用户性别(0:男；1：女；2：未知)
     */
    private Integer sex;

    /**
     * 用户状态(0:正常；1：停用；2：注销)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 部门名称
     */
    @TableField(exist = false)
    private String deptName;

    @TableField(exist = false)
    private List<SysRole> roleList;

    /**
     * 用户角色权限
     */
    @TableField(exist = false)
    private String roleName;

    @TableField(exist = false)
    private Set<String> permissions;

    @TableField(exist = false)
    private Set<String> btn;
}
