package com.das.system.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @author: SL
 * @create: 2021-06-23 15:06
 **/
@Data
public class SysBackup extends Model<SysBackup> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 备份文件名
     */
    @NotBlank(message = "备份文件名不能为空")
    private String name;

    /**
     * 保存地址类型（本地备份/异地备份）
     */
    @NotBlank(message = "保存地址类型不能为空")
    private String isLocalAddress;

    /**
     * 保存地址
     */
    @NotBlank(message = "保存地址不能为空")
    private String address;

    /**
     * 备份类型（mysql数据库/资源文件库）
     */
    @NotBlank(message = "备份类型不能为空")
    private String type;

    /**
     * 备份方式（主动备份/定时备份）
     */
    @NotBlank(message = "备份方式不能为空")
    private String backupType;

    /**
     * 备份时间
     */
    private String backupTime;


    @TableField(exist = false)
    private List<SysBackup> backups;
}
