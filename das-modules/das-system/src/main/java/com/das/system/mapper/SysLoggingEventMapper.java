package com.das.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.system.entity.SysLoggingEvent;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * 日志Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@Repository
public interface SysLoggingEventMapper extends BaseMapper<SysLoggingEvent> {
    /**
     * 获取异常日志列表
     *
     * @param page 分页实体
     * @return IPage
     */
    IPage<SysLoggingEvent> getLoggingEvent(@Param(value = "page") Page<SysLoggingEvent> page,
                                           @Param(value = "params") Map<String, Object> params,
                                           @Param(value = "sort") String sort, @Param(value = "order") String order);
}
