package com.das.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: SL
 * @create: 2020-08-24 09:14
 **/
@Data
public class SysDept {

    private static final long serialVersionUID = 1L;

    /**
     * 流水号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 组织名称
     */
    @NotBlank(message = "组织名称不能为空")
    private String name;

    /**
     * 父节点流水号
     */
    private Long parentId;

    /**
     * 组织类型
     */
    private String type;

    /**
     * 排序号
     */
    @NotNull(message = "排序号不能为空")
    private int sortNo;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 备注
     */
    private String remark;

}
