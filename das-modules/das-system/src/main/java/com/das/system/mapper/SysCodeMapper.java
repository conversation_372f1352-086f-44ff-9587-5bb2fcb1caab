package com.das.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.system.entity.SysCode;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-07-11
 */
@Repository
public interface SysCodeMapper extends BaseMapper<SysCode> {

    /**
     * 获取字典类型列表
     *
     * @return List
     */
    List<SysCode> getSysCodeType(@Param("field") String field,@Param("isLastChild")int isLastChild);


    List<SysCode> getSysCodeTree();


    /**
     * 根据类型获取字典列表List
     *
     * @param fileid 字典实体
     * @return List
     */
    List<SysCode> getSysCodeList(String fileid);

    IPage<SysCode> getAll(@Param(value = "page") Page<SysCode> page,
                          @Param(value = "params") Map<String, Object> params,
                          @Param(value = "sort") String sort, @Param(value = "order") String order);

    List<SysCode> getCodeDetail(Long id);

    Integer getMax(Long parentId);

    Integer updateByField(SysCode sysCode);

    IPage<SysCode> getAllById(@Param(value = "page") Page<SysCode> page,
                              @Param(value = "params") Map<String, Object> params,
                              @Param(value = "sort") String sort, @Param(value = "order") String order);

    /**
     * 获取当前父节点下的所有子节点
     */
    List<SysCode> getCodeById(@Param("parentId") Long parentId);

    /**
     * 根据当前类型获取父节点下所有子节点
     */
    List<SysCode> getCodeByField(SysCode sysCode);

}
