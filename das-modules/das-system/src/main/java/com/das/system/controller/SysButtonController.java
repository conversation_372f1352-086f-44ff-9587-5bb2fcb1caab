package com.das.system.controller;

import com.das.annotation.RequestSecurity;
import com.das.system.entity.SysButton;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.service.ISysButtonService;
import com.das.utils.response.ResultMsg;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 平台菜单权限管理类
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@RestController
@RequestMapping("/sysBtn")
public class SysButtonController {

    @Resource
    private ISysButtonService sysButtonService;

    //根据用户和菜单获取按钮列表
    @RequiresPermissions(value = "/system", logical = Logical.OR)
    @RequestSecurity(value = "/getBtn", method = RequestMethod.POST)
    public ResultMsg getBtn(@RequestBody Map<String,Object> Param) {
        List list = (List) Param.get("Param");
        return new ResultMsg(sysButtonService.getBtn(list.get(0).toString(),list.get(1).toString()));
    }

    //获取按钮列表
    @RequiresPermissions(value = "/system", logical = Logical.OR)
    @RequestSecurity(value = "/getBtnList", method = RequestMethod.POST)
    public ResultMsg getBtnList(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(sysButtonService.getBtnList(pageEntity));
    }

    //新增or修改按钮
    @RequiresPermissions(value = "/system", logical = Logical.OR)
    @RequestSecurity(value = "/saveBtn", method = RequestMethod.POST)
    public ResultMsg saveBtn(@Valid @RequestBody SysButton sysButton) {
        return new ResultMsg(sysButtonService.saveOrUpdate(sysButton));
    }

    //删除按钮
    @RequiresPermissions(value = "/system", logical = Logical.OR)
    @RequestSecurity(value = "/deleteBtn", method = RequestMethod.POST)
    public ResultMsg deleteBtn(@RequestBody SysButton sysButton) {
        return new ResultMsg(sysButtonService.removeById(sysButton.getId()));
    }

}
