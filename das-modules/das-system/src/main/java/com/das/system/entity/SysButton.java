package com.das.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 系统菜单工具类
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@Data
public class SysButton extends Model<SysButton> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 按钮方法名
     */
    @NotBlank(message = "按钮方法名不能为空")
    private String method;

    /**
     * 按钮名称
     */
    @NotBlank(message = "按钮名称不能为空")
    private String name;

    /**
     * 所属菜单
     */
    @NotBlank(message = "所属菜单不能为空")
    private String menuId;

    /**
     * 图标
     */
    @NotBlank(message = "图标不能为空")
    private String icon;

    /**
     * 标题浮显
     */
    private Integer title;

    /**
     * 父按钮ID
     */
    private Long parentId;

    /**
     * 按钮层级
     */
    private Integer level;

    /**
     * 排序号
     */
    private Integer sortNo;

    /**
     * 是否启用(0:启用;1:不启用)
     */
    private Integer enabled;

    @TableField(exist = false)
    private List<SysButton> sysBtnList;

}
