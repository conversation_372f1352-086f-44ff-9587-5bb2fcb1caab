package com.das.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.das.system.entity.SysResCheckLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: Yj
 * @create: 2021-06-25 10:24
 **/
public interface SysResCheckLogMapper extends BaseMapper<SysResCheckLog> {

    List<SysResCheckLog> queryByType(@Param(value = "type") Long type,@Param(value = "checkId") List checkId);

}
