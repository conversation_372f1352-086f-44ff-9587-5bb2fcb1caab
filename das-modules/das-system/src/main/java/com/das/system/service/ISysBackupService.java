package com.das.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.system.entity.SysBackup;
import com.das.system.entity.entitywrap.SysBackupDbEntity;
import com.das.system.entity.entitywrap.PageEntity;

/**
 * 菜单权限接口
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
public interface ISysBackupService extends IService<SysBackup> {
    /**
     * 获取数据库备份列表信息
     *
     * @param pageEntity 分页实体
     * @return IPage
     */
    IPage<SysBackup> getList(PageEntity pageEntity);

    /**
     * 删除数据库备份列表信息
     *
     * @param backupInfo 实体
     * @return IPage
     */
    boolean deleteRecordsInfo(SysBackup backupInfo);

    /**
     * 增加数据库备份信息
     *
     * @param entity 实体
     * @return IPage
     */
    boolean addBackUpDb(SysBackupDbEntity entity);


    boolean manualBackup(SysBackupDbEntity entity);

    boolean autoBackup(SysBackupDbEntity entity);
}
