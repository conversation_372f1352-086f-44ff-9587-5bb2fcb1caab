package com.das.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.system.entity.SysRole;
import com.das.system.entity.SysUser;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@Repository
public interface SysRoleMapper extends BaseMapper<SysRole> {
    /**
     * 获取角色权限
     */
    List<SysRole> getRole();

    /**
     * 分页获取角色信息信息
     *
     * @param page 分页实体
     * @return IPage
     */
    IPage<SysRole> getUserRole(@Param(value = "page") Page<SysRole> page,
                               @Param(value = "params") Map<String, Object> params,
                               @Param(value = "sort") String sort, @Param(value = "order") String order);

    List<SysRole> getPermissions(@Param(value = "user") SysUser user);

    List<Integer> getRoleUserList(int roleId);

    List<SysRole> getReviewList();

    List<SysRole> getAdminUser(@Param(value = "name") String name);

}
