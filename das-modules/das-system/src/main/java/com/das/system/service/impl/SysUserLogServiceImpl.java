package com.das.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.system.entity.SysUser;
import com.das.system.entity.SysUserLog;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.mapper.SysUserLogMapper;
import com.das.system.service.ISysUserLogService;
import com.das.utils.common.Constants;
import com.das.utils.common.SpringUtil;
import com.das.utils.tasks.AsyncManager;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.TimerTask;

/**
 * 用户记录以及统计管理业务
 *
 * <AUTHOR>
 * @since 2020-07-09
 */
@Service
public class SysUserLogServiceImpl extends ServiceImpl<SysUserLogMapper, SysUserLog> implements ISysUserLogService {

    @Autowired
    private SysUserLogMapper SysUserLogMapper;

    public IPage<SysUserLog> getSysUserLog(PageEntity pageEntity) {
        return SysUserLogMapper.getSysUserLog(new Page<>(pageEntity.getCurrent(), pageEntity.getSize()),
                pageEntity.getParams(), pageEntity.getSort(), pageEntity.getOrder());
    }


    public static void saveUserLog(SysUserLog param) {
        AsyncManager.me().executor(saveUserLog(param, (SysUser) SecurityUtils.getSubject().getSession().getAttribute(Constants.MANAGER_USER)));
    }

    public static TimerTask saveUserLog(SysUserLog userLog, SysUser user){
        return new TimerTask() {
            @Override
            public void run() {
                if (!StringUtils.isEmpty(user)) {
                    userLog.setUserGuid(user.getGuid());
                    userLog.setName(user.getName() + "(" + user.getUserName() + ")");
                }
                //userLog.setGuid(StringHandle.createUUID());
                ((ISysUserLogService) SpringUtil.getBean("sysUserLogServiceImpl")).save(userLog);
            }
        };
    }

}
