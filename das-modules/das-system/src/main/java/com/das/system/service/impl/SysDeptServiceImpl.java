package com.das.system.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.system.entity.SysDept;
import com.das.system.entity.SysUser;
import com.das.system.entity.entitywrap.DeptWrap;
import com.das.system.mapper.SysDeptMapper;
import com.das.system.service.ISysDeptService;
import com.das.utils.common.Constants;
import lombok.val;
import org.apache.shiro.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: SL
 * @create: 2020-08-24 09:22
 **/
@Service
@Transactional
public class SysDeptServiceImpl extends ServiceImpl<SysDeptMapper, SysDept> implements ISysDeptService {

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Override
    public List<DeptWrap> getDept() {
        return formatMenus(list());
    }

    @Override
    public boolean setDept(SysDept sysDept){
        val q = new QueryWrapper<SysDept>();
        q.eq("name",sysDept.getName());
        SysDept dept = sysDeptMapper.selectOne(q);
        if(dept != null){
            if(sysDept.getId() == null || (!sysDept.getId().equals(dept.getId()))){
                return false;
            }
        }else if(sysDept.getId()==null||"".equals(sysDept.getId().toString())){
            sysDept.setSortNo(sysDeptMapper.getMaxSort()+1);
        }
        return saveOrUpdate(sysDept);
    }

    @Override
    public List<SysDept> getDeptById(Long id) {
        return sysDeptMapper.getDeptById(id);
    }

    @Override
    public List<DeptWrap> getDeptUser() {
        return formatMenus(sysDeptMapper.getDeptUser());
    }

    private List<DeptWrap> formatMenus(List<SysDept> sysDepts) {
        List<DeptWrap> menuList = new ArrayList<>();
        for (SysDept sysDept : sysDepts) {
            // 一级菜单parentId为0
            if (sysDept.getParentId().equals(0L)) {
                menuList.add(new DeptWrap(sysDept));
            }
        }
        // 为一级菜单设置子菜单，getChild是递归调用的
        for (DeptWrap btn : menuList) {
            btn.setChildren(getChild(btn.getId(), sysDepts));
        }
        return menuList;
    }

    private List<DeptWrap> getChild(Long id, List<SysDept> rootDept) {
        // 子菜单
        List<DeptWrap> childList = new ArrayList<>();
        for (SysDept sysDept : rootDept) {
            // 遍历所有节点，将父菜单id与传过来的id比较
            if (sysDept.getParentId().equals(id)) {
                childList.add(new DeptWrap(sysDept));
            }
        }
        // 把子菜单的子菜单再循环一遍
        for (DeptWrap btn : childList) {
            btn.setChildren(getChild(btn.getId(), rootDept));
        }
        if (childList.size() == 0) {
            return null;
        }
        return childList;
    }
}
