package com.das.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.system.entity.SysResCheckLog;
import com.das.system.entity.SysUser;
import com.das.system.mapper.SysResCheckLogMapper;
import com.das.system.service.ISysResCheckLogService;
import com.das.utils.common.Constants;
import com.das.utils.common.DateUtil;
import org.apache.shiro.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * @author: Yj
 * @create: 2021-06-22 10:25
 **/
@Service
@Transactional
public class SysResCheckLogServiceImpl extends ServiceImpl<SysResCheckLogMapper, SysResCheckLog> implements ISysResCheckLogService {


    @Override
    public void saveCangpCheckLog(Long refId, Long checkFunction, String result, byte checkFlag) {
        SysUser user = (SysUser) SecurityUtils.getSubject().getSession().getAttribute(Constants.MANAGER_USER);
        //记录审核日志  待实现
        SysResCheckLog sysResCheckLog = new SysResCheckLog();
        //关联ID
        sysResCheckLog.setResId(refId);
        //流程type
        sysResCheckLog.setCheckFunction(checkFunction);
        //审批人
        sysResCheckLog.setCheckUser(user.getName());
        //审核结果信息
        sysResCheckLog.setCheckLog(result);
        //是否通过
        sysResCheckLog.setCheckFlag(checkFlag);
        //审批时间
        sysResCheckLog.setCheckTime(DateUtil.getDay());
        //更新时间
        sysResCheckLog.setUpdateTime(DateUtil.getDay());
        save(sysResCheckLog);
    }
}
