package com.das.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.system.entity.SysLoggingEvent;
import com.das.system.entity.entitywrap.PageEntity;

/**
 * <p>
 * 异常日志类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
public interface ISysLoggingEventService extends IService<SysLoggingEvent> {

    IPage<SysLoggingEvent> getLoggingEvent(PageEntity pageEntity);

}
