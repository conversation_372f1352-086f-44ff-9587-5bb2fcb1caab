package com.das.system.entity.entitywrap;

import lombok.Data;

import java.util.Map;

/**
 * Description TODO
 *
 * <AUTHOR>
 * @since 2020/4/27
 */
@Data
public class PageEntity {
    /** 当前页 */
    private int current;
    /** 分页数 */
    private int size;
    /** 排序字段 */
    private String sort;
    /** 正/倒序 */
    private String order;
    /** 查询参数集 */
    private Map<String, Object> params;
    public int getCurrent(){
        if (this.current == 0) {
            return 1;
        } else {
            return this.current;
        }
    }

    public void setParamsValue(String key, Object object){
        this.params.put(key, object);
    }

}
