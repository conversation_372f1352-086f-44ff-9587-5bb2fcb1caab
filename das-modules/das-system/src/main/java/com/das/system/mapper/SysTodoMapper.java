package com.das.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.system.entity.SysTodo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * Mapper 数字资源使用申请查询
 *
 * <AUTHOR>
 * @since 2020-07-27
 */
@Repository
public interface SysTodoMapper extends BaseMapper<SysTodo> {

    /**
     * 获取代办事项
     * @param page
     * @param params
     * @return
     */
    IPage<SysTodo> getSysTodoList(@Param(value = "page") Page<SysTodo> page,
                                  @Param(value = "params") Map<String, Object> params);


    int updateEnable(String resId);

}
