package com.das.system.controller;

import com.das.annotation.RequestSecurity;
import com.das.system.entity.SysCode;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.service.ISysCodeService;
import com.das.utils.response.ResultCode;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * 查询系统编码表
 *
 * <AUTHOR>
 * @since 2020-07-11
 */
@RestController
@RequestMapping("/sysCode")
@Api(value = "系统字典管理接口", description = "系统字典管理接口")
public class SysCodeController {

    @Resource
    private ISysCodeService iSysCodeService;

    @ApiOperation(value = "获取字典列表")
    @RequestSecurity(value = "/getSysCodeList", method = RequestMethod.POST)
    public ResultMsg getSysCodeList(@RequestBody SysCode sysCode) {
        return new ResultMsg(iSysCodeService.getSysCodeList(sysCode.getField()));
    }

    @ApiOperation(value = "分页获取字典列表")
    @RequiresPermissions(value = "/system/code", logical = Logical.OR)
    @RequestSecurity(value = "/getAll", method = RequestMethod.POST)
    public ResultMsg getAll(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(iSysCodeService.getAll(pageEntity));
    }


    @ApiOperation(value = "获取字典树结构数据")
    @RequestSecurity(value = "/getSysCodeTree",method = RequestMethod.GET)
    public ResultMsg getSysCodeTree() {
        return new ResultMsg(iSysCodeService.getSysCodeTree());
    }

    @ApiOperation(value = "根据id获取字典下的详情数据")
    @RequestSecurity(value = "/getCodeDetail", method = RequestMethod.POST)
    public ResultMsg getCodeDetail(@RequestBody SysCode sysCode) {
        return new ResultMsg(iSysCodeService.getCodeDetail(sysCode));
    }


    @ApiOperation(value = "新增or修改字典")
    @RequiresPermissions(value = "/system/code", logical = Logical.OR)
    @RequestSecurity(value = "/saveCode", method = RequestMethod.POST)
    public ResultMsg saveCode(@Valid @RequestBody SysCode sysCode) {
        boolean flag =iSysCodeService.saveCode(sysCode);
        return flag?new ResultMsg(flag):new ResultMsg(ResultCode.CODE_30001,  null);
    }

    @ApiOperation(value = "保存字典下的详情")
    @RequiresPermissions(value = "/system/code", logical = Logical.OR)
    @RequestSecurity(value = "/saveCodeDetail", method = RequestMethod.POST)
    public ResultMsg saveCodeDetail(@RequestBody SysCode sysCode) {
        boolean flag =iSysCodeService.saveCodeDetail(sysCode);
        return flag?new ResultMsg(flag):new ResultMsg(ResultCode.CODE_30001,  null);
    }

    @ApiOperation(value = "删除字典下的详情")
    @RequiresPermissions(value = "/system/code", logical = Logical.OR)
    @RequestSecurity(value = "/deleteCodeDetail", method = RequestMethod.POST)
    public ResultMsg deleteCodeDetail(@RequestBody SysCode sysCode) {
        return new ResultMsg(iSysCodeService.removeById(sysCode));
    }

    @ApiOperation(value = "删除字典")
    @RequiresPermissions(value = "/system/code", logical = Logical.OR)
    @RequestSecurity(value = "/deleteCode", method = RequestMethod.POST)
    public ResultMsg deleteCode(@RequestBody SysCode sysCode) {
        return new ResultMsg(iSysCodeService.deleteCode(sysCode));
    }

    @ApiOperation(value = "根据父节点查询其下的子节点数据")
    @RequestSecurity(value = "/getCodeById", method = RequestMethod.POST)
    public ResultMsg getCodeById(@RequestBody SysCode sysCode) {
        return new ResultMsg(iSysCodeService.getCodeById(sysCode));
    }

    @ApiOperation(value = "根据类型查询其下的子节点数据")
    @RequestSecurity(value = "/getCodeByField", method = RequestMethod.POST)
    public ResultMsg getCodeByField(@RequestBody SysCode sysCode) {
        return new ResultMsg(iSysCodeService.getCodeByField(sysCode));
    }

    @ApiOperation(value = "根据类型集合查询其下的子节点数据并返回树结构")
    @RequestSecurity(value = "/getCodeByMap", method = RequestMethod.POST)
    public ResultMsg getCodeByMap(@RequestBody Map<String,String> field) {
        return new ResultMsg(iSysCodeService.getCodeByMap(field));
    }

    @ApiOperation(value = "根据类型集合查询其下的子节点数据并返回map结构")
    @RequestSecurity(value = "/getCodeToMap",method = RequestMethod.POST)
    public ResultMsg getCodeToMap(@RequestBody Map<String,String> field){
        return new ResultMsg(iSysCodeService.getCodeToMap(field));
    }

}
