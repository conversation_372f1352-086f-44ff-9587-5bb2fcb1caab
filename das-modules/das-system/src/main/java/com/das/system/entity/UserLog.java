package com.das.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * 平台用户行为日志
 *
 * <AUTHOR>
 * @since 2020-07-09
 */
@Data
public class UserLog extends Model<UserLog> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户gid
     */
    private String guid;

    /**
     * 请求方法
     */
    private String method;

    /**
     * 请求ip
     */
    private String ip;

    /**
     * 请求参数
     */
    private String param;

    /**
     * 请求时间
     */
    private String datetime;



    /*==========================================================*/
    /**
     * 用户name
     */
    private String name;
    /**
     * 请求描述
     */
    private String info;

    /**
     * 请求模块(9.典型建筑模型数据中心;16:字典管理)
     */
    @TableField(exist = false)
    private Long methodId;

    /**
     * 类型（1：新增；2：更新；3：删除；4：撤销；5：发布；）
     */
    @TableField(exist = false)
    private Long types;

    /**
     * userGuid
     */
    @TableField(exist = false)
    private String userGuid;

    /**
     * newOld//新旧内容比较
     */
    @TableField(exist = false)
    private String newOld;

    /**
     * 描述
     */
    private String describes;

//    public UserLog(){
//
//    }


//    public UserLog(String guid, String method, String ip, String param, String datetime){
//        this.setGuid(guid);
//        this.setMethod(method);
//        this.setIp(ip);
//        this.setParam(param);
//        this.datetime = datetime;
//    }

}
