package com.das.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.system.entity.SysResCheck;
import com.das.system.entity.SysResCheckLog;
import com.das.system.entity.SysUser;
import com.das.system.mapper.SysResCheckLogMapper;
import com.das.system.mapper.SysResCheckMapper;
import com.das.system.service.ISysResCheckLogService;
import com.das.system.service.ISysResCheckService;
import com.das.utils.common.ResConstant;
import com.das.utils.common.SystemUtil;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: SL
 * @create: 2021-06-22 15:06
 **/
@Service
@Transactional
public class SysResCheckServiceImpl extends ServiceImpl<SysResCheckMapper, SysResCheck> implements ISysResCheckService {

    @Resource
    private SysResCheckMapper sysResCheckMapper;

    @Resource
    private ISysResCheckService sysResCheckService;

    @Resource
    private ISysResCheckLogService sysResCheckLogService;

    @Resource
    private SysResCheckLogMapper sysResCheckLogMapper;

    @Value("${server.port}")
    private String port;

    @Value("${server.host}")
    private String host;

    private String checkUrl="";

    @Override
    public boolean saveResCheck(SysResCheck sysResCheck) {
        String [] no = sysResCheck.getNo().split(",");
        List<SysResCheck> list = sysResCheckMapper.checkSave(sysResCheck.getType(),no);
        if(list.size() > 0){
            List<SysResCheck> lst = list.stream().filter(s->!s.getId().equals(sysResCheck.getId())).collect(Collectors.toList());
            if(sysResCheck.getId() == null || lst.size() > 0){
                return false;
            }
        }
        String headStr =StringUtils.rightPad("1", sysResCheck.getStore(),"1");
        String flag = StringUtils.rightPad(headStr, 12, "0");
        sysResCheck.setFlag(flag);
        //修改流程。将该审核流程初始化
        if(sysResCheck.getId() != null){
            insertCheckLogs(sysResCheck.getType());
        }
        return saveOrUpdate(sysResCheck);
    }

    @Override
    public List<SysResCheck> getResCheckList(SysResCheck sysResCheck) {
        return sysResCheckMapper.getResCheckList(sysResCheck);
    }

    @Override
    public boolean delResCheckList(SysResCheck sysResCheck) {
        val q = new QueryWrapper<SysResCheck>();
        q.eq("type", sysResCheck.getType()).gt("store", sysResCheck.getStore());
        List<SysResCheck> list = sysResCheckService.list(q);
        if(list.size() != 0){
            for(SysResCheck resCheck : list){
                resCheck.setStore(resCheck.getStore()-1);
                String headStr =resCheck.getFlag().substring(0,resCheck.getFlag().lastIndexOf("1"));
                String flag = StringUtils.rightPad(headStr, 12, "0");
                resCheck.setFlag(flag);
            }
            removeById(sysResCheck.getId());
            return saveOrUpdateBatch(list);
        }
        insertCheckLogs(sysResCheck.getType());
        return  removeById(sysResCheck.getId());

    }

    /**
     * 删除审核流程后，在审核日志中新增一条提示记录
     */
    private void insertCheckLogs(Long type) {
        ArrayList ls = new ArrayList();
        SysUser sysUser = (SysUser) SystemUtil.getLoginUser();
        //获取当前审核流下的审核日志
        List<SysResCheckLog> list1 = sysResCheckLogMapper.queryByType(type,ls);
        List<Long> lst =  list1.stream().map(ResConstant.CheckFlow_73 == type?SysResCheckLog::getCheckId:SysResCheckLog::getResId).collect(Collectors.toList());
        if(lst.size() == 0){
            return ;
        }
        //将申请表中的包含此流程的数据状态全部变成初审状态
        RestTemplate template = new RestTemplate();
        MultiValueMap<String,Object> paramMap = new LinkedMultiValueMap<String, Object>();
        paramMap.add("ids", lst);
        paramMap.add("type",type);
        String url = checkUrl +"/getAuditDigitRes";
        System.out.println("url: "+url);
        ResponseEntity<Map> responseEntity = template.postForEntity(url,paramMap, Map.class);
        //请求成功
        if(responseEntity.getStatusCodeValue() == 200){
            List list = (List)responseEntity.getBody().get("obj");
            List<SysResCheckLog> logList = new ArrayList<>();
            if(list.size() !=0){
                List<SysResCheckLog> lt = sysResCheckLogMapper.queryByType(type,list);
                for(SysResCheckLog sysResCheckLog:lt){
                    sysResCheckLog.setCheckUser(sysUser.getName());
                    sysResCheckLog.setCheckFlag((byte) 2);
                    sysResCheckLog.setCheckLog("审核流程已改变！");
                    logList.add(sysResCheckLog);
                }
                sysResCheckLogService.saveBatch(logList);
            }
        }
    }

    @Override
    public boolean isLsatOne(Long type, String flag) {
        int max = sysResCheckMapper.getStoreMaxByType(type);
        char[] chars = flag.toCharArray();
        int count = 0;
        for (char temp : chars) {
            if (temp=='1'){
                count ++;
            }
        }
        return max==count;
    }
}
