package com.das.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.system.entity.SysButton;
import com.das.system.entity.entitywrap.BtnWrap;
import com.das.system.entity.entitywrap.PageEntity;

import java.util.List;

/**
 * 菜单权限接口
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
public interface ISysButtonService extends IService<SysButton> {
    List<BtnWrap> getBtn(String userId, String menuId);

    IPage<SysButton> getBtnList(PageEntity pageEntity);
}
