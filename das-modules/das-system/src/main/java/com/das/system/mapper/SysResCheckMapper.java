package com.das.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.das.system.entity.SysResCheck;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: SL
 * @create: 2021-06-22 15:04
 **/
public interface SysResCheckMapper extends BaseMapper<SysResCheck> {

    List<SysResCheck> getResCheckList(SysResCheck sysResCheck);

    int getStoreMaxByType (Long type);

    List<SysResCheck> checkSave(@Param(value = "type") Long type, @Param(value = "no") String [] no);
}
