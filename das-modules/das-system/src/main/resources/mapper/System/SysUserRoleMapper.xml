<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://Mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.system.mapper.SysUserRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.system.entity.SysUserRole">
        <id column="user_id" property="user_id"/>
        <result column="role_id" property="role_id"/>
    </resultMap>

    <!-- 根据管理员角色ID批量添加权限-->
    <insert id="insertBatchPermissionsByRoleId">
        insert into
        sys_user_role(user_id, role_id)
        values
        <foreach collection="permission" item="role_id" separator=",">
            (#{user_id}, #{role_id})
        </foreach>
    </insert>

    <select id="getUserRoleInfo" resultType="java.util.HashMap" >
        select
        (select name from sys_user where id =u.user_id) username,
        (select name from sys_role where id =u.role_id) rolename
        From sys_user_role u
        where u.user_id=#{user_id}
    </select>


    <select id="getAllUser" resultType="java.util.HashMap" >
        select a.user_id,a.role_id,b.name From sys_user_role a,sys_role b
        where a.role_id =b.id
        order by user_id
    </select>

</mapper>
