<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://Mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.system.mapper.SysCodeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.system.entity.SysCode">
        <id column="id" property="id"/>
        <result column="field" property="field"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="description" property="description"/>
        <result column="enabled" property="enabled"/>
        <result column="editMode" property="edit_mode"/>
        <result column="sortNo" property="sort_no"/>
        <result column="remark" property="remark"/>
        <result column="parentId" property="parent_id"/>
        <result column="isLastChild" property="is_last_child"/>
    </resultMap>

    <!--    获取字典类型列表   -->
    <select id="getSysCodeType" resultType="com.das.system.entity.SysCode">
        select id, field from sys_code where field = #{field} and is_last_child = #{isLastChild}
    </select>

    <!--    获取字典类型列表   -->
    <select id="getSysCodeTree" resultType="com.das.system.entity.SysCode">
        select id,field ,name ,code ,description ,enabled ,edit_mode ,parent_id , sort_no ,remark,is_last_child from sys_code sdt where is_last_child = 0 order by id
    </select>

    <!--    根据类型获取字典列表List-->
    <select id="getSysCodeList" resultType="com.das.system.entity.SysCode">
        select id,field,name,code,description,edit_mode from sys_code where enabled = 1 and field = #{guid} order by sort_no
    </select>


    <select id="getCodeDetail" resultType="com.das.system.entity.SysCode">
        select id,field ,name ,code ,description ,enabled ,edit_mode ,parent_id , sort_no ,remark,is_last_child from sys_code sdt where id = #{id}
    </select>

    <select id="getMax" resultType="java.lang.Integer">
        select  ifnull(max(sort_no),0) from sys_code where parent_id = #{parentId}
    </select>

    <select id="getAllList" resultType="com.das.system.entity.SysCode">
        select id,field,name,code,edit_mode from sys_code
    </select>


    <select id="getAll" resultType="com.das.system.entity.SysCode">
        select a.id,a.field ,a.name ,a.code ,a.description ,a.enabled ,a.edit_mode ,a.parent_id , a.sort_no ,a.remark,a.is_last_child from sys_code a
        where 1=1
        <if test="params.id != '' and params.id !=null and params.id !='1000'">
            and  a.parent_id = #{params.id}
        </if>

        <if test="params.id != '' and params.id !=null and params.id =='1000'">
            and  a.is_last_child = 1
        </if>

        <if test="params.name !='' and params.name !=null">
            and description like CONCAT('%',#{params.name},'%')
        </if>
        order by a.${sort} ${order}
    </select>

    <select id="getAllById" resultType="com.das.system.entity.SysCode">
        SELECT
        t3.id,t3.field ,t3.name ,t3.code ,t3.description ,t3.enabled ,t3.edit_mode ,t3.parent_id , t3.sort_no ,t3.remark,t3.is_last_child
        FROM
        (
        SELECT
        *,
        IF
        ( find_in_set( t1.parent_id, @p ) > 0, @p := concat( @p, ',', id ), 0 ) AS childId
        FROM
        ( SELECT id,field ,name ,code ,description ,enabled ,edit_mode ,parent_id , sort_no ,remark,is_last_child FROM sys_code t  ORDER BY id ) t1,
        ( SELECT @p := #{params.id} ) t2
        ) t3
        WHERE
        t3.is_last_child =1
        and childId != 0
        <if test="params.name !='' and params.name !=null">
            and t3.description like CONCAT('%',#{params.name},'%')
        </if>
        order by t3.${sort} ${order}
    </select>

    <select id="getCodeById" resultType="com.das.system.entity.SysCode">
        SELECT
        t3.id,t3.field ,t3.name ,t3.code ,t3.description ,t3.enabled ,t3.edit_mode ,t3.parent_id , t3.sort_no ,t3.remark,t3.is_last_child
        FROM
        (
        SELECT
        *,
        IF
        ( find_in_set( t1.parent_id, @p ) > 0, @p := concat( @p, ',', id ), 0 ) AS childId
        FROM
        ( SELECT id,field ,name ,code ,description ,enabled ,edit_mode ,parent_id , sort_no ,remark,is_last_child FROM sys_code t  ORDER BY id ) t1,
        ( SELECT @p := #{parentId} ) t2
        ) t3
        WHERE
        childId != 0
        and enabled = 1
        union all
        select t3.id,t3.field ,t3.name ,t3.code ,t3.description ,t3.enabled ,t3.edit_mode ,0 as parent_id , t3.sort_no ,t3.remark,t3.is_last_child from sys_code t3
        where  id =#{parentId}
        order by id
    </select>

    <select id="getCodeByField" resultType="com.das.system.entity.SysCode">
        select id,field ,name ,code ,description ,enabled ,edit_mode ,parent_id ,sort_no ,remark,is_last_child from sys_code
        where is_last_child = 0 and field = #{field}
    </select>

    <update id="updateByField" parameterType="com.das.system.entity.SysCode">
        update sys_code
          set name = #{name} ,description = #{name}
        where field = #{field} and is_last_child = 0
    </update>


</mapper>
