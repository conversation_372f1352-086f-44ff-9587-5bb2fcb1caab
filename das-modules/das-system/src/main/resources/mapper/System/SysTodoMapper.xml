<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://Mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.system.mapper.SysTodoMapper">

    <select id="getSysTodoList" resultType="com.das.system.entity.SysTodo">
        select a.id,a.user_id,a.title,a.path,a.create_time,a.enabled,
        ifnull((select u.update_content From data_manager u where u.id=a.res_id and u.register=a.user_id),null) updateContent
        from sys_todo a where a.user_id = #{params.userId} and a.enabled = 0
          order by a.create_time desc
    </select>

    <update id="updateEnable" parameterType="java.lang.String">
        update sys_todo set enabled=1 where res_id = #{resId}
    </update>

</mapper>
