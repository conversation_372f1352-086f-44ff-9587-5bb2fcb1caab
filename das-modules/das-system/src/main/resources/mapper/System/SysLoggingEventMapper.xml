<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.system.mapper.SysLoggingEventMapper">

    <select id="getLoggingEvent" resultType="com.das.system.entity.SysLoggingEvent">
        select event_id,(from_unixtime(SUBSTR(timestmp, 1, length(timestmp)-3),'%Y-%m-%d %H:%i:%s')) as timestmp_str,formatted_message,logger_name,level_string,thread_name,reference_flag,arg0,arg1,arg2,arg3,caller_filename,caller_class,caller_method,caller_line
          from logging_event u where 1=1
          <if test="params.datetime!='' and params.datetime!=null">
              and from_unixtime(SUBSTR(timestmp, 1, length(timestmp)-3),'%Y-%m-%d')=DATE_FORMAT( #{params.datetime}, '%Y-%m-%d' )
          </if>
            order by u.${sort} ${order}
    </select>

</mapper>
