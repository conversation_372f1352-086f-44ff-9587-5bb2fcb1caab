<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://Mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.system.mapper.SysMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.system.entity.SysMenu">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="icon" property="icon"/>
        <result column="path" property="path"/>
        <result column="parent_id" property="parentId"/>
        <result column="level" property="level"/>
        <result column="sortNo" property="sort_no"/>
        <result column="status" property="status"/>
        <result column="is_lock" property="isLock"/>
    </resultMap>

    <select id="getUserMenus" resultType="com.das.system.entity.SysMenu">
        select id,name,icon,path,parent_id From sys_menu
          where id in (
                SELECT DISTINCT menu_id
                    FROM sys_role_menu a, sys_user_role b,sys_user c
                WHERE a.role_id = b.role_id and c.id=b.user_id and a.type=0 and c.guid=#{guid}
                )
        <!-- 菜单启用条件 -->
        and status = 0 order by sort_no
    </select>

    <select id="getAllMenus" resultType="com.das.system.entity.SysMenu">
        select id,name,icon,path,parent_id From sys_menu
        where 1=1
        <!-- 菜单启用条件 -->
        and status = 0 order by sort_no
    </select>

    <!--    获取用户权限-->
    <select id="getPermissionByGuid" resultType="java.lang.String">
        select sm.path
        from
        sys_menu sm
<!--        left join role_menu rm on rm.menu_id = sm.id-->
<!--        left join user u on u.role = rm.role_id-->
        where id in (
        SELECT DISTINCT menu_id FROM sys_role_menu a, sys_user_role b,sys_user c
        WHERE a.role_id = b.role_id and c.id=b.user_id AND c.guid = #{userGuid}
        )
    </select>

    <!-- 批量插入用户拥有的角色-->
    <select id="selectBatchPermissionsByRoleId" resultType="com.das.system.entity.SysMenu">
        select id,name,path,parent_id,level,sort,status from sys_menu
        where parent_id &lt;&gt; 0
        <foreach collection="permission" item="role_id" separator=",">
            (#{user_id}, #{role_id})
        </foreach>
    </select>

    <!-- 根据管理员角色ID批量添加权限-->
    <insert id="insertBatchMenuId">
        insert into
        sys_role_menu(role_id, menu_id,type)
        values
        <foreach collection="children" item="menu_id" separator=",">
            (#{role_id}, #{menu_id},'0')
        </foreach>
    </insert>

    <!-- 根据管理员角色ID批量添加权限-->
    <insert id="insertBatchMenuId_btn">
        insert into
        sys_role_menu_button(role_id, menu_id,botton)
        values
        <foreach collection="children" item="menu_id" separator=",">
            (#{role_id}, #{menu_id},'0')
        </foreach>
    </insert>

    <select id="getRoleMenu" resultType="java.lang.String">
        select id
        From sys_menu
        where id in (
            SELECT  menu_id
            FROM sys_role_menu a
            WHERE a.role_id =#{role_id}
        )
<!--        and (parent_id &lt;&gt; 0)-->
        and status = 0
        union all
        select concat(menu_id,LPAD(id,4,0)) id From sys_button where id in (
        select a.button_id From sys_role_menu_button a, sys_role_menu b
        where b.role_id=a.role_id
        and b.menu_id=a.menu_id
        and b.role_id=#{role_id}
        )
    </select>

    <select id="getMenu" resultType="com.das.system.entity.SysMenu">
        SELECT id,name,icon,path,parent_id,level,sort_no,status FROM sys_menu WHERE status = 0 and id != 43
        union all
        select concat(menu_id,LPAD(id,4,0)) id,name, icon,method path, menu_id parent_id,level,sort_no,enabled status from sys_button order by sort_no
    </select>
    <select id="getManagerMenu" resultType="com.das.system.entity.SysMenu">
        SELECT id,name,icon,path,parent_id,level,sort_no,status FROM sys_menu
        WHERE status = 0 and id = parent_id and id not in (35,37,38)
    </select>

    <select id="getCenterMeun" resultType="com.das.system.entity.SysMenu">
        select name ,path From sys_menu
        where id in ( SELECT  menu_id FROM sys_role_menu a WHERE a.role_id in ${type} )
        and parent_id = id
        and status = 0
        order by sort_no
    </select>

    <select id="getUserHomeMenus" resultType="com.das.system.entity.SysMenu">
        select id,name,icon,path,parent_id From sys_menu
        where id in (
        SELECT DISTINCT menu_id
        FROM sys_role_menu a, sys_user_role b,sys_user c
        WHERE a.role_id = b.role_id and c.id=b.user_id and a.type=0
        <if test="guid != null">
            and c.guid = #{guid}
        </if>
        )
        <!-- 菜单启用条件 -->
        and id > 42
<!--        <if test="guid == null">-->
<!--            and id != 47-->
<!--        </if>-->
        and status = 0 order by sort_no
    </select>

</mapper>
