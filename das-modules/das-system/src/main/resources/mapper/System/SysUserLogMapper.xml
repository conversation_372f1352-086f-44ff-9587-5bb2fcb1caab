<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://Mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.system.mapper.SysUserLogMapper">

    <select id="getSysUserLog" resultType="com.das.system.entity.SysUserLog">
        select id,method,ip,param,datetime,
        name,describes,describes,info
        from sys_user_log u where 1=1
        <if test="params.datetime!='' and params.datetime!=null">
           and DATE_FORMAT(u.datetime, '%Y-%m-%d' )=DATE_FORMAT( #{params.datetime}, '%Y-%m-%d' )
        </if>
          order by u.${sort} ${order}
    </select>


</mapper>
