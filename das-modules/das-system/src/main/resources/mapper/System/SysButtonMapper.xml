<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://Mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.system.mapper.SysButtonMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.system.entity.SysButton">
        <id column="id" property="id"/>
        <result column="method" property="method"/>
        <result column="name" property="name"/>
        <result column="menu_id" property="menuId"/>
        <result column="icon" property="icon"/>
        <result column="title" property="title"/>
        <result column="parent_id" property="parentId"/>
        <result column="level" property="level"/>
        <result column="sortNo" property="sort_no"/>
        <result column="enabled" property="enabled"/>
    </resultMap>

    <select id="getBtn" resultType="com.das.system.entity.SysButton">
            select b.id,b.name,b.icon,b.method,b.parent_id,b.menu_id
                From sys_role_menu a ,sys_button b,sys_user_role c
                where a.menu_id=b.id
                  and c.role_id=a.role_id
                  and a.type=1
                  and b.enabled=0
                  and c.user_id=#{userId}
                  and b.menu_id=#{menuId}
    </select>

    <select id="getPermissionById" resultType="com.das.system.entity.SysButton">
        select b.method,b.menu_id
        From sys_role_menu a ,sys_button b, sys_role_menu_button d
        where  a.role_id=d.role_id
        and a.menu_id=d.menu_id
        and b.id=d.button_id
        and b.enabled=0
        and a.role_id in (select role_id from sys_user_role  where user_id=#{id})
    </select>

    <select id="getBtnList" resultType="com.das.system.entity.SysButton">
        select a.id,a.method,a.name,a.menu_id,a.icon,a.title,a.parent_id,a.level,a.sort_no,a.enabled  from sys_button a
        where 1=1
        <if test="params.menuId != ''">
            and menu_id = #{params.menuId}
        </if>
        <if test="params.name != ''">
            and name like CONCAT('%',#{params.name},'%')
        </if>
        order by a.${sort} ${order}
    </select>

</mapper>
