<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/Mybatis-3-mapper.dtd">
<mapper namespace="com.das.system.mapper.SysRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.system.entity.SysRole">
        <id column="id" property="id"/>
        <result column="Name" property="name"/>
        <result column="type" property="type"/>
        <result column="role_info" property="roleInfo"/>
        <result column="sort_no" property="sortNo"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 获取角色权限-->
    <select id="getRole" resultType="com.das.system.entity.SysRole">
        select a.id,a.type ,a.name ,a.role_info,a.sort_no,a.create_time from sys_role a where type != 0
    </select>

    <!-- 获取角色信息 -->
    <select id="getUserRole" resultType="com.das.system.entity.SysRole">
        select a.id,a.name,a.type ,a.role_info,a.sort_no,a.create_time from sys_role a
        where 1=1 and type != 0
        <if test="params.name != null">
            and a.name like CONCAT('%',#{params.name},'%')
        </if>

        <if test="params.type != null and params.type != -1">
            and a.type = #{params.type}
        </if>

        order by a.${sort} ${order}
    </select>

    <!-- 获取角色信息 -->
    <select id="getPermissions" resultType="com.das.system.entity.SysRole">
        select a.id,a.name,a.type ,a.role_info,a.sort_no,a.create_time
        from sys_role a ,sys_user_role b
        where a.id=b.role_id
          and b.user_id =  #{user.id}

    </select>

    <!-- 获取角色下的用户 -->
    <select id="getRoleUserList" resultType="java.lang.Integer">
        select user_id from sys_user_role a where role_id = #{roleId}
    </select>

    <!-- 获取角色 -->
    <select id="getReviewList" resultType="com.das.system.entity.SysRole">
        select * from sys_role a where type != 0
    </select>

    <!-- 获取管理员 -->
    <select id="getAdminUser" resultType="com.das.system.entity.SysRole">
        SELECT
        *
        FROM
        ( SELECT DISTINCT user_id AS id FROM sys_user_role WHERE role_id IN ( SELECT id FROM sys_role WHERE type != 2 ) ) t
        LEFT JOIN sys_user a ON t.id = a.id
        WHERE
        a.`status` = 0
        <if test="name != '' and name !=null">
          and  a.name like CONCAT('%',#{name},'%')
        </if>
    </select>


</mapper>
