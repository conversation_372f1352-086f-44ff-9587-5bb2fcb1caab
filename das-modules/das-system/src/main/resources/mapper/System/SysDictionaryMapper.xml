<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://Mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.system.mapper.SysDictionaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.system.entity.SysDictionary">
        <id column="id" property="id"/>
        <result column="value" property="value"/>
        <result column="type" property="type"/>
        <result column="parent_id" property="parentId"/>
        <result column="number" property="number"/>
        <result column="sort" property="sort"/>
        <result column="sort" property="sort"/>
        <result column="level" property="level"/>
    </resultMap>

    <!--    获取字典类型列表-->
    <select id="getDictionaryType" resultType="com.das.system.entity.SysDictionary">
        select sdt.id, sdt.name value, sdt.type from sys_dictionary_type sdt
        order by sdt.sort asc
    </select>

    <!--    分页获取不同类型字典信息-->
    <select id="getDictionaryInfos" resultType="com.das.system.entity.SysDictionary">
        select sdc.id, sdc.value, sdc.type, sdc.number, sdc.parent_id, sdc.sort, sdc.level,
        sd.value parent
        from sys_dictionary sdc
        left join sys_dictionary sd on sd.id = sdc.parent_id
        where sdc.value is not null
        <if test='params.type!=null and params.type!=""'>
            and sdc.type = #{params.type}
        </if>

        <if test='params.parentId!=null and params.parentId!="-1"'>
            and sdc.parent_id = #{params.parentId}
        </if>

        <if test='params.name!=null and params.name!=""'>
            and sdc.value like CONCAT('%',#{params.name},'%')
        </if>
        order by
        sd.sort asc,
        sdc.sort asc
    </select>

    <!--    根据类型获取字典列表List-->
    <select id="getDictionary" resultType="com.das.system.entity.SysDictionary">
        select sdc.id, sdc.value, sdc.type, sdc.parent_id, sdc.sort
        from sys_dictionary sdc
        where sdc.value is not null
        <if test="dictionary.type != null">
            and sdc.type = #{dictionary.type}
        </if>
        <if test="dictionary.parentId != null">
            and sdc.parent_id = #{dictionary.parentId}
        </if>
        order by sdc.sort asc
    </select>

    <!--获取未新增资源的展品信息列表-->
    <select id="getSpotListRemoval" resultType="com.das.system.entity.SysDictionary">
        select sdc.id, sdc.value, sdc.type, sdc.parent_id, sdc.sort
        from sys_dictionary sdc
        where sdc.value is not null and sdc.type="place" and sdc.id not in(select s.number from scenic_information s)
    </select>

    <select id="getSpotList" resultType="com.das.system.entity.SysDictionary">
        SELECT d.id, d.`value`,d.type ,d.parent_id from sys_dictionary d where d.type ="place" or d.type ="floor"
    </select>

    <select id="getAttribute" resultType="com.das.system.entity.SysDictionary">
        SELECT d.id, d.`value`,d.type ,d.parent_id from sys_dictionary d where d.type ="exhibit"
    </select>

    <select id="getPlace" resultType="com.das.system.entity.SysDictionary">
        SELECT d.id, d.`value`,d.type ,d.parent_id from sys_dictionary d where d.value =#{place}
    </select>

    <select id="getBackList" resultType="com.das.system.entity.SysDictionary">
        SELECT d.id, d.`value`,d.type ,d.parent_id from sys_dictionary d where d.type ="problem" ORDER BY d.sort
    </select>

    <select id="getFeedList" resultType="com.das.system.entity.SysDictionary">
        SELECT d.id, d.`value`,d.type ,d.parent_id from sys_dictionary d where d.type ="question" ORDER BY d.sort
    </select>


</mapper>
