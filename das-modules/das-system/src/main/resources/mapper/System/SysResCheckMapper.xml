<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://Mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.system.mapper.SysResCheckMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.system.entity.SysResCheck">
        <id column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="name" property="name"/>
        <result column="store" property="store"/>
        <result column="role" property="role"/>
        <result column="no" property="no"/>
        <result column="flag" property="flag"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="getResCheckList" resultType="com.das.system.entity.SysResCheck">
        select  a.id ,a.type ,a.name ,a.store , a.role ,a.no ,a.flag ,a.create_time ,a.update_time
           , (select GROUP_CONCAT(name) from sys_user where `status` = 0 and id &lt;&gt; 1 and FIND_IN_SET(id,a.no)) userName
        from sys_res_check a
        where 1=1
        <if test="type != '' and type !=null">
            and  a.type = #{type}
        </if>
        <if test="no != '' and no !=null and no != 1">
            and  FIND_IN_SET(#{no},a.no)
        </if>
        order by a.store
    </select>

    <select id="getStoreMaxByType" resultType="int" >
        select ifNull(max(store),0) from sys_res_check where type = #{type}
    </select>

    <select id="checkSave" resultType="com.das.system.entity.SysResCheck">
        select * from sys_res_check where type = #{type}
        and
        <foreach collection="no" index="index" item="id" open="(" close=")">
            <choose>
                <when test="index != (no.length - 1)">
                    FIND_IN_SET(#{id},no) or
                </when>
                <otherwise>
                    FIND_IN_SET(#{id},no)
                </otherwise>
            </choose>
        </foreach>
    </select>

</mapper>
