<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://Mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.system.mapper.SysTaskConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.system.entity.SysTaskConfig">
        <id column="id" property="id"/>
        <result column="back_id" property="backId"/>
        <result column="type" property="type"/>
        <result column="frequency" property="frequency"/>
        <result column="date" property="date"/>
        <result column="time" property="time"/>
        <result column="isClose" property="isClose"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 删除定时任务 -->
    <delete id="deleteByBackId">
        DELETE FROM sys_task_config r WHERE r.back_id = #{id}
    </delete>

    <!-- 获取定时任务 -->
    <select id="getTaskConfigById" resultType="com.das.system.entity.SysTaskConfig">
        select * from sys_task_config n where n.back_id =#{id}
    </select>

</mapper>
