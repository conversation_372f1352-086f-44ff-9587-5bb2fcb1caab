<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://Mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.system.mapper.SysBackupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.system.entity.SysBackup">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="is_local_address" property="isLocalAddress"/>
        <result column="address" property="address"/>
        <result column="type" property="type"/>
        <result column="backup_type" property="backupType"/>
        <result column="backup_time" property="backupTime"/>
    </resultMap>
    <!--获取数据库备份列表信息-->
    <select id="getList" resultType="com.das.system.entity.SysBackup">
        select b.id,b.name, b.is_local_address, b.address,b.backup_type,b.type,b.backup_time
        from sys_backup b
        where 1=1
        <if test="params!=null and params.name != null">
            and b.name like CONCAT('%',#{params.name},'%')
        </if>
        <if test="params!=null and params.isLocalAddress != null">
            and b.is_local_address like CONCAT('%',#{params.isLocalAddress},'%')
        </if>
        <if test="params!=null and params.backupType != null and params.backupType != '-1'">
            and b.backup_type like CONCAT('%',#{params.backupType},'%')
        </if>
        <if test="params!=null and params.address != null">
            and b.address like CONCAT('%',#{params.address},'%')
        </if>
        <if test="params!=null and params.startTime != null">
            <![CDATA[   and DATE_FORMAT(b.backup_time, '%Y-%m-%d')>=  DATE_FORMAT(#{params.startTime}, '%Y-%m-%d')   ]]>
        </if>
        <if test="params!=null and params.endTime != null">
            <![CDATA[  and DATE_FORMAT(b.backup_time, '%Y-%m-%d') <= DATE_FORMAT(#{params.endTime}, '%Y-%m-%d')    ]]>
        </if>
        <if test="sort!=null and order != null">
            order by b.${sort} ${order}
        </if>
    </select>

</mapper>
