<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://Mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.system.mapper.SysUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.system.entity.SysUser">
        <id column="id" property="id"/>
        <result column="guid" property="guid"/>
        <result column="name" property="name"/>
        <result column="username" property="user_name"/>
        <result column="headImg" property="head_img"/>
        <result column="password" property="password"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="sex" property="sex"/>
        <result column="status" property="status"/>
        <result column="dept_id" property="deptId"/>
        <result column="duties" property="duties"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!--    获取登录用户详细信息-->
    <select id="loginByUserName" resultType="com.das.system.entity.SysUser">
        select u.id,u.guid, u.name, u.user_name, u.head_img, u.phone, u.email, u.sex, u.status, u.password,u.dept_id,
        u.duties,u.create_time
        from sys_user u where u.status!=2 and u.user_name = #{userName}
    </select>

    <!--分页获取平台用户信息-->
    <select id="getAllUserByRole" resultType="com.das.system.entity.SysUser">
        select * from (
        select u.id, u.guid, u.name, u.user_name, u.head_img, u.phone, u.email, u.sex, u.status, u.create_time,
        u.dept_id, u.duties,
        (select name from sys_dept where id=u.dept_id) deptName
        from sys_user u where u.user_name is not null
        <if test="params.name != null">
            and u.name like CONCAT('%',#{params.name},'%')
        </if>
        <if test="params.username != null">
            and u.user_name like CONCAT('%',#{params.username},'%')
        </if>
        <if test="params.status !=null and params.status != -1">
            and u.status = #{params.status}
        </if>
        ) b where b.status!=2
         and b.id != 1
        order by b.${sort} ${order}
    </select>

    <!--检查账号是否存在-->
    <select id="checkByUserName" resultType="com.das.system.entity.SysUser">
        select
        u.id, u.name, u.user_name
        from sys_user u
        where u.user_name = #{userName}
        <if test="id !=null">
            and u.id != #{id}
        </if>
    </select>

    <!--检查用户名是否存在-->
    <select id="checkByName" resultType="com.das.system.entity.SysUser">
        select
        u.id, u.name, u.user_name
        from sys_user u
        where u.user_name = #{name}
        <if test="id !=null">
            and u.id != #{id}
        </if>
    </select>

    <!--检查手机号是否存在-->
    <select id="checkByPhone" resultType="com.das.system.entity.SysUser">
        select
        u.id, u.name, u.user_name
        from sys_user u
        where u.phone = #{phone}
        <if test="id !=null">
            and u.id != #{id}
        </if>
    </select>
</mapper>
