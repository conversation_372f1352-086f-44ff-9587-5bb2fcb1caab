<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://Mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.system.mapper.SysResCheckLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.system.entity.SysResCheckLog">
        <result column="id" property="id"/>
        <result column="check_id" property="checkId"/>
        <result column="res_id" property="resId"/>
        <result column="check_function" property="checkFunction"/>
        <result column="check_user" property="checkUser"/>
        <result column="check_result" property="checkResult"/>
        <result column="check_flag" property="checkFlag"/>
        <result column="check_log" property="checkLog"/>
        <result column="check_time" property="checkTime"/>
        <result column="user_id" property="userId"/>
        <result column="enabled" property="enabled"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="queryByType" resultType="com.das.system.entity.SysResCheckLog">
        select DISTINCT check_id, res_id, check_function, user_id from sys_res_check_log where check_function = #{type}

        <if test="checkId != null and checkId.size() >0 ">
            and  check_id in
            <foreach collection="checkId" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>


    </select>

</mapper>
