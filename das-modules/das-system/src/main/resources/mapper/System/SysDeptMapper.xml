<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://Mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.system.mapper.SysDeptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.system.entity.SysDept">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="parent_id" property="parentId"/>
        <result column="type" property="type"/>
        <result column="sort_no" property="sortNo"/>
        <result column="create_time" property="createTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <select id="getMaxSort" resultType="java.lang.Integer">
        select max(sort_no) from sys_dept
    </select>

    <select id="getDeptById" resultType="com.das.system.entity.SysDept">
        select id,name from  sys_dept where id = (select dept_id from user where id = #{id})
    </select>

    <select id="getDeptUser" resultType="com.das.system.entity.SysDept">
        select id,name,parent_id, '0' as type From sys_dept a
        UNION all
        select id,name,dept_id as parent_id ,'1' as type from sys_user b
        where b.`status` = 0 and b.id != 1
        order by parent_id
    </select>

</mapper>
