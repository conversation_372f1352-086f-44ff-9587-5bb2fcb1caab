

# 大势智慧文博部后台服务框架  2021.6.18  Yangj
# 结构信息
das-server
   ├── das-doc       			# 其他资料（sql、word、excel...）
   ├── das-ui         			# 前端UI（预留集成）
   ├── das-api         		    # 接口模块（预留对第三方访问）
   ├── das-controller     		# 服务入口（已将各个控制器分散在各自业务中）
   ├── das-common         		# 通用模块
   │	    └── das-common-annotation                       	# 自定义注解
   │	    └── das-common-aspect                           	# 自定义切面
   │        └── das-common-config                            	# 配置信息
   │	    └── das-common-es                               	# ES搜索引擎
   │	    └── das-common-register                         	# 插件注册
   │	    └── das-common-shiros                            	# 权限控制
   ├── das-utils         		# 工具模块
   ├── das-modules         	    # 基础模块
   │      └── das-system                              		# 系统管理基础模块
   │      └── das-gen                                 		# 代码生成
   │      └── das-job                                 		# 定时任务
   ├── das-business            	# 业务模块（所有业务代码在此编写）
   │      └── das-cangp                            	  	    # 藏品管理
   │      └── das-digit                              		# 数字资源
   └── pom.xml                	# 公共依赖


注意事项：
    1. 所有包版本控制全都在公共依赖（顶级父节点）中配置GAV来管理，子模块只引用父模块（GA）依赖
    2. 各个依赖不要进行交叉依赖否则产生类似于循环依赖问题导致打包出错
    3. 所有的业务模块均在das-business中进行完成
    4. 在需要封装功能插件时不能依赖其他模块，做到单组件可独立引用运行
    5. 封装das-utils模块时不要将业务属性带入工具类中，比如导出excel时，只用传入导出列信息和导出数据data内容
    6. ......
