package com.das.es.service;

import com.das.es.entity.ExhibitionInfo;
import com.das.es.entity.responseEntity.EsDigitPage;
import com.das.utils.response.PageEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-07-30
 */

public interface IExhibitionService {
    EsDigitPage<ExhibitionInfo> findByPageForExhibition(PageEntity pageEntity);

    void save(ExhibitionInfo exhibitionInfo);

    void delete(Long id);

    ExhibitionInfo get(Long id);

    void saveAll(List<ExhibitionInfo> allExhibitionInfo);

    boolean deleteAll();
}
