package com.das.es.service.impl;

import com.das.es.dao.ExhibitionRepository;
import com.das.es.entity.ExhibitionInfo;
import com.das.es.entity.responseEntity.EsDigitPage;
import com.das.es.entity.responseEntity.SysCodeAndCount;
import com.das.es.service.IExhibitionService;
import com.das.es.utils.Utils;
import com.das.system.entity.SysCode;
import com.das.system.service.ISysCodeService;
import com.das.utils.common.CangPConstant;
import com.das.utils.response.PageEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021-07-30
 */
@Slf4j
@Service
public class ExhibitionServiceImpl implements IExhibitionService {

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Autowired
    private ExhibitionRepository exhibitionRepository;

    @Resource
    private ISysCodeService sysCodeService;

    @Override
    public EsDigitPage<ExhibitionInfo> findByPageForExhibition(PageEntity pageEntity) {
        NativeSearchQueryBuilder groupBuild = new NativeSearchQueryBuilder();

        TermsAggregationBuilder typeAgg = AggregationBuilders.terms("group_type").field("type");
        groupBuild.addAggregation(typeAgg);

        Pageable pageable = PageRequest.of(pageEntity.getCurrent()-1,pageEntity.getSize());
        String keyword = pageEntity.getParams().get("keyword").toString();
        String type = pageEntity.getParams().get("type").toString();

        SearchHits<ExhibitionInfo> countSearch =null;
        if (StringUtils.isBlank(keyword)){
            countSearch = elasticsearchRestTemplate.search(groupBuild.build(), ExhibitionInfo.class);
        }else {
            countSearch = elasticsearchRestTemplate.search(groupBuild.withQuery(QueryBuilders.queryStringQuery(keyword)).build(), ExhibitionInfo.class);
        }

        NativeSearchQueryBuilder build = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isBlank(keyword)){
            build.withSort(SortBuilders.fieldSort("createTime.keyword").order(SortOrder.DESC));
        }
        if (StringUtils.isBlank(type)&&StringUtils.isBlank(keyword)){
            QueryBuilder matchAll = QueryBuilders.matchAllQuery();
            boolQueryBuilder.must(matchAll);
        }else {
            if (StringUtils.isNotBlank(keyword)){
                QueryBuilder queryStringQueryBuilder = QueryBuilders.queryStringQuery(keyword);
                boolQueryBuilder.must(queryStringQueryBuilder);
            }
            if (StringUtils.isNotBlank(type)){
                if ("-1".equals(type)){
                    boolQueryBuilder.mustNot(QueryBuilders.existsQuery("type"));
                }else {
                    QueryBuilder typeQuery = QueryBuilders.matchQuery("type", Integer.parseInt(type));
                    boolQueryBuilder.must(typeQuery);
                }
            }
        }

        NativeSearchQuery searchQuery = build.withQuery(boolQueryBuilder).withPageable(pageable).build();
        log.info(searchQuery.toString());
        SearchHits<ExhibitionInfo> result = elasticsearchRestTemplate.search(searchQuery, ExhibitionInfo.class);
        // 存入数据
        List<ExhibitionInfo> list = new ArrayList<>();
        for (SearchHit<ExhibitionInfo> exhibitionInfoHit : result) {
            ExhibitionInfo content = exhibitionInfoHit.getContent();
            list.add(content);
        }
        EsDigitPage<ExhibitionInfo> resultPage = new EsDigitPage<>();
        resultPage.setRecord(list);
        resultPage.setTotal(result.getTotalHits());
        resultPage.setCurrent(pageEntity.getCurrent());
        resultPage.setSize(pageEntity.getSize());

        Terms term = countSearch.getAggregations().get("group_type");
        List<SysCode> sysCodeList = sysCodeService.getByFiledAndIsChild(CangPConstant.EXHIBITION_TYPE,1);
        List<SysCodeAndCount> typeList = Utils.typeCount(sysCodeList, term, countSearch.getTotalHits());
        resultPage.setTypeCount(typeList);
        return resultPage;
    }

    @Override
    public void save(ExhibitionInfo exhibitionInfo) {
        exhibitionRepository.save(exhibitionInfo);
    }

    @Override
    public void delete(Long id) {
        exhibitionRepository.deleteById(id);
    }

    @Override
    public ExhibitionInfo get(Long id) {
        Optional<ExhibitionInfo> optional = exhibitionRepository.findById(id);
        if (optional.isPresent()){
            return optional.get();
        }
        return null;
    }

    @Override
    public void saveAll(List<ExhibitionInfo> allExhibitionInfo) {
        exhibitionRepository.saveAll(allExhibitionInfo);
    }

    @Override
    public boolean deleteAll() {
        elasticsearchRestTemplate.deleteIndex(ExhibitionInfo.class);
        return true;
    }
}
