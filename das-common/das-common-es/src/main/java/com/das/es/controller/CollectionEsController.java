package com.das.es.controller;

import com.das.annotation.RequestSecurity;
import com.das.es.entity.CollectionInfo;
import com.das.es.entity.requestEntity.SearchEntity;
import com.das.es.service.ICollectionService;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultMsg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2021-07-30
 */

@RestController
@RequestMapping("/esCollection")
public class CollectionEsController {

    @Autowired
    private ICollectionService collectionService;

    @RequestSecurity(value = "/edit",method = RequestMethod.POST)
    public void edit(@RequestBody CollectionInfo collectionInfo) {
        collectionService.save(collectionInfo);
    }

    @RequestSecurity(value = "/get",method = RequestMethod.POST)
    public ResultMsg get(@RequestParam Long id) {
        return new ResultMsg(collectionService.get(id)) ;
    }

    @RequestSecurity(value = "/findByPage",method = RequestMethod.POST)
    public ResultMsg find(@RequestBody SearchEntity searchEntity) {
        return new ResultMsg(collectionService.findByPage(searchEntity));
    }

    @RequestSecurity(value = "/deleteAll",method = RequestMethod.POST)
    public ResultMsg delete() {
        return new ResultMsg(collectionService.deleteAll()) ;
    }

    @RequestSecurity(value = "/findByPageForCollection",method = RequestMethod.POST)
    public ResultMsg findByPageForCollection(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(collectionService.findByPageForCollection(pageEntity)) ;
    }
}
