package com.das.es.service;

import com.das.es.dao.EsEntityRepository;
import com.das.es.entity.EsEntity;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/5/26
 */
@Service
@Slf4j
public class EsService {

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Resource
    private EsEntityRepository esEntityRepository;

    /**
     * 索引名称
     */
    private final String index_name = "cj_ruins_info";

    public List<EsEntity> searchList(String keyword, String description) {
        boolean exists = elasticsearchRestTemplate.indexOps(EsEntity.class).exists();
        // 如果索引不存在,则创建
        if (!exists) {
            elasticsearchRestTemplate.indexOps(EsEntity.class).create();
            Document mappings = elasticsearchRestTemplate.indexOps(EsEntity.class).createMapping();
            elasticsearchRestTemplate.indexOps(EsEntity.class).putMapping(mappings);
        }
        List<EsEntity> list = new ArrayList<>();

        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilders.boolQuery()
                        .must(QueryBuilders.multiMatchQuery(keyword,
                                "ruinsName", /*"category",*/ "city",
                                "county", "address", /*"longitude",*/
                                /*"latitude",*//*"altitude",*/ "level",
                                /*"acreage",*/"age", "owner",
                                "subjection", "monomerDescription", "description",
                                "preserveStatus", "statusDescription", "naturalReason",
                                "artificialReason", "reasonDescription", "environment",
                                "humanity", "relicsNames", "materialNames", "materialTypes",
                                "materialFileNames", "digitNames", "digitTypes"))
                        .must(QueryBuilders.matchPhraseQuery("category", description))
                ).build();

        log.info(query.getQuery().toString());

        SearchHits<EsEntity> searchHits = elasticsearchRestTemplate.search(query, EsEntity.class);
        for (SearchHit<EsEntity> ruinsInfoSearchHit : searchHits) {
            EsEntity ruinsInfo = ruinsInfoSearchHit.getContent();
            list.add(ruinsInfo);
        }
        return list;
    }

    BulkOptions options = BulkOptions.builder().withTimeout(TimeValue.timeValueMinutes(2)).build();


    /**
     * 批量入es
     *
     * @param esEntities
     */
    public void batchSave(List<EsEntity> esEntities) {
        long start = System.currentTimeMillis();
        List<IndexQuery> queries = new ArrayList<>();
        int count = 0;
        for (EsEntity esEntity : esEntities) {
            IndexQuery indexQuery = new IndexQuery();
            indexQuery.setId(esEntity.getId());
            indexQuery.setObject(esEntity);
            queries.add(indexQuery);
            if (count % 100 == 0) {
                elasticsearchRestTemplate.bulkIndex(queries, options, IndexCoordinates.of(index_name));
                queries.clear();
            }
            count++;
        }

        if (queries.size() > 0) {
            elasticsearchRestTemplate.bulkIndex(queries, options, IndexCoordinates.of(index_name));
            queries.clear();
        }

        elasticsearchRestTemplate.indexOps(IndexCoordinates.of(index_name)).refresh();
        log.info("本次入es耗时：" + (System.currentTimeMillis() - start) + "毫秒");
        ;
    }


    /**
     * 插入es
     *
     * @param entity
     */
    public void saveEs(EsEntity entity) {
        boolean exists = elasticsearchRestTemplate.indexOps(EsEntity.class).exists();
        // 如果索引不存在,则创建
        if (!exists) {
            elasticsearchRestTemplate.indexOps(EsEntity.class).create();
            Document mappings = elasticsearchRestTemplate.indexOps(EsEntity.class).createMapping();
            elasticsearchRestTemplate.indexOps(EsEntity.class).putMapping(mappings);
        }
        //查询es中是否存在
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilders.boolQuery()
                        .must(QueryBuilders.matchQuery("tid", entity.getTid()))
                )
                .build();
        SearchHits<EsEntity> searchHits = elasticsearchRestTemplate.search(query, EsEntity.class);
        if (searchHits.getTotalHits() > 0) {
            for (SearchHit<EsEntity> searchHit : searchHits) {
                EsEntity esEntity = searchHit.getContent();
                Document document = Document.create();
                document.put("ruinsName", entity.getRuinsName());
                document.put("category", entity.getCategory());
                document.put("city", entity.getCity());
                document.put("county", entity.getCounty());
                document.put("address", entity.getAddress());
                document.put("longitude", entity.getLongitude());
                document.put("latitude", entity.getLatitude());
                document.put("altitude", entity.getAltitude());
                document.put("level", entity.getLevel());
                document.put("acreage", entity.getAcreage());
                document.put("age", entity.getAge());
                document.put("owner", entity.getOwner());
                document.put("subjection", entity.getSubjection());
                document.put("monomerDescription", entity.getMonomerDescription());
                document.put("description", entity.getDescription());
                document.put("preserveStatus", entity.getPreserveStatus());
                document.put("statusDescription", entity.getStatusDescription());
                document.put("naturalReason", entity.getNaturalReason());
                document.put("artificialReason", entity.getArtificialReason());
                document.put("reasonDescription", entity.getReasonDescription());
                document.put("environment", entity.getEnvironment());
                document.put("humanity", entity.getHumanity());
                elasticsearchRestTemplate.update(UpdateQuery.builder(esEntity.getId())
                        .withDocument(document).build(), IndexCoordinates.of("cj_ruins_info"));
            }
        } else {
            EsEntity es = new EsEntity();
            es.setTid(entity.getTid())
                    .setRuinsName(entity.getRuinsName())
                    .setCategory(entity.getCategory())
                    .setCity(entity.getCity())
                    .setCounty(entity.getCounty())
                    .setAddress(entity.getAddress())
                    .setLongitude(entity.getLongitude())
                    .setLatitude(entity.getLatitude())
                    .setAltitude(entity.getAltitude())
                    .setLevel(entity.getLevel())
                    .setAcreage(entity.getAcreage())
                    .setAge(entity.getAge())
                    .setOwner(entity.getOwner())
                    .setSubjection(entity.getSubjection())
                    .setMonomerDescription(entity.getMonomerDescription())
                    .setDescription(entity.getDescription())
                    .setPreserveStatus(entity.getPreserveStatus())
                    .setStatusDescription(entity.getStatusDescription())
                    .setNaturalReason(entity.getNaturalReason())
                    .setArtificialReason(entity.getArtificialReason())
                    .setReasonDescription(entity.getReasonDescription())
                    .setEnvironment(entity.getEnvironment())
                    .setHumanity(entity.getHumanity())
                    .setResourceId(entity.getResourceId());
            log.info("插入es成功");
            esEntityRepository.save(es);
        }
    }

    /**
     * 删除es中的数据
     *
     * @param id
     */
    public void delEs(Long id) {
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilders.boolQuery()
                        .must(QueryBuilders.matchQuery("tid", id))
                )
                .build();
        log.info("删除es成功");
        elasticsearchRestTemplate.delete(query, EsEntity.class);
    }
}
