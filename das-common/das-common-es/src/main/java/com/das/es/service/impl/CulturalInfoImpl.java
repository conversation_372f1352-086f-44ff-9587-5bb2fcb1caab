package com.das.es.service.impl;

import com.das.es.dao.CulturalRepository;
import com.das.es.entity.CulturalInfo;
import com.das.es.entity.responseEntity.EsDigitPage;
import com.das.es.service.ICulturalService;
import com.das.utils.response.PageEntity;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021-07-30
 */
@Service
public class CulturalInfoImpl implements ICulturalService {
    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Autowired
    private CulturalRepository culturalRepository;

    @Override
    public EsDigitPage<CulturalInfo> findByPageForCultural(PageEntity pageEntity) {
        Pageable pageable = PageRequest.of(pageEntity.getCurrent()-1,pageEntity.getSize());
        NativeSearchQueryBuilder build = new NativeSearchQueryBuilder();
        String keyword = pageEntity.getParams().get("keyword").toString();
        if (StringUtils.isBlank(keyword)){
            build.withSort(SortBuilders.fieldSort("createTime.keyword").order(SortOrder.DESC));
        }
        if (StringUtils.isBlank(keyword)){
            QueryBuilder matchAll = QueryBuilders.matchAllQuery();
            build.withQuery(matchAll);
        }else {
            QueryBuilder queryStringQueryBuilder = QueryBuilders.queryStringQuery(keyword);
            build.withQuery(queryStringQueryBuilder);
        }
        NativeSearchQuery searchQuery = build.withPageable(pageable).build();
        SearchHits<CulturalInfo> result = elasticsearchRestTemplate.search(searchQuery, CulturalInfo.class);
        // 存入数据
        List<CulturalInfo> list = new ArrayList<>();

        for (SearchHit<CulturalInfo> culturalInfoHit : result) {
            CulturalInfo content = culturalInfoHit.getContent();
            list.add(content);
        }
        EsDigitPage<CulturalInfo> resultPage = new EsDigitPage<>();
        resultPage.setRecord(list);
        resultPage.setTotal(result.getTotalHits());
        resultPage.setCurrent(pageEntity.getCurrent());
        resultPage.setSize(pageEntity.getSize());
        return resultPage;
    }

    @Override
    public void save(CulturalInfo culturalInfo) {
        culturalRepository.save(culturalInfo);
    }

    @Override
    public void delete(Long id) {
        culturalRepository.deleteById(id);
    }

    @Override
    public CulturalInfo get(Long id) {
        Optional<CulturalInfo> optional = culturalRepository.findById(id);
        if (optional.isPresent()){
            return optional.get();
        }
        return null;
    }

    @Override
    public void saveAll(List<CulturalInfo> allCulturalInfo) {
        culturalRepository.saveAll(allCulturalInfo);
    }

    @Override
    public boolean deleteAll() {
        elasticsearchRestTemplate.deleteIndex(CulturalInfo.class);
        return true;
    }

}
