package com.das.es.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.es.dao.CollectionRepository;
import com.das.es.entity.CollectionInfo;
import com.das.es.entity.requestEntity.SearchEntity;
import com.das.es.entity.responseEntity.EsDigitPage;
import com.das.es.entity.responseEntity.SysCodeAndCount;
import com.das.es.service.ICollectionService;
import com.das.es.utils.Utils;
import com.das.system.entity.SysCode;
import com.das.system.service.ISysCodeService;
import com.das.utils.common.CangPConstant;
import com.das.utils.response.PageEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021-07-30
 */

@Slf4j
@Service
public class CollectionServiceImpl implements ICollectionService {

    @Autowired
    private CollectionRepository collectionRepository;

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Autowired
    private ISysCodeService sysCodeService;

    @Override
    public void save(CollectionInfo collectionInfo) {
        collectionRepository.save(collectionInfo);
    }

    @Override
    public void delete(Long id) {
        collectionRepository.deleteById(id);
    }

    @Override
    public CollectionInfo get(Long id) {
        Optional<CollectionInfo> byId = collectionRepository.findById(id);
        if (byId.isPresent()){
            return byId.get();
        }
        return null;
    }

    @Override
    public void saveAll(List<CollectionInfo> allCollection) {
        collectionRepository.saveAll(allCollection);
    }

    @Override
    public boolean deleteAll() {
        elasticsearchRestTemplate.deleteIndex(CollectionInfo.class);
        return true;
    }

    @Override
    public IPage<CollectionInfo> findByPage(SearchEntity searchEntity) {
        Pageable pageable = PageRequest.of(searchEntity.getCurrent()-1,searchEntity.getSize());
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(QueryBuilders.queryStringQuery(searchEntity.getKeyword())).withPageable(pageable).build();

        SearchHits<CollectionInfo> searchHits = elasticsearchRestTemplate.search(searchQuery, CollectionInfo.class);
        List<CollectionInfo> list = new ArrayList<>();
        for (SearchHit<CollectionInfo> collectionInfoSearchHit : searchHits) {
            CollectionInfo collectionInfo = collectionInfoSearchHit.getContent();
            list.add(collectionInfo);
        }
        Page page = new Page<CollectionInfo>();
        page.setTotal(searchHits.getTotalHits());
        page.setCurrent(searchEntity.getCurrent());
        page.setSize(searchEntity.getSize());
        page.setRecords(list);
        return page;
    }

    @Override
    public EsDigitPage<CollectionInfo> findByPageForCollection(PageEntity pageEntity) {
        NativeSearchQueryBuilder groupBuild = new NativeSearchQueryBuilder();
        TermsAggregationBuilder typeAgg = AggregationBuilders.terms("group_type").field("typeCode");
        groupBuild.addAggregation(typeAgg);

        String keyword = pageEntity.getParams().get("keyword").toString();
        String type = pageEntity.getParams().get("type").toString();
        SearchHits<CollectionInfo> countSearch =null;
        if (StringUtils.isBlank(keyword)){
            countSearch = elasticsearchRestTemplate.search(groupBuild.build(), CollectionInfo.class);
        }else {
            countSearch = elasticsearchRestTemplate.search(groupBuild.withQuery(QueryBuilders.queryStringQuery(keyword)).build(), CollectionInfo.class);
        }

        NativeSearchQueryBuilder build = new NativeSearchQueryBuilder();
        Pageable pageable = PageRequest.of(pageEntity.getCurrent()-1,pageEntity.getSize());
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isBlank(keyword)){
            build.withSort(SortBuilders.fieldSort("createTime.keyword").order(SortOrder.DESC));
        }
        if (StringUtils.isBlank(type)&&StringUtils.isBlank(keyword)){
            QueryBuilder matchAll = QueryBuilders.matchAllQuery();
            boolQueryBuilder.must(matchAll);
        }else {
            if (StringUtils.isNotBlank(keyword)){
                QueryBuilder queryStringQueryBuilder = QueryBuilders.queryStringQuery(keyword);
                boolQueryBuilder.must(queryStringQueryBuilder);
            }
            if (StringUtils.isNotBlank(type)){
                if ("-1".equals(type)){
                    boolQueryBuilder.mustNot(QueryBuilders.existsQuery("typeCode"));
                }else {
                    QueryBuilder typeQuery = QueryBuilders.matchQuery("typeCode", Integer.parseInt(type));
                    boolQueryBuilder.must(typeQuery);
                }
            }
        }
        NativeSearchQuery searchQuery = build.withQuery(boolQueryBuilder).withPageable(pageable).build();

        log.info(searchQuery.getQuery().toString());

        SearchHits<CollectionInfo> result = elasticsearchRestTemplate.search(searchQuery, CollectionInfo.class);
        // 存入数据
        List<CollectionInfo> list = new ArrayList<>();
        for (SearchHit<CollectionInfo> collectionInfoSearchHit : result) {
            CollectionInfo content = collectionInfoSearchHit.getContent();
            list.add(content);
        }
        EsDigitPage<CollectionInfo> resultPage = new EsDigitPage<>();
        resultPage.setRecord(list);
        resultPage.setTotal(result.getTotalHits());
        resultPage.setCurrent(pageEntity.getCurrent());
        resultPage.setSize(pageEntity.getSize());
        Terms term = countSearch.getAggregations().get("group_type");
        List<SysCode> sysCodeList = sysCodeService.getByFiledAndIsChild(CangPConstant.TYPE_FIELD,1);
        List<SysCodeAndCount> typeList = Utils.typeCount(sysCodeList, term, countSearch.getTotalHits());
        resultPage.setTypeCount(typeList);
        return resultPage;
    }

    private String[] getAllFiled(Class clazz){
        Field[] fields = clazz.getDeclaredFields();
        List<String> list = new ArrayList<>();
        for (Field field : fields) {
            if ("id".equals(field.getName())){
                continue;
            }
            list.add(field.getName());
        }

        String[] strings = new String[list.size()];

        strings = list.toArray(strings);
        return strings;

    }
}
