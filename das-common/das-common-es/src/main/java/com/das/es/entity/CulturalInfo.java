package com.das.es.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * 文创信息实体es类
 *
 * <AUTHOR>
 * @since 2021-07-30
 */

@Data
@Document(indexName = "cultural_info")
public class CulturalInfo {

    /**
     * id
     */
    @Id
    private Long id;

    /**
     * 名称
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String name;

    /**
     * 图片
     */
    @Field(type = FieldType.Keyword)
    private String image;

    /**
     * 设计者
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String author;

    /**
     * 大小
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String size;

    /**
     * 色系
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String color;

    /**
     * 材质
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String texture;

    /**
     * IP归属
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String ip;

    /**
     * 设计灵感
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String inspiration;

    /**
     * 文化符号
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String sign;

    /**
     * 文化意义
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String meaning;

    /**
     * 消费需求
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String needs;

    /**
     * 消费场景
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String scene;

    /**
     * 是否锁定（0：否；1：是）
     */
    @Field(type = FieldType.Long)
    private Integer lockStatus;

    /**
     * 更新时间
     */
    @Field(type = FieldType.Keyword)
    private String updateTime;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Keyword)
    private String createTime;

    /**
     * 授权方
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String authorizer;

    /**
     * 授权范围
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String authorizerRange;

    /**
     * 代理商
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String agent;

}
