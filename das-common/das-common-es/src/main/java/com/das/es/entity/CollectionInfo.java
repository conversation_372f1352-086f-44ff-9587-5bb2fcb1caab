package com.das.es.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;


/**
 * <AUTHOR>
 * @date 2021-07-30
 */
@Document(indexName = "collection_info")
@Data
public class CollectionInfo {

    @Id
    private Long id;

    /**
     * 图片
     */
    @Field(type = FieldType.Keyword)
    private String picture;
    /**
     * 登记号
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String registrationNumber;
    /**
     * 分类号 Long
     */
    private String classifyNumber;
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String classifyDetail;

    /**
     * 藏品名称
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String name;
    /**
     * 原号
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String oldNumber;
    /**
     * 原名
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String oldName;
    /**
     * 藏品级别 Long
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String level;
    /**
     * 藏品类别 Long
     */
    @Field(type = FieldType.Keyword)
    private String type;

    @Field(type = FieldType.Long)
    private Long typeCode;
    /**
     * 入藏年代范围 Long
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String timeFrame;
    /**
     * 入藏时间
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String inTime;
    /**
     * 所在地址
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String address;
    /**
     * 基本信息
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String information;
    /**
     * 年代
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String age;
    /**
     * 具体年代
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String ageDetail;
    /**
     * 用途
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String used;
    /**
     * 颜色
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String color;

    /**
     * 作者
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String author;

    /**
     * 作者小传
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String authorStory;

    /**
     * 附属物说明
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String appendages;

    /**
     * 长(厘米)
     */
    @Field(type = FieldType.Keyword)
    private String length;

    /**
     * 宽(厘米)
     */
    @Field(type = FieldType.Keyword)
    private String width;

    /**
     * 高(厘米)
     */
    @Field(type = FieldType.Keyword)
    private String height;

    /**
     * 腹围(厘米)
     */
    @Field(type = FieldType.Keyword)
    private String presence;

    /**
     * 口径(厘米)
     */
    @Field(type = FieldType.Keyword)
    private String bore;

    /**
     * 底径（厘米）
     */
    @Field(type = FieldType.Keyword)
    private String bootBore;

    /**
     * 具体尺寸
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String size;

    /**
     * 质量范围  long
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String quality;

    /**
     * 具体质量
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String qualityDetail;

    /**
     * 质量单位 long
     */
    @Field(type = FieldType.Keyword)
    private String qualityUnit;

    /**
     * 件数
     */
    @Field(type = FieldType.Keyword)
    private String count;

    /**
     * 数量单位 long
     */
    @Field(type = FieldType.Keyword)
    private String countUnit;

    /**
     * 实际数量
     */
    @Field(type = FieldType.Keyword)
    private String realityNumber;

    /**
     * 数量备注
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String remark;

    /**
     * 保存状态 long
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String saveStatus;

    /**
     * 完残程度 long
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String complete;

    /**
     * 完残情况
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String completeInfo;

    /**
     * 质地 long[]
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String texture;

    /**
     * 质地备注
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String textureInfo;

    /**
     * 来源 long
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String source;

    /**
     * 具体来源
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String sourceInfo;

    /**
     * 出土位置
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String excavated;

    /**
     * 出土时间
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String excavatedTime;

    /**
     * 藏品备注
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String collectionRemark;
    /**
     * 征集时间
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String collectTime;

    /**
     * 入馆凭证号
     */
    private String museumVoucher;

    /**
     * 入馆时间
     */
    private String inMuseumTime;

    /**
     * 排序
     */
    private String sort;

    /**
     * 入藏凭证号
     */
    private String collectingVoycher;

    /**
     * 库房id long
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String room;

    /**
     * 柜架id long
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String frame;

    /**
     * 层id long
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String floor;

    /**
     * 屉id long
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String drawer;

    /**
     * 列id long
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String column;

    /**
     * 号id long
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String number;

    /**
     * 藏品包装 long
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String pack;
    /**
     * 包装尺寸
     */
    private String packSize;
    /**
     * 预定信息
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String bookInfo;
    /**
     * 形状内容描述
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String description;
    /**
     * 搜集过程
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String process;

    /**
     * 流传经历
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String handDown;

    /**
     * 现状记录
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String record;

    /**
     * 附录
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String appendix;

    /**
     * 内容提要
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String synopsis;

    @Field(type = FieldType.Keyword)
    private String createTime;
    @Field(type = FieldType.Keyword)
    private String colStatus;
    @Field(type = FieldType.Keyword)
    private String writeUserId;
    @Field(type = FieldType.Keyword)
    private Integer status;

}
