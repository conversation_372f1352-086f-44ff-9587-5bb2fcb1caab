package com.das.es.entity;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2022/5/26
 */
@Document(indexName = "cj_ruins_info", createIndex = false)
@Data
@Accessors(chain = true)
public class EsEntity {

    @Id
    private String id;

    @Field(type = FieldType.Long)
    private Long tid;

    /**
     * 资源id
     */
    @Field(type = FieldType.Long)
    private Long resourceId;

    /****************************************遗址基础信息****************************************/
    // 考古遗址名称
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String ruinsName;

    // 类别
    @Field(type = FieldType.Text)
    private String category;

    // 市
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String city;

    // 县
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String county;

    // 地址
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String address;

    // 经度
    @Field(type = FieldType.Keyword)
    private Double longitude;

    // 纬度
    @Field(type = FieldType.Keyword)
    private Double latitude;

    // 海拔高度
    @Field(type = FieldType.Integer)
    private Double altitude;

    // 级别
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String level;

    // 面积
    @Field(type = FieldType.Integer)
    private Double acreage;

    // 年代
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String age;

    // 使用者
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String owner;

    // 隶属
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String subjection;

    // 单体描述
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String monomerDescription;

    // 简介
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String description;

    // 保存状况
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String preserveStatus;

    // 状态描述
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String statusDescription;

    // 自然损毁原因
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String naturalReason;

    // 人为损毁原因
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String artificialReason;

    // 损毁原因描述
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String reasonDescription;

    // 环境
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String environment;

    // 人文
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String humanity;

    /****************************************出土文物基础信息****************************************/
    // 出土文物名称
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String relicsNames;

    /****************************************发掘资料基础信息****************************************/
    // 发掘资料名称
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String materialNames;

    // 发掘资料类型
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String materialTypes;

    // 发掘资料附件名称
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String materialFileNames;

    /****************************************数字化成果基础信息****************************************/
    // 数字化成果名称
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String digitNames;

    // 数字化成果类型
    @Field(analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", type = FieldType.Text)
    private String digitTypes;

}
