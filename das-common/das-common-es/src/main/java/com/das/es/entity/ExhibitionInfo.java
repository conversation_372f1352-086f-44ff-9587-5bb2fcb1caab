package com.das.es.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * <AUTHOR>
 * @date 2021-07-30
 */

@Document(indexName = "exhibition_info")
@Data
public class ExhibitionInfo {

    @Id
    private Long id;

    /**
     * 展览主题
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String title;

    /**
     * 展览 地址
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String address;

    /**
     * 环境
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String environment;

    /**
     * 策划人
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String planner;
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String typeDetail;

    @Field(type = FieldType.Long)
    private Long type;
    /**
     * 移交人
     */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_max_word")
    private String userName;

    /**
     * 更新时间
     */
    @Field(type = FieldType.Keyword)
    private String updateTime;

    /**
     * 图片路径
     */
    @Field(type = FieldType.Keyword)
    private String picture;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Keyword)
    private String createTime;

    @Field(type = FieldType.Keyword)
    private String startTime;

    @Field(type = FieldType.Keyword)
    private String endTime;

}
