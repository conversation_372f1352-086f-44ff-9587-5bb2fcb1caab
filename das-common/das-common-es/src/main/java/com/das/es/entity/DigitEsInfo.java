package com.das.es.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.List;

/**
 * @author: SL
 * @create: 2021-07-30 09:41
 **/
@Document(indexName = "search")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DigitEsInfo {

    @Id
    private Long id;

    private String title;

    private String titleNo;

    private int creatorId;

    private String creator;

    private String description;

    private String publisher;

    private String contributor;

    private String date;

    private int type;

    private String format;

    private String identifier;

    private String language;

    private int source;

    private String sourceName;

    private String relation;

    private String coverage;

    private String rights;

    @TableField(value = "`rank`")
    private int rank;

    private int released;

    private int enabled;

    private String shareUrl;

    private String coverUrl;

    private String createTime;

    private String updateTime;

    @Field(type = FieldType.Nested)
    private List<Object> digitResFiles;

    private String archivesType;

    private String researchDirection;

    private String researchPerson;

}
