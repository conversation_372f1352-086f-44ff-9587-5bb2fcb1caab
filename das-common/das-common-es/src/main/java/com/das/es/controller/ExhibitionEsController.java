package com.das.es.controller;

import com.das.annotation.RequestSecurity;
import com.das.es.service.IExhibitionService;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultMsg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@RestController
@RequestMapping("/esExhibition")
public class ExhibitionEsController {

    @Autowired
    private IExhibitionService exhibitionService;

    @RequestSecurity(value = "/findByPageForExhibition",method = RequestMethod.POST)
    public ResultMsg findByPageForDig(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(exhibitionService.findByPageForExhibition(pageEntity));
    }

    @RequestSecurity(value = "/deleteAll",method = RequestMethod.POST)
    public ResultMsg delete() {
        return new ResultMsg(exhibitionService.deleteAll());
    }
}
