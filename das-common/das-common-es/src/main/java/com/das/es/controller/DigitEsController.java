package com.das.es.controller;

import com.das.annotation.RequestSecurity;
import com.das.es.entity.DigitEsInfo;
import com.das.es.entity.requestEntity.SearchEntity;
import com.das.es.service.impl.DigitEsServiceImpl;
import com.das.utils.response.ResultMsg;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: SL
 * @create: 2021-07-30 10:30
 **/
@RestController
@RequestMapping("/search")
public class DigitEsController {

    @Resource
    private DigitEsServiceImpl digitEsService;

    @RequestSecurity(value = "/saveInfo", method = RequestMethod.POST)
    public void saveInfo(@RequestBody DigitEsInfo digitEsInfo){
        digitEsService.saveInfo(digitEsInfo);
    }

    @RequestSecurity(value = "/findByPage", method = RequestMethod.POST)
    public ResultMsg findByPage (@RequestBody SearchEntity searchEntity){
        return new ResultMsg(digitEsService.findByPage(searchEntity));
    }

    @RequestSecurity(value = "/del", method = RequestMethod.POST)
    public void del (@RequestParam(value = "id") long id){
        digitEsService.del(id);
    }

    @RequestSecurity(value = "/saveAll", method = RequestMethod.POST)
    public void saveAll (@RequestBody List<DigitEsInfo> list){
        digitEsService.saveAll(list);
    }

}
