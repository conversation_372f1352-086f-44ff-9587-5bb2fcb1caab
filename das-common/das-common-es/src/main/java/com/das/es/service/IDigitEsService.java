package com.das.es.service;

import com.das.es.entity.DigitEsInfo;
import com.das.es.entity.requestEntity.SearchEntity;
import com.das.es.entity.responseEntity.EsDigitPage;

import java.util.List;

/**
 * @author: SL
 * @create: 2021-07-30 10:00
 **/
public interface IDigitEsService {

    void saveInfo(DigitEsInfo digitEsInfo);

    EsDigitPage<DigitEsInfo> findByPage(SearchEntity searchEntity);

    void del(Long id);

    void saveAll(List<DigitEsInfo> list);

}
