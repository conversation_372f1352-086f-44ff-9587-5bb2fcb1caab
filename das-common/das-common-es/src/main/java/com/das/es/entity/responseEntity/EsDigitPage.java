package com.das.es.entity.responseEntity;

import com.das.utils.response.PageEntity;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  es分页分类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@Data
public class EsDigitPage<T> extends PageEntity {
    private List<T> record;
    private Map<String,Object> type;
    private List<SysCodeAndCount> typeCount;

    private Long total;
}
