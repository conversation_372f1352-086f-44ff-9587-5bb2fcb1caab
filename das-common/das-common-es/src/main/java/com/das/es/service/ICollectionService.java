package com.das.es.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.das.es.entity.CollectionInfo;
import com.das.es.entity.requestEntity.SearchEntity;
import com.das.es.entity.responseEntity.EsDigitPage;
import com.das.utils.response.PageEntity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-07-30
 */

public interface ICollectionService {

    void save(CollectionInfo collectionInfo);

    void delete(Long id);

    CollectionInfo get(Long id);

    void saveAll(List<CollectionInfo> allCollection);

    boolean deleteAll();

    IPage<CollectionInfo> findByPage(SearchEntity searchEntity);
    EsDigitPage<CollectionInfo> findByPageForCollection(PageEntity pageEntity);

}
