package com.das.es.service;

import com.das.es.entity.CulturalInfo;
import com.das.es.entity.responseEntity.EsDigitPage;
import com.das.utils.response.PageEntity;

import java.util.List;

public interface ICulturalService {

    EsDigitPage<CulturalInfo> findByPageForCultural(PageEntity pageEntity);

    void save(CulturalInfo culturalInfo);

    void delete(Long id);

    CulturalInfo get(Long id);

    void saveAll(List<CulturalInfo> allCulturalInfo);

    boolean deleteAll();
}
