package com.das.es.service.impl;

import com.das.es.dao.DigitEsRepository;
import com.das.es.entity.DigitEsInfo;
import com.das.es.entity.requestEntity.SearchEntity;
import com.das.es.entity.responseEntity.EsDigitPage;
import com.das.es.entity.responseEntity.SysCodeAndCount;
import com.das.es.service.IDigitEsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.nested.NestedAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.nested.ParsedNested;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: SL
 * @create: 2021-07-30 10:01
 **/
@Service
@Transactional
@Slf4j
public class DigitEsServiceImpl implements IDigitEsService {

    @Resource
    private DigitEsRepository digitEsRepository;

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Override
    public void saveInfo(DigitEsInfo digitEsInfo) {
        boolean exists = elasticsearchRestTemplate.indexOps(DigitEsInfo.class).exists();
        // 如果索引不存在,则创建
        if(!exists){
            elasticsearchRestTemplate.indexOps(DigitEsInfo.class).create();
            Document mappings = elasticsearchRestTemplate.indexOps(DigitEsInfo.class).createMapping();
            elasticsearchRestTemplate.indexOps(DigitEsInfo.class).putMapping(mappings);
        }
        digitEsRepository.save(digitEsInfo);
        log.info("插入数据成功");
    }

    @Override
    public EsDigitPage findByPage(SearchEntity searchEntity) {
        boolean exists = elasticsearchRestTemplate.indexOps(DigitEsInfo.class).exists();
        // 判断索引是否存在
        if(!exists){
            EsDigitPage page = new EsDigitPage();
            page.setTotal(Long.valueOf("0"));
            page.setCurrent(searchEntity.getCurrent());
            page.setSize(searchEntity.getSize());
            page.setTypeCount(null);
            page.setRecord(null);
            return page;
        }
        Pageable pageable = PageRequest.of(searchEntity.getCurrent() - 1, searchEntity.getSize());
        int sourceType;
        switch (Integer.parseInt(searchEntity.getParams().get("sourceType").toString())) {
            case 1:
                sourceType = 132; //研究（以研究方向、研究人分组统计）
                return getResearchBuilder(searchEntity, sourceType, pageable);
            case 2:
                sourceType = 133;//讲解(资源分类、以各种资源类型进行分组统计)
                return getExplain(searchEntity, sourceType, pageable);
            case 3:
                sourceType = 134;//出版（以出版社、出版人分组统计）
                return getPublish(searchEntity, sourceType, pageable);
            case 4:
                sourceType = 135;//档案（以档案分类分组统计）
                return getArchives(searchEntity, sourceType, pageable);
            default:
                return null;
        }
    }

    /**
     * 档案查询
     *
     * @param searchEntity
     * @param sourceType
     * @param pageable
     * @return
     */
    private EsDigitPage getArchives(SearchEntity searchEntity, int sourceType, Pageable pageable) {
        QueryBuilder QueryBuilderAll = null;
        if(!"".equals(searchEntity.getKeyword())){
            QueryBuilderAll = QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType))
                    .must(QueryBuilders.queryStringQuery(searchEntity.getKeyword()));
        }else{
            QueryBuilderAll = QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType));
        }
        //分组构造器
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms("group_one").field("archivesType.keyword");
        QueryBuilder QueryBuilder = null;

        //条件查询构造器
        if (!"".equals(searchEntity.getParams().get("type"))) {
            String param1 = searchEntity.getParams().get("name").toString();
            QueryBuilder = !"".equals(searchEntity.getKeyword()) ?
                    QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType))
                            .must(QueryBuilders.queryStringQuery(searchEntity.getKeyword()))
                            .must(QueryBuilders.matchQuery("archivesType.keyword", param1)) :
                    QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType))
                            .must(QueryBuilders.matchQuery("archivesType.keyword", param1));
        } else {
            QueryBuilder = !"".equals(searchEntity.getKeyword()) ?
                    QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType))
                            .must(QueryBuilders.queryStringQuery(searchEntity.getKeyword())) :
                    QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType));

        }

        //排序构造器
        FieldSortBuilder sortBuilder = SortBuilders.fieldSort("createTime.keyword").order(SortOrder.DESC);

        //查询构造器
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilder)
                .withPageable(pageable)
                .withSort(sortBuilder)
                .build();

        //按关键词查询分组
        NativeSearchQuery queryGroup = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilderAll)
                .addAggregation(Objects.requireNonNull(aggregationBuilder))
                .withPageable(pageable)
                .build();

        //按类型查询分组
        NativeSearchQuery queryAll = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType)))
                .addAggregation(Objects.requireNonNull(aggregationBuilder))
                .withPageable(pageable)
                .build();

        return getEsDigitPage(searchEntity, query, sourceType, queryGroup, queryAll);
    }

    /**
     * 出版查询
     *
     * @param searchEntity
     * @param sourceType
     * @param pageable
     * @return
     */
    private EsDigitPage getPublish(SearchEntity searchEntity, int sourceType, Pageable pageable) {
        QueryBuilder QueryBuilderAll = null;
        if(!"".equals(searchEntity.getKeyword())){
            QueryBuilderAll = QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType))
                    .must(QueryBuilders.queryStringQuery(searchEntity.getKeyword()));
        }else{
            QueryBuilderAll = QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType));
        }
        //分组构造器
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms("group_one").field("publisher.keyword")
                .subAggregation(AggregationBuilders.terms("group_two").field("contributor.keyword"));
        QueryBuilder QueryBuilder = null;

        //条件查询构造器
        if (!"".equals(searchEntity.getParams().get("type"))) {
            String param1 = searchEntity.getParams().get("name").toString();
            String type = searchEntity.getParams().get("type").toString();
            if (type.equals("1")) {
                QueryBuilder = !"".equals(searchEntity.getKeyword()) ?
                        QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType))
                                .must(QueryBuilders.queryStringQuery(searchEntity.getKeyword()))
                                .must(QueryBuilders.matchQuery("publisher.keyword", param1)) :
                        QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType))
                                .must(QueryBuilders.matchQuery("publisher.keyword", param1));
            } else {
                QueryBuilder = !"".equals(searchEntity.getKeyword()) ?
                        QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType))
                                .must(QueryBuilders.queryStringQuery(searchEntity.getKeyword()))
                                .must(QueryBuilders.matchQuery("contributor.keyword", param1)) :
                        QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType))
                                .must(QueryBuilders.matchQuery("contributor.keyword", param1));
            }
        } else {
            QueryBuilder = !"".equals(searchEntity.getKeyword()) ?
                    QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType))
                            .must(QueryBuilders.queryStringQuery(searchEntity.getKeyword())) :
                    QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType));

        }

        //排序构造器
        FieldSortBuilder sortBuilder = SortBuilders.fieldSort("createTime.keyword").order(SortOrder.DESC);

        //查询构造器
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilder)
                .withPageable(pageable)
                .withSort(sortBuilder)
                .build();

        //按关键词查询分组
        NativeSearchQuery queryGroup = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilderAll)
                .addAggregation(Objects.requireNonNull(aggregationBuilder))
                .withPageable(pageable)
                .build();

        //按类型查询分组
        NativeSearchQuery queryAll = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType)))
                .addAggregation(Objects.requireNonNull(aggregationBuilder))
                .withPageable(pageable)
                .build();

        return getEsDigitPage(searchEntity, query, sourceType, queryGroup, queryAll);
    }

    /**
     * 讲解查询
     *
     * @param searchEntity
     * @param sourceType
     * @param pageable
     * @return
     */
    private EsDigitPage getExplain(SearchEntity searchEntity, int sourceType, Pageable pageable) {
        QueryBuilder QueryBuilderAll = null;
        if(!"".equals(searchEntity.getKeyword())){
            QueryBuilderAll = QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType))
                    .must(QueryBuilders.queryStringQuery(searchEntity.getKeyword()));
        }else{
            QueryBuilderAll = QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType));
        }
        //分组构造器
        NestedAggregationBuilder aggregationBuilder = AggregationBuilders.nested("digitResFiles", "digitResFiles")
                .subAggregation(AggregationBuilders.terms("sourceType_group").field("digitResFiles.sourceType.keyword"));
        QueryBuilder QueryBuilder = null;

        //条件查询构造器
        if (!"".equals(searchEntity.getParams().get("type"))) {
            String param1 = searchEntity.getParams().get("name").toString();
            QueryBuilder = !"".equals(searchEntity.getKeyword()) ?
                    QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType))
                            .must(QueryBuilders.queryStringQuery(searchEntity.getKeyword()))
                            .must(QueryBuilders.nestedQuery("digitResFiles", QueryBuilders.boolQuery()
                                    .must(QueryBuilders.matchQuery("digitResFiles.sourceType.keyword", param1)), ScoreMode.None)) :
                    QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType))
                            .must(QueryBuilders.nestedQuery("digitResFiles", QueryBuilders.boolQuery()
                                    .must(QueryBuilders.matchQuery("digitResFiles.sourceType.keyword", param1)), ScoreMode.None));
        } else {
            QueryBuilder = !"".equals(searchEntity.getKeyword()) ?
                    QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType))
                            .must(QueryBuilders.queryStringQuery(searchEntity.getKeyword())) :
                    QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType));
        }

        //排序构造器
        FieldSortBuilder sortBuilder = SortBuilders.fieldSort("createTime.keyword").order(SortOrder.DESC);

        //查询构造器
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilder)
                .withPageable(pageable)
                .withSort(sortBuilder)
                .build();

        //按关键词查询分组
        NativeSearchQuery queryGroup = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilderAll)
                .addAggregation(Objects.requireNonNull(aggregationBuilder))
                .withPageable(pageable)
                .build();

        //按类型查询分组
        NativeSearchQuery queryAll = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType)))
                .addAggregation(Objects.requireNonNull(aggregationBuilder))
                .withPageable(pageable)
                .build();

        return getEsDigitPage(searchEntity, query, sourceType, queryGroup, queryAll);
    }

    /**
     * 研究查询
     *
     * @param searchEntity
     * @param sourceType
     * @param pageable
     * @return
     */
    private EsDigitPage getResearchBuilder(SearchEntity searchEntity, int sourceType, Pageable pageable) {
        QueryBuilder QueryBuilderAll = null;
        if(!"".equals(searchEntity.getKeyword())){
            QueryBuilderAll = QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType))
                    .must(QueryBuilders.queryStringQuery(searchEntity.getKeyword()));
        }else{
            QueryBuilderAll = QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType));
        }
        //分组构造器
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms("group_one").field("researchDirection.keyword")
                .subAggregation(AggregationBuilders.terms("group_two").field("researchPerson.keyword"));
        QueryBuilder QueryBuilder = null;

        //条件查询构造器
        if (!"".equals(searchEntity.getParams().get("type"))) {
            String param1 = searchEntity.getParams().get("name").toString();
            String type = searchEntity.getParams().get("type").toString();
            if (type.equals("1")) {
                QueryBuilder = !"".equals(searchEntity.getKeyword()) ?
                        QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType))
                                .must(QueryBuilders.queryStringQuery(searchEntity.getKeyword()))
                                .must(QueryBuilders.matchQuery("researchDirection.keyword", param1)) :
                        QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType))
                                .must(QueryBuilders.matchQuery("researchDirection.keyword", param1));
            } else {
                QueryBuilder = !"".equals(searchEntity.getKeyword()) ?
                        QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType))
                                .must(QueryBuilders.queryStringQuery(searchEntity.getKeyword()))
                                .must(QueryBuilders.matchQuery("researchPerson.keyword", param1)) :
                        QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType))
                                .must(QueryBuilders.matchQuery("researchPerson.keyword", param1));
            }
        } else {
            QueryBuilder = !"".equals(searchEntity.getKeyword()) ?
                    QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType))
                            .must(QueryBuilders.queryStringQuery(searchEntity.getKeyword())) :
                    QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType));

        }

        //排序构造器
        FieldSortBuilder sortBuilder = SortBuilders.fieldSort("createTime.keyword").order(SortOrder.DESC);

        //查询构造器
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilder)
                .withPageable(pageable)
                .withSort(sortBuilder)
                .build();

        //按关键词查询分组
        NativeSearchQuery queryGroup = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilderAll)
                .addAggregation(Objects.requireNonNull(aggregationBuilder))
                .withPageable(pageable)
                .build();

        //按类型查询分组
        NativeSearchQuery queryAll = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilders.boolQuery().must(QueryBuilders.termQuery("type", sourceType)))
                .addAggregation(Objects.requireNonNull(aggregationBuilder))
                .withPageable(pageable)
                .build();

        return getEsDigitPage(searchEntity, query, sourceType, queryGroup, queryAll);
    }

    /**
     * 查询结果返回
     *
     * @param searchEntity
     * @param query
     * @param sourceType
     * @param queryAllType
     * @return
     */
    private EsDigitPage getEsDigitPage(SearchEntity searchEntity, NativeSearchQuery query, int sourceType, NativeSearchQuery queryAllType, NativeSearchQuery queryAll) {
        SearchHits<DigitEsInfo> search = elasticsearchRestTemplate.search(query, DigitEsInfo.class);
        List<DigitEsInfo> list = new ArrayList<>();
        for (SearchHit<DigitEsInfo> digitEsInfoSearchHit : search) {
            DigitEsInfo digitEsInfo = digitEsInfoSearchHit.getContent();
            list.add(digitEsInfo);
        }
        //当前根据关键字查询后的分组
        SearchHits<DigitEsInfo> searchHits = elasticsearchRestTemplate.search(queryAllType, DigitEsInfo.class);
        List<SysCodeAndCount> allType = getAllType(searchHits, sourceType);

        //当前类型的所有分组
        SearchHits<DigitEsInfo> hits = elasticsearchRestTemplate.search(queryAll, DigitEsInfo.class);
        List<SysCodeAndCount> countList = getAllType(hits, sourceType);

        Map<String,Long> map_allType = allType.stream().collect(Collectors.toMap(SysCodeAndCount::getCodeName,SysCodeAndCount::getCount));
        Map<String,Long> map_countList = countList.stream().collect(Collectors.toMap(SysCodeAndCount::getCodeName,SysCodeAndCount::getCount));
        //将查询后不存在的类型统计置0
        map_countList.entrySet().forEach(entry->{
            if(!map_allType.containsKey(entry.getKey())){
                entry.setValue(Long.valueOf("0"));
            }
        });
        List<SysCodeAndCount> andCountList = map_countList.entrySet().stream().map(e-> {
           for(SysCodeAndCount code:countList){
                if(e.getKey().equals(code.getCodeName())){
                    return  new SysCodeAndCount(e.getKey(),e.getValue(),code.getType());
                }
            }
           return null;
        }).collect(Collectors.toList());
        EsDigitPage page = new EsDigitPage();
        page.setTotal(search.getTotalHits());
        page.setCurrent(searchEntity.getCurrent());
        page.setSize(searchEntity.getSize());
        page.setTypeCount(andCountList);
        page.setRecord(list);
        return page;
    }

    /**
     * 获取分组统计信息
     *
     * @param search
     * @param sourceType
     * @return
     */
    private List<SysCodeAndCount> getAllType(SearchHits<DigitEsInfo> search, int sourceType) {
        Terms terms;
        if (sourceType == 133) {
            terms = ((ParsedNested) Objects.requireNonNull(search.getAggregations()).get("digitResFiles")).getAggregations().get("sourceType_group");
        } else {
            terms = Objects.requireNonNull(search.getAggregations()).get("group_one");
        }
        List<SysCodeAndCount> lt = new ArrayList<>();
        for (Terms.Bucket bucket : terms.getBuckets()) {
            SysCodeAndCount sysCodeAndCount = new SysCodeAndCount();
            sysCodeAndCount.setCount(bucket.getDocCount());
            sysCodeAndCount.setCodeName(bucket.getKeyAsString());
            sysCodeAndCount.setType(1);
            lt.add(sysCodeAndCount);
            if (sourceType == 132 || sourceType == 134) {
                Terms terms1 = bucket.getAggregations().get("group_two");
                for (Terms.Bucket bucket1 : terms1.getBuckets()) {
                    SysCodeAndCount sysCode = new SysCodeAndCount();
                    sysCode.setCount(bucket1.getDocCount());
                    sysCode.setCodeName(bucket1.getKeyAsString());
                    sysCode.setType(2);
                    lt.add(sysCode);
                }
            }
        }
        return lt;
    }

    @Override
    public void del(Long id) {
        digitEsRepository.deleteById(id);
    }

    @Override
    public void saveAll(List<DigitEsInfo> list) {
        boolean exists = elasticsearchRestTemplate.indexOps(DigitEsInfo.class).exists();
        // 如果索引不存在,则创建
        if(!exists){
            elasticsearchRestTemplate.indexOps(DigitEsInfo.class).create();
            Document mappings = elasticsearchRestTemplate.indexOps(DigitEsInfo.class).createMapping();
            elasticsearchRestTemplate.indexOps(DigitEsInfo.class).putMapping(mappings);
        }
        digitEsRepository.saveAll(list);
        log.info("插入数据成功");
    }

}
