package com.das.es.utils;

import com.das.es.entity.responseEntity.SysCodeAndCount;
import com.das.system.entity.SysCode;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-07-30
 */
public class Utils {

    public static List<SysCodeAndCount> typeCount(List<SysCode> sysCodes , Terms term, Long total){
        List<SysCodeAndCount> result = new ArrayList<SysCodeAndCount>();
        long count  = 0;
        for (SysCode sysCode : sysCodes) {
            SysCodeAndCount sysCodeAndCount = new SysCodeAndCount();
            sysCodeAndCount.setId(sysCode.getId().toString());
            sysCodeAndCount.setCodeName(sysCode.getDescription());
            sysCodeAndCount.setCount(0L);
            for (Terms.Bucket bucket : term.getBuckets()) {
                if (bucket.getKeyAsString().equals(sysCode.getId().toString())){
                    sysCodeAndCount.setCount(bucket.getDocCount());
                    count += bucket.getDocCount();
                    break;
                }
            }
            result.add(sysCodeAndCount);
        }
        SysCodeAndCount otherCount = new SysCodeAndCount();
        otherCount.setId("-1");
        otherCount.setCodeName("未分类");
        otherCount.setCount(total-count);
        result.add(otherCount);
        return result;
    }
}
