package com.das.es.controller;

import com.das.es.entity.requestEntity.SearchEntity;
import com.das.es.service.EsService;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Description: 搜索接口
 *
 * <AUTHOR>
 * @since 2022/5/26
 */
@Api(tags = "搜索接口")
@RestController
@RequestMapping("/esSearch")
public class SearchController {

    private static final Logger logger = LoggerFactory.getLogger(SearchController.class);

    @Resource
    private EsService esTopicService;

    @ApiOperation(value = "获取搜索结果")
    @RequestMapping(value = "/getSearchList", method = RequestMethod.POST)
    public ResultMsg getSearchList(@RequestBody SearchEntity searchEntity) {
        return new ResultMsg(esTopicService.searchList(searchEntity.getKeyword(), null));
    }

}