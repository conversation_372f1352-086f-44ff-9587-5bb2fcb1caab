package com.das.annotation;

import com.das.utils.aesEncrypt.AesEncryptUtils;
import org.springframework.web.bind.annotation.Mapping;

import java.lang.annotation.*;

/**
 * Description TODO
 *
 * <AUTHOR>
 * @since 2021/2/24
 */

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Mapping
@Documented
public @interface SecurityParameter {

    /**
     * 入参是否解密，默认解密
     */
    boolean inDecode() default AesEncryptUtils.OPEN_AES;

    /**
     * 出参是否加密，默认加密
     */
    boolean outEncode() default AesEncryptUtils.OPEN_AES;
}