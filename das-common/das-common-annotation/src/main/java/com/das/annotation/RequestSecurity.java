package com.das.annotation;
import com.das.utils.aesEncrypt.AesEncryptUtils;
import org.springframework.core.annotation.AliasFor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.lang.annotation.*;

/**
 * Description TODO
 *
 * <AUTHOR>
 * @since 2021/2/24
 */

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@SecurityParameter
@RequestMapping
@Documented
public @interface RequestSecurity {

    @AliasFor(annotation = SecurityParameter.class)
    boolean inDecode() default AesEncryptUtils.OPEN_AES;

    @AliasFor(annotation = SecurityParameter.class)
    boolean outEncode() default AesEncryptUtils.OPEN_AES;

    @AliasFor(annotation = RequestMapping.class)
    String[] value() default {};

    @AliasFor(annotation = RequestMapping.class)
    RequestMethod[] method() default RequestMethod.POST;

}