package com.das.shiros;

import com.das.system.entity.SysUser;
import com.das.system.service.ISysUserService;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.servlet.AdviceFilter;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR> <PERSON>
 * date 2020-04-19
 */
public class ShiroLoginFilter extends AdviceFilter {
	private ISysUserService userService;


	public ShiroLoginFilter(ISysUserService userService){
		this.userService = userService;
	}
    /**
     * 在shiro中配置loginUrl为login.html
     * 如果用户没有认证会跳转到login.html
     * 这里拦截login.html只要拦截到这个请求，就返回用户没有登录的信息
     */
    @Override
    protected boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
//        String sessionId = httpServletRequest.getRequestedSessionId();
        Subject subject = SecurityUtils.getSubject();
		if (!subject.isAuthenticated()) {
        	//未登录用户
        	if(httpServletRequest.getRequestURI().contains("/notLogin")){
				// notLogin 放行
	            return true;
        	}else if(subject.isRemembered()){
        		//记住我用户
        		String principal = subject.getPrincipal().toString();
        		SysUser user = userService.loginByUserName(principal);
        		//再次登录，便于其它系统或集群能共享登录状态
        		UsernamePasswordToken usernamePasswordToken =
						new UsernamePasswordToken(user.getUserName(), user.getPassword(), true);
        		subject.login(usernamePasswordToken);
//        		return true;
        	}
        }
        return true;
    }
}