package com.das.shiros;

import com.das.system.entity.SysUser;
import com.das.system.mapper.SysMenuMapper;
import com.das.system.service.ISysRoleService;
import com.das.system.service.ISysUserService;
import com.das.utils.common.Constants;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.*;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;

import javax.annotation.Resource;

/**
 * <AUTHOR> wangyongzheng
 * @since : 2021-6-28 12:01
 * TODO shiro配置类
 **/
public class MyShiroRealm extends AuthorizingRealm {

    @Resource
    public ISysUserService userService;
    @Resource
    private SysMenuMapper menuMapper;
    @Resource
    private ISysRoleService iSysRoleService;

    /**
     * 认证
     */
    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authenticationToken) throws AuthenticationException {
        UsernamePasswordToken token = (UsernamePasswordToken) authenticationToken;
        String username = token.getUsername();
        //验证用户是否存在
        SysUser user = userService.loginByUserName(username);
        if (user == null) {
            throw new UnknownAccountException("该用户不存在");
        }

        if (user.getStatus() != 0) {
            throw new LockedAccountException("帐户已锁定");
        }
        //查询用户权限List
        user.setRoleList(iSysRoleService.queryUserRole(user));

        //默认为账号登录。如果需要微信认证，这里需要重新判断
        boolean flag = true;
        if (flag) {
            //管理员认证
            //验证用户密码
            SimpleAuthenticationInfo simpleAuthenticationInfo =
                    new SimpleAuthenticationInfo(username, user.getPassword(), getName());
            //绑定到session
            user.setPassword(null);

            SecurityUtils.getSubject().getSession().setAttribute(Constants.MANAGER_USER, user);
            return simpleAuthenticationInfo;
        } else {
            //微信认证
            //验证用户密码
            SimpleAuthenticationInfo simpleAuthenticationInfo =
                    new SimpleAuthenticationInfo(username, username, getName());
            return simpleAuthenticationInfo;
        }
    }

    /**
     * 授权
     */
    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        //基础用户这边不需要权限 ， 只需要认证就行了。
        SysUser user = (SysUser) SecurityUtils.getSubject().getSession().getAttribute(Constants.MANAGER_USER);
        // 用户存在 授权
        if (user != null) {
            SimpleAuthorizationInfo authorizationInfo = new SimpleAuthorizationInfo();
            // 权限
            authorizationInfo.setStringPermissions(menuMapper.getPermissionByGuid(user.getGuid()));
            return authorizationInfo;
        }
        return new SimpleAuthorizationInfo();
    }
}
