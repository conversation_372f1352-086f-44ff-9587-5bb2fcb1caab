package com.das.shiros;

import com.das.utils.response.ResultCode;
import com.das.utils.response.ResultMsg;
import org.apache.shiro.authz.AuthorizationException;
import org.apache.shiro.authz.UnauthorizedException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * date 2020-04-19
 */
@ControllerAdvice
public class NoPermissionException {
    @ResponseBody
    @ExceptionHandler(UnauthorizedException.class)
    public ResultMsg handleShiroException(Exception ex) {
        return new ResultMsg(ResultCode.CODE_10006,  null);
    }
    @ResponseBody
    @ExceptionHandler(AuthorizationException.class)
    public ResultMsg AuthorizationException(Exception ex) {
        return new ResultMsg(ResultCode.CODE_10006, null);
    }
}
