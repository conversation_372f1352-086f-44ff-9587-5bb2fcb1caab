package com.das.shiros;

import com.das.utils.common.StringHandle;
import com.das.yml.RedisConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.shiro.authc.credential.HashedCredentialsMatcher;
import org.apache.shiro.codec.Base64;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.spring.LifecycleBeanPostProcessor;
import org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.mgt.CookieRememberMeManager;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.apache.shiro.web.servlet.SimpleCookie;
import org.apache.shiro.web.session.mgt.DefaultWebSessionManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.crazycake.shiro.RedisCacheManager;
import org.crazycake.shiro.RedisManager;
import org.crazycake.shiro.RedisSessionDAO;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR> Yongzheng
 * @since 2020-04-19
 * TODO shiro配置类
 */
@Configuration
public class ShiroConfig {

    @Autowired
    private RedisConfig redisConfig;

    /**
     * 配置拦截器
     *
     * @param securityManager 处理拦截资源文件问题
     * @return ShiroFilterFactoryBean
     */
    @Bean
    public ShiroFilterFactoryBean shiroFactory(SecurityManager securityManager) {
        ShiroFilterFactoryBean shiroFilterFactoryBean = new ShiroFilterFactoryBean();
        shiroFilterFactoryBean.setSecurityManager(securityManager);
        shiroFilterFactoryBean.getFilters().put("shiroLoginFilter", shiroLoginFilter());
        Map<String, String> filterMap = new LinkedHashMap<String, String>();

        filterMap.put("/home/<USER>", "anon"); //查询坐标
        filterMap.put("/ruins/**", "anon"); //查询古文化遗址
        filterMap.put("/cjCollectionInfo/init", "anon"); //文物类
        filterMap.put("/cjCollectionInfo/getCollectionInfo", "anon"); //文物类
        filterMap.put("/cjCollectionInfo/getById/**", "anon"); //文物类
        filterMap.put("/cjReport/**", "anon"); //文献
        filterMap.put("/sysCode/**", "anon"); //字典
        filterMap.put("/uploadHandle/**", "anon"); //上传
        filterMap.put("/cjArchaeologyAge/**", "anon"); //时间轴
        filterMap.put("/ruinsDetail", "anon"); //时空演变推送数据
        filterMap.put("/cjRuinsInfo/ExportCjRuinsInfoList", "anon");
        filterMap.put("/cjRuinsInfo/getCjRuinsInfoAll", "anon");
        filterMap.put("/cjRuinsInfo/getRuinsDetail", "anon");
        filterMap.put("/sysMenu/getUserHomeMenus", "anon");
        filterMap.put("/cjRuinsInfo/getCjRuinsInfoById", "anon");
        filterMap.put("/cjRuinsRelics/getCjRuinsRelicsList", "anon");
        filterMap.put("/cjRuinsRelics/getTypeAndCount", "anon");
        filterMap.put("/cjRuinsMaterial/getCjRuinsMaterialList", "anon");
        filterMap.put("/cjRuinsMaterial/getTypeAndCount", "anon");
        filterMap.put("/cjRuinsMaterial/getCjRuinsMaterialById", "anon");
        filterMap.put("/cjRuinsDigit/getCjRuinsDigitList", "anon");
        filterMap.put("/cjRuinsDigit/getTypeAndCount", "anon");
        filterMap.put("/cjRuinsDigit/getCjRuinsDigitById", "anon");
        filterMap.put("/cjRuinsInfoTombs/getCjRuinsInfoTombsDetailById", "anon");
        filterMap.put("/cjDictionaries/**", "anon");
        filterMap.put("/cjRuinsInfoTombs/**", "anon");



        filterMap.put("/navigation/webSocket", "anon");
        filterMap.put("/webSocket", "anon");
        filterMap.put("/sysMenu/getUserMenus", "anon");
        filterMap.put("/sysMenu/getCenterMeun", "anon");
        filterMap.put("/user/loginUser", "anon");
        filterMap.put("/swagger-ui.html", "anon");
        filterMap.put("/webjars/springfox-swagger-ui/**", "anon");
        filterMap.put("/swagger-resources/**", "anon");
        filterMap.put("/digitRes/getShareData", "anon");
        filterMap.put("/digitRes/getResAll", "anon");
        filterMap.put("/digitResApply/getAuditDigitRes", "anon");
        filterMap.put("/v2/**", "anon");
        filterMap.put("/user/getVrSession", "anon");
        filterMap.put("/**", "user");
        shiroFilterFactoryBean.setFilterChainDefinitionMap(filterMap);
        shiroFilterFactoryBean.setLoginUrl("/notLogin");
        shiroFilterFactoryBean.setSuccessUrl("/success");

        return shiroFilterFactoryBean;
    }

    @Bean(name = "securityManager")
    public SecurityManager securityManager() {
        DefaultWebSecurityManager manager = new DefaultWebSecurityManager();
        manager.setRealm(myShiroRealm());
        manager.setRememberMeManager(cookieRememberMeManager());
        manager.setSessionManager(sessionManager());
        return manager;
    }

    @Bean
    public MyShiroRealm myShiroRealm() {
        return new MyShiroRealm();
    }

    /**
     * cacheManager 缓存 redis实现
     * 使用的是shiro-redis开源插件
     */
    @Bean
    public RedisCacheManager cacheManager() {
        RedisCacheManager redisCacheManager = new RedisCacheManager();
        redisCacheManager.setRedisManager(redisManager());
        redisCacheManager.setExpire(redisConfig.getExpire());
        return redisCacheManager;
    }

    /**
     * 配置shiro redisManager
     * 使用的是shiro-redis开源插件
     */
    @Bean
    public RedisManager redisManager() {
        RedisManager redisManager = new RedisManager();
        redisManager.setHost(redisConfig.getHost());
        redisManager.setPort(redisConfig.getPort());
        if (StringHandle.isNotEmpty(redisConfig.getPassword())) {
            redisManager.setPassword(redisConfig.getPassword());
        }
        return redisManager;
    }

    @Bean(name = "sessionManager")
    public DefaultWebSessionManager sessionManager() {
        DefaultWebSessionManager sessionManager = new DefaultWebSessionManager();
        sessionManager.setGlobalSessionTimeout(-1);
        // 判断是否是redis环境
        if (redisConfig.isRedis == 1) {
            sessionManager.setSessionDAO(redisSessionDAO());
        }
        return sessionManager;
    }

    /**
     * RedisSessionDAO shiro sessionDao层的实现 通过redis
     * 使用的是shiro-redis开源插件
     */
    @Bean
    public RedisSessionDAO redisSessionDAO() {
        RedisSessionDAO redisSessionDAO = new RedisSessionDAO();
        redisSessionDAO.setExpire(redisConfig.getExpire());
        redisSessionDAO.setRedisManager(redisManager());
        return redisSessionDAO;
    }

    /**
     * 凭证匹配器
     * （由于我们的密码校验交给Shiro的SimpleAuthenticationInfo进行处理了
     * 所以我们需要修改下doGetAuthenticationInfo中的代码;
     * ）
     */
    @Bean
    public HashedCredentialsMatcher hashedCredentialsMatcher() {
        HashedCredentialsMatcher hashedCredentialsMatcher = new HashedCredentialsMatcher();
        //散列算法:这里使用MD5算法;
        hashedCredentialsMatcher.setHashAlgorithmName("md5");
        hashedCredentialsMatcher.setHashIterations(2);
        return hashedCredentialsMatcher;
    }


    @Bean
    public CookieRememberMeManager cookieRememberMeManager() {
        CookieRememberMeManager cr = new CookieRememberMeManager();
        cr.setCipherKey(Base64.decode(StringHandle.createMD5("admin","7E84AB0DEAF1AEB38383926EEC477AC6")));
        cr.setCookie(simpleCookie());
        return cr;
    }


    @Bean(name = "shiroLoginFilter")
    public ShiroLoginFilter shiroLoginFilter() {
        return new ShiroLoginFilter(myShiroRealm().userService);
    }

    @Bean
    public SimpleCookie simpleCookie() {
        SimpleCookie simpleCookie = new SimpleCookie("rememberMe");
        simpleCookie.setHttpOnly(true);
        //记住我cookie生效时间,默认30天 ,单位秒：60 * 60 * 24 * 30
        simpleCookie.setMaxAge(-1);
        return simpleCookie;
    }

    /**
     * 使用shiro的注解，必须要配置的bean
     *
     * @param securityManager manager
     * @return AuthorizationAttributeSourceAdvisor
     */
    @Bean
    public AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor(SecurityManager securityManager) {
        AuthorizationAttributeSourceAdvisor attributeSourceAdvisor = new AuthorizationAttributeSourceAdvisor();
        attributeSourceAdvisor.setSecurityManager(securityManager);
        return attributeSourceAdvisor;
    }

    @Bean
    public static LifecycleBeanPostProcessor lifecycleBeanPostProcessor() {
        return new LifecycleBeanPostProcessor();
    }
}
