package com.das.yml;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <p>
 * DbBackConfig
 * <p/>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@Component
@ConfigurationProperties(prefix = "mysql.back")
public class DbBackConfig {

    @Getter
    @Setter
    public String ip;

    @Getter
    @Setter
    public String port;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String username;

    @Getter
    @Setter
    private String password;

    @Getter
    @Setter
    private String address;

    @Getter
    @Setter
    private String savePath;

    @Getter
    @Setter
    private String mysqldumpPath;
}
