package com.das.yml;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> wangyongzheng
 * @since : 2021-6-28 12:01
 * TODO 公共配置类，详细说明见YML文件
 **/
@Component
@ConfigurationProperties(prefix = "spring.redis")
@Data
public class RedisConfig {
    public String host;
    public int port;
    public String password;
    public int expire;
    /**
     * 是否是redis环境
     */
    public int isRedis;
}
