package com.das.yml;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * TODO 公共配置类，详细说明见YML文件
 *
 * <AUTHOR>
 * @since 2020-11-16 12:01
 *
 */
@Component
@ConfigurationProperties(prefix = "common.config")
@Data
public class CommonConfig {

    public String renderUrl;
    public String managerUrl;
    public int synClud;
    public String loginUrl;
    public String userAddUrl;
    public String userUrl;
    public String useList;
    public String clearList;
    public String getUrl;
    public String getCount;

}
