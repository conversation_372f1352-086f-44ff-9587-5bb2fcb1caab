package com.das.aspect;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> @since 2020-07-20 11:21
 */
public class DescribesActionIfo {
    private static final Map<String, Long> describesMap;

    static {
        describesMap = new HashMap<>();
        describesMap.put("/user/loginUser", 2L);                            // 用户登录
        describesMap.put("/user/logout", 4L);                               // 用户退出登录
        describesMap.put("/user/getAllUserByRole", 5L);                     // 分页获取用户信息
        describesMap.put("/user/addUserInfo", 6L);                          // 新增或编辑用户信息
        describesMap.put("/user/updateUserInfo", 7L);                       // 批量更新用户状态等信息
        describesMap.put("/sysRole/deleteUserRole", 8L);                    // 删除角色
        describesMap.put("/sysRole/setRole", 9L);                           // 角色授予菜单权限
        describesMap.put("/sysRole/addRole", 10L);                          // 新增平台角色
//        describesMap.put("/sysMenu/getUserMenus", 11L);                   // 获取用户菜单权限列表
        describesMap.put("/sysMenu/saveMenu", 12L);                         // 新增菜单
        describesMap.put("/sysUserRole/addRole", 13L);                      // 新增或者编辑平台角色
        describesMap.put("/sysUserRole/deleteUserRole", 14L);               // 删除角色
        describesMap.put("/sysUserRole/setAdminPermissions", 15L);          // 设置分配任务权限

        describesMap.put("/dept/setDept", 16L);                             // 新增或修改部门
        describesMap.put("/dept/delDept", 17L);                             // 删除部门

        describesMap.put("/sysCode/saveCode", 18L);                         // 新增或者修改字典
        describesMap.put("/sysCode/saveCodeDetail", 19L);                   // 新增或者修改字典明细
        describesMap.put("/sysCode/deleteCodeDetail", 20L);                 // 删除字典明细
        describesMap.put("/database/deleteRecordsInfo", 21L);               // 删除数据库列表
        describesMap.put("/database/remoteBackup", 22L);                    // 异地数据库备份
        describesMap.put("/digitRes/downloadUrlFile", 23L);                // 下载文件
        describesMap.put("/datam/addDataManager", 24L);                     // 新增资源数据
        describesMap.put("/datam/delDataManager", 25L);                     // 删除数字资源
//        describesMap.put("/datam/getReqDataCheck", 26L);                  // 申请审核记录查询
        describesMap.put("/datam/getReqData", 27L);                         // 申请记录查询
        describesMap.put("/datam/checkReqDataManager", 28L);                // 提交申请
        describesMap.put("/datam/checkManager", 29L);                       // 资源审核功能
        describesMap.put("/datam/getDataManagerFile", 30L);                 // 获取附件列表(查看项目文件)
//        describesMap.put("/datam/getDataManager", 31L);                   // 获取资源数据
    }

    public static Long getDescribesActionIfo(String method) {
        Long res = 0L;
        for (String key : describesMap.keySet()) {
            if (method.indexOf(key) != -1) {
                res = describesMap.get(key);
            }
        }
        return res;
    }


}
