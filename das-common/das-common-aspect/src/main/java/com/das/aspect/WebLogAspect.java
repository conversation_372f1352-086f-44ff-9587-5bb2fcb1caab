package com.das.aspect;

import com.das.system.entity.*;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.service.impl.SysUserLogServiceImpl;
import com.das.utils.common.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * <AUTHOR>
 * date 2020-04-19
 */
@Aspect
@Slf4j
@Component
public class WebLogAspect {

    @Pointcut("execution(* com.das.*.controller.*.*(..)) || execution(* com.das.*.*.controller.*.*(..))")
    public void webLog() {
    }

    @Before("webLog()")
    public void doBefore(JoinPoint joinPoint) throws Throwable {
        // 接收到请求，记录请求内容
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return;
        }
        HttpServletRequest request = attributes.getRequest();
        // 记录下请求内容
        log.info("URL : " + request.getRequestURL().toString());
        log.debug("请求类型 : " + request.getMethod());
        log.debug("IP : " + request.getRemoteAddr());
        log.debug("请求方法 : " + joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName());
        log.debug("请求参数 : " + Arrays.toString(joinPoint.getArgs()));
        //从yml文件中获取开关是否打开
        if (true) {
            saveUserLog(joinPoint, request.getRequestURI(), request.getRemoteAddr());
        }
    }


    /**
     * 保存日志，后台使用异步线程方式
     *
     * @param joinPoint
     * @param method
     * @param ip
     */
    public void saveUserLog(JoinPoint joinPoint, String method, String ip) {
        StringBuffer stringBuffer = new StringBuffer();//请求参数结果
        SysUserLog log = new SysUserLog();
        for (Object object : joinPoint.getArgs()) {
            if (object == null || object instanceof MultipartFile[] || object instanceof HttpServletRequest || object instanceof HttpServletResponse) {
                continue;
            } else {
                try {
                    if (!method.contains("/digitRes/downloadUrlFile")) {
                        JSONObject jsonObject = JSONObject.fromObject(object);
                        jsonObject.remove("file");
                        stringBuffer.append(jsonObject.toString());
                        if (method.equals("/user/loginUser")) {
                            log.setName(jsonObject.get("username").toString());
                        }
                    } else {
                        stringBuffer.append(object.toString().substring(object.toString().lastIndexOf("/")+1));
                    }
                } catch (Exception e) {
                    return;
                }
            }

        }
        log.setIp(ip);
        SysUserActions userActions = new SysUserActions();
        userActions.setLogs(DescribesActionIfo.getDescribesActionIfo(method));
        if (userActions.getLogs().equals(3L)) {
            //请求描述
            log.setInfo("分页获取用户行为日志");
            //请求路径
            log.setMethod(method);
            log.setMethodId(16L);
            log.setTypes(6L);
            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject jsonObject = JSONObject.fromObject(stringBuffer.toString());
            PageEntity pageEntity = (PageEntity) JSONObject.toBean(jsonObject, PageEntity.class);
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("用户查询接口：", method);
            fromObjectUser.put("用户请求分页的第几页：", pageEntity.getCurrent());
            fromObjectUser.put("用户每页发送的条数：", pageEntity.getSize());
            fromObjectUser.put("每页的排序：", pageEntity.getOrder());
            fromObjectUser.put("以那个字段排序：", pageEntity.getSort());
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }

        if (userActions.getLogs().equals(2L)) {
            //请求描述
            log.setInfo("用户登录");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(13L);
            //类型（1：新增；2：更新；3：删除；4：撤销；5：发布；6:查询；7.下载8.登录）
            log.setTypes(8L);
            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("用户登录接口：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(4L)) {
            //请求描述
            log.setInfo("用户退出登录");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(13L);
            //类型（1：新增；2：更新；3：删除；4：撤销；5：发布；6:查询；7.下载8.登录9.退出）
            log.setTypes(9L);
            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("用户退出登录接口：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(5L)) {
            //请求描述
            log.setInfo("分页获取用户信息");
            //请求路径
            log.setMethod(method);
            log.setMethodId(13L);
            log.setTypes(6L);  //类型（1：新增；2：更新；3：删除；4：撤销；5：发布；6:查询；7.下载8.登录9.退出）
            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject jsonObject = JSONObject.fromObject(stringBuffer.toString());
            PageEntity pageEntity = (PageEntity) JSONObject.toBean(jsonObject, PageEntity.class);
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("分页获取用户信息接口：", method);
            fromObjectUser.put("用户请求分页的第几页：", pageEntity.getCurrent());
            fromObjectUser.put("用户每页发送的条数：", pageEntity.getSize());
            fromObjectUser.put("每页的排序：", pageEntity.getOrder());
            fromObjectUser.put("以那个字段排序：", pageEntity.getSort());
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(6L)) {
            JSONObject json = JSONObject.fromObject(stringBuffer.toString());//参数json化
            //请求描述
            log.setInfo(StringUtils.isEmpty(json.get("guid").toString()) ? "新增[" + json.get("name") + "]用户信息" : "编辑[" + json.get("name") + "]用户信息");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(13L);
            //类型（1：新增；2：更新；3：删除；4：撤销；5：发布；6:查询；7.下载8.登录9.退出10.其他）
            log.setTypes(10L);
            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put(StringUtils.isEmpty(json.get("guid").toString()) ? "新增用户信息：" : "编辑用户信息：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(7L)) {
            //请求描述
            log.setInfo("批量更新用户状态等信息");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(13L);

            log.setTypes(5L);

            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("批量更新用户状态等信息接口：", method);
            fromObjectUser.put("用户批量修改对象的id集合：", stringBuffer.toString());
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(8L)) {
            //请求描述
            log.setInfo("删除角色");
            //请求路径
            log.setMethod(method);
            log.setMethodId(13L);
            log.setTypes(6L);  //类型（1：新增；2：更新；3：删除；4：撤销；5：发布；6:查询；7.下载8.登录9.退出）

            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject jsonObject = JSONObject.fromObject(stringBuffer.toString());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("删除角色信息接口：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(9L)) {
            //请求描述
            log.setInfo("角色授予菜单权限");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(13L);

            log.setTypes(7L);
            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("角色授予菜单权限接口：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(24L)) {
            JSONObject json = JSONObject.fromObject(stringBuffer.toString());//参数json化
            //请求描述
            if (!json.get("updateContent").equals("")) {
                log.setInfo("通知整改[" + json.get("name") + "]资源");
            } else {
                log.setInfo("1".equalsIgnoreCase(json.get("exeType").toString()) ? "新增[" + json.get("name") + "]资源" : "修改[" + json.get("name") + "]资源");
            }

            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(20L);

            log.setTypes(7L);
            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("1".equalsIgnoreCase(json.get("exeType").toString()) ? "新增数字资源数据接口：" : "修改数字资源数据接口：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(10L)) {
            JSONObject json = JSONObject.fromObject(stringBuffer.toString());//参数json化
            //请求描述
            log.setInfo("0".equalsIgnoreCase(json.get("id").toString()) ? "新增平台角色" : "编辑平台角色");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(17L);

            log.setTypes(2L);

            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("0".equalsIgnoreCase(json.get("id").toString()) ? "新增平台角色接口：" : "编辑平台角色接口：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(11L)) {
            //请求描述
            log.setInfo("获取用户菜单权限列表");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(17L);

            log.setTypes(6L);

            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("获取用户菜单权限列表接口：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(12L)) {
            //请求描述
            log.setInfo("新增菜单");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(16L);

            log.setTypes(6L);

            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("新增菜单接口：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(13L)) {
            JSONObject json = JSONObject.fromObject(stringBuffer.toString());//参数json化
            //请求描述
            log.setInfo("新增或编辑平台角色");
            //请求路径
            log.setMethod(method);
            log.setMethodId(16L);
            log.setTypes(6L);  //类型（1：新增；2：更新；3：删除；4：撤销；5：发布；6:查询；7.下载8.登录9.退出）

            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject jsonObject = JSONObject.fromObject(stringBuffer.toString());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("新增或编辑平台角色", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(14L)) {
            //请求描述
            log.setInfo("删除角色");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(16L);

            log.setTypes(3L);

            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("删除角色：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(15L)) {
            /*JSONObject j =JSONObject.fromObject(stringBuffer.toString());
            List<Map> list = sysUserRoleMapper.getUserRoleInfo(j.get("id").toString());
            //请求描述
            if(list.size()>0){//撤销授权
                log.setInfo("撤销用户["+list.get(0).get("username")+"]的["+list.get(0).get("rolename")+"]权限");
            }else{//授权
                val q =new QueryWrapper<User>();
                q.eq("id",j.get("id"));
                val qw = new QueryWrapper<SysRole>();
                qw.eq("id",j.get("permissions"));
                List<User> userList = userService.list(q);
                List<SysRole> sysRoleList = sysRoleService.list(qw);
                log.setInfo("授予用户["+userList.get(0).getName()+"]["+sysRoleList.get(0).getName()+"]权限");
            }

            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(16L);

            log.setTypes(6L);

            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("设置分配角色权限：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);*/
        }
        if (userActions.getLogs().equals(16L)) {
            JSONObject json = JSONObject.fromObject(stringBuffer.toString());//参数json化
            //请求描述
            log.setInfo(StringUtils.isEmpty(json.get("id").toString()) ? "新增部门" : "修改部门");
            //请求路径
            log.setMethod(method);
            log.setMethodId(9L);
            log.setTypes(6L);  //类型（1：新增；2：更新；3：删除；4：撤销；5：发布；6:查询；7.下载8.登录9.退出）

            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject jsonObject = JSONObject.fromObject(stringBuffer.toString());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put(StringUtils.isEmpty(json.get("id").toString()) ? "新增部门接口：" : "修改部门接口：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(17L)) {
            //请求描述
            log.setInfo("删除部门");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(9L);

            log.setTypes(3L);

            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("删除部门：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(23L)) {
            //请求描述
            log.setInfo("下载[" + stringBuffer.toString() + "]文件");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(20L);

            log.setTypes(3L);

            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("下载文件：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(18L)) {
            JSONObject json = JSONObject.fromObject(stringBuffer.toString());//参数json化
            //请求描述
            log.setInfo("编辑字典类型".equalsIgnoreCase(json.get("name").toString()) ? "修改字典" : "新增字典");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(9L);

            log.setTypes(5L);

            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("编辑字典类型".equalsIgnoreCase(json.get("name").toString()) ? "修改字典接口：" : "新增字典接口：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(19L)) {
            JSONObject json = JSONObject.fromObject(stringBuffer.toString());//参数json化
            //请求描述
            log.setInfo("0".equalsIgnoreCase(json.get("id").toString()) ? "新增字典明细" : "修改字典明细");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(20L);

            log.setTypes(6L);

            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject jsonObject = JSONObject.fromObject(stringBuffer.toString());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("0".equalsIgnoreCase(json.get("id").toString()) ? "新增字典明细接口：" : "修改字典明细接口：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(20L)) {
            //请求描述
            log.setInfo("删除字典明细");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(15L);

            log.setTypes(6L);

            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject jsonObject = JSONObject.fromObject(stringBuffer.toString());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("删除字典明细接口：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(21L)) {
            //请求描述
            log.setInfo("删除数据库列表");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(15L);

            log.setTypes(3L);

            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("删除数据库列表接口：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(22L)) {
            //请求描述
            log.setInfo("异地数据库备份");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(15L);

            log.setTypes(3L);
            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("异地数据库备份：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(25L)) {

        }
        if (userActions.getLogs().equals(26L)) {
            //请求描述
            log.setInfo("已对[" + getDataName(stringBuffer) + "]资源审核完成");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(2L);

            log.setTypes(5L);

            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("数字资源申请审核记录查询接口：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(27L)) {
            //请求描述
            log.setInfo("数字资源申请记录查询");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(2L);

            log.setTypes(3L);
            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("数字资源申请记录查询接口：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(28L)) {
            //请求描述
            log.setInfo("申请[" + getDataName(stringBuffer) + "]资源");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(6L);

            log.setTypes(6L);
            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject jsonObject = JSONObject.fromObject(stringBuffer.toString());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("数字资源提交申请接口：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(29L)) {
            //请求描述
            log.setInfo("已审核[" + getDataName(stringBuffer) + "]资源");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(6L);

            log.setTypes(3L);
            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("数字资源申请审核接口：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(30L)) {
            //请求描述
            log.setInfo("查看[" + getDataName(stringBuffer) + "]资源");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(6L);
            log.setTypes(5L);
            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("获取附件列表：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
        if (userActions.getLogs().equals(31L)) {
            //请求描述
            log.setInfo("查询数字资源");
            //请求路径
            log.setMethod(method);
            //请求路径请求模块(9.典型建筑模型数据中心;16:字典管理)
            log.setMethodId(6L);

            log.setTypes(5L);

            log.setParam(stringBuffer.toString());
            //设置请求参数
            log.setDatetime(DateUtil.getDay());
            JSONObject fromObjectUser = new JSONObject();
            fromObjectUser.put("查询数字资源接口：", method);
            log.setDescribes(fromObjectUser.toString());
            SysUserLogServiceImpl.saveUserLog(log);
        }
    }

    //获取文件名称
    private String getFileName(StringBuffer stringBuffer) {
        return "";
    }

    //获取资源名称
    private String getDataName(StringBuffer stringBuffer) {
        return "";
    }

    @AfterReturning(returning = "ret", pointcut = "webLog()")
    public void doAfterReturning(JoinPoint joinPoint, Object ret) {
        // 处理完请求，返回内容
        log.info(joinPoint.getSignature().getName() + "====RESPONSE =====: " + ret);
    }
}
