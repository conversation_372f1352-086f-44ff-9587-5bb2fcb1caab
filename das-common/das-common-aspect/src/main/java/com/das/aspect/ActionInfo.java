package com.das.aspect;

import java.util.HashMap;
import java.util.Map;

/**
 * Description TODO
 *
 * <AUTHOR>
 * @since 2020/4/21
 */
public class ActionInfo {
    private static final Map<String, Long> actionMap;
    static
    {
        actionMap = new HashMap<String, Long>();
        actionMap.put("loginUser", 1L);                         // 用户登录
        actionMap.put("weChatLoginUser", 1L);                   // 用户登录
        actionMap.put("weChatInfrastructureOne", 2L);           // 用户浏览
        actionMap.put("3", 3L);                                 // 用户收听
        actionMap.put("saveWeChatComment", 4L);                 // 用户评论
        actionMap.put("getUserWeChatQuestionBank", 5L);         // 用户回答
        actionMap.put("getWeChatQuestionBankInvestigation", 6L);// 用户问卷
        actionMap.put("getWeChatFeedBack", 7L);                 // 用户反馈

    }
    public static long getUserAction(String method) {
        if (actionMap.get(method) == null) {
            return 0L;
        }
        return actionMap.get(method);
    }
}
