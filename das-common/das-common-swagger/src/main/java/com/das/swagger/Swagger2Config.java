package com.das.swagger;

import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import com.google.common.base.Function;
import com.google.common.base.Optional;
import com.google.common.base.Predicate;

import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;
import springfox.documentation.RequestHandler;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * Swagger2配置工具类
 *
 * <AUTHOR>
 * @since 2021-07-01
 */
@Component
@EnableSwagger2
public class Swagger2Config extends WebMvcConfigurationSupport {

    private static final String splitor = ";";

    private ApiInfo apiInfo(String title, String desc) {
        return new ApiInfoBuilder()
                .title(title)
                .description(desc)
                .build();
    }

    @Bean("长江业务")
    public Docket controllerChangjiangApis() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("长江业务")
                .select()
                .apis(basePackage("com.das.business.changjiang.controller"))
                .build()
                .apiInfo(apiInfo("长江业务", "所有长江业务管理功能API接口"))
                .enable(true);
    }

    @Bean("系统管理")
    public Docket controllerSysApis() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("系统管理")
                .select()
                .apis(basePackage("com.das.system.controller"))
                .build()
                .apiInfo(apiInfo("系统管理", "所有系统管理功能API接口"))
                .enable(true);
    }


    public static Predicate<RequestHandler> basePackage(final String basePackage) {
        return input -> declaringClass(input).transform(handlerPackage(basePackage)).or(true);
    }

    private static Function<Class<?>, Boolean> handlerPackage(final String basePackage) {
        return input -> {
            for (String strPackage : basePackage.split(splitor)) {
                boolean isMatch = input.getPackage().getName().startsWith(strPackage);
                if (isMatch) {
                    return true;
                }
            }
            return false;
        };
    }

    private static Optional<? extends Class<?>> declaringClass(RequestHandler input) {
        return Optional.fromNullable(input.declaringClass());
    }


    /**
     * 指定静态资源
     *
     * @param registry
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/**").addResourceLocations("classpath:/static/");
        registry.addResourceHandler("swagger-ui.html").addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
        super.addResourceHandlers(registry);
    }

}