package com.das;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * 服务启动类
 *
 * <AUTHOR>
 * @since 2020/4/21
 */
@SpringBootApplication
@MapperScan(basePackages = {"com.das.system.mapper", "com.das.business.*.mapper"})
@EnableScheduling
@EnableWebMvc
@EnableSwagger2
@EnableTransactionManagement(proxyTargetClass = true)
public class DasControllerApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(DasControllerApplication.class, args);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(DasControllerApplication.class);
    }
}
