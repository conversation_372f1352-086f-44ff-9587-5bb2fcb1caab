package com.das.controller.handle;

import lombok.Data;

/**
 * Description TODO 文件对象属性
 *
 * <AUTHOR>
 * @since 2020/4/21
 */
@Data
public class UploadFiles {
    /**
     * 附件名称
     */
    String name;
    /**
     * 保存名称
     */
    String saveName;
    /**
     * 加载路径
     */
    String loadPath;
    /**
     * 上传人
     */
    String userName;
    /**
     * 文件类型
     */
    String suffix;

    /**
     * 文件大小
     */
    Long fileSize;

    public UploadFiles() {
    }

    public UploadFiles(String name, String saveName, String loadPath, String userName, String suffix) {
        this.name = name;
        this.saveName = saveName;
        this.loadPath = loadPath;
        this.userName = userName;
        this.suffix = suffix;
    }

    public UploadFiles(String name, String saveName, String loadPath, String userName, String suffix,Long fileSize) {
        this.name = name;
        this.saveName = saveName;
        this.loadPath = loadPath;
        this.userName = userName;
        this.suffix = suffix;
        this.fileSize = fileSize;
    }
}
