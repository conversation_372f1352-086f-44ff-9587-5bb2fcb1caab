package com.das.controller.handle;

import com.das.annotation.RequestSecurity;
import com.das.system.entity.SysUser;
import com.das.utils.common.*;
import com.das.utils.response.ResultCode;
import com.das.utils.response.ResultMsg;
import com.das.yml.FtpConfig;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * Description TODO
 *
 * <AUTHOR>
 * @since 2020/4/21
 */
@Slf4j
@RestController
@RequestMapping(value = "/uploadHandle")
public class UploadHandle {
    @Autowired
    private FtpConfig ftpConfig;

    /**
     * 通用文件上传接口
     *
     * @param files   文件列表
     * @param request 请求体
     * @return ResultMsg
     */
    @RequestSecurity(value = "/upload", inDecode = false)
    public ResultMsg upload(@RequestParam("files") MultipartFile[] files, HttpServletRequest request) {
        if (files == null || files.length == 0) {
            return new ResultMsg(ResultCode.CODE_40003, null);
        }
        SysUser user = (SysUser) SystemUtil.getLoginUser();
        String model = request.getParameter("model");
        String resourcePath = Constants.PATH_MAP.get(model);
        List<UploadFiles> result = new ArrayList<>();
        for (MultipartFile multipartFile : files) {
            try {
                String fileName = StringHandle.getRealFileName(multipartFile);
                if (!StringHandle.isEmpty(fileName)) {
                    String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
                    String uuid = StringHandle.createUUID();
                    String saveName = uuid + "." + suffix;
                    String path = ftpConfig.getFtpPath() + resourcePath + saveName;
                    File dest = new File(path);
                    if (!dest.getParentFile().exists()) {
                        dest.getParentFile().mkdirs();
                    }
                    File file = new File(path);
                    multipartFile.transferTo(file);
                    if ("three".equals(model)) {
                        String unzipPath = ftpConfig.getFtpPath() + resourcePath + uuid;
                        if ("zip".equalsIgnoreCase(suffix)) {
                            UnZipAnRar.unZip(file, unzipPath);

                        } else if ("rar".equalsIgnoreCase(suffix)) {
                            UnZipAnRar.unRar(file, unzipPath);
                            unzipPath = unzipPath + "/" + fileName.substring(0, fileName.lastIndexOf("."));
                        } else {
                            log.error("上传的文件不是rar或者zip压缩包");
                            return new ResultMsg(ResultCode.CODE_40002, null);
                        }
                        System.out.println("unzipPath:" + unzipPath);
                        File file1 = new File(unzipPath);
                        File[] fs = file1.listFiles();
                        for (File f : fs) {
                            String name = f.getName();
                            if (!f.isDirectory() && name.substring(name.lastIndexOf(".") + 1).equals(CangPConstant.THREETYPE)) {
                                if ("zip".equalsIgnoreCase(suffix)) {
                                    resourcePath = resourcePath + uuid + "/";
                                } else {
                                    resourcePath = resourcePath + uuid + "/" + fileName.substring(0, fileName.lastIndexOf(".")) + "/";
                                }
                                saveName = name;
                            }
                        }
                        log.info("文件删除{}", file.delete() ? "成功" : "失败");
                    }
                    String loadPath = SystemUtil.getFileURL(resourcePath, saveName);
                    if (suffix.equalsIgnoreCase("tif")) {
                        // tif转png
                        suffix = "png";
                        saveName = uuid + "." + suffix;
                        fileName = saveName;
                        ImgUtil.tiffToPng(path, ftpConfig.getFtpPath() + resourcePath + saveName, suffix);
                        loadPath = SystemUtil.getFileURL(resourcePath, saveName);
                    }
                    result.add(new UploadFiles(fileName, saveName, loadPath, user.getName(), suffix, multipartFile.getSize()));
                }
            } catch (Exception e) {
                log.error(StringHandle.getExceptionMessage("上传文件失败", e));
                return new ResultMsg(ResultCode.CODE_40002, null);
            }
        }
        return new ResultMsg(result);
    }

    /**
     * 获取缩略图
     */
    @RequestSecurity(value = "/thumbnail", inDecode = false)
    public ResultMsg getThumbnail(@RequestParam(value = "loadPath") String loadPath, HttpServletRequest request) {
        String fileName = loadPath.substring(loadPath.lastIndexOf("/") + 1);
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        String model = request.getParameter("model");
        String resourcePath = Constants.MODEL_MAP.get(model) + Constants.PATH_MAP.get("img");
        try {
            String filePath = SystemUtil.getFilePath(resourcePath, suffix);
            File dest = new File(filePath);
            if (!dest.getParentFile().exists()) {
                dest.getParentFile().mkdirs();
            }
            Thumbnails.of(loadPath.replace(ftpConfig.getFtpUrl(), ftpConfig.getFtpPath()))
                    .scale(0.25f)
                    .toFile(filePath);
            return new ResultMsg(SystemUtil.getFileURL(resourcePath, filePath.substring(filePath.lastIndexOf("/") + 1)));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return new ResultMsg("");
    }

    /**
     * 上传图片
     */
    @RequestSecurity(value = "/image", inDecode = false)
    public ResultMsg uploadImg(@RequestParam(value = "file") MultipartFile multipartFile, HttpServletRequest request) {
        if (multipartFile.isEmpty()) {
            return new ResultMsg(ResultCode.CODE_40003, null);
        }
        String model = request.getParameter("model");
        String resourcePath = Constants.MODEL_MAP.get(model) + Constants.PATH_MAP.get("img");
        return imageFile(multipartFile, resourcePath);
    }

    private ResultMsg imageFile(MultipartFile multipartFile, String resourcePath) {
        String fileName = multipartFile.getOriginalFilename();
        String suffixName = fileName.substring(fileName.lastIndexOf(".") + 1);
        String path = SystemUtil.getFilePath(resourcePath, suffixName);
        File dest = new File(path);
        if (!dest.getParentFile().exists()) {
            dest.getParentFile().mkdirs();
        }
        String newName = path.substring(path.lastIndexOf("/") + 1);
        try {
            multipartFile.transferTo(dest);
            //图片大于5M,进行压缩
            if (multipartFile.getSize() > 5 * 1024 * 1024) {
                //压缩图片并保存
                String oldPath = path.substring(0, path.lastIndexOf("/") + 1);
                String newPath = oldPath + "0" + newName;
                if (suffixName.equalsIgnoreCase("tif")) {
                    // tif转png
                    suffixName = "png";
                    fileName = StringHandle.createUUID() + "." + suffixName;
                    ImgUtil.tiffToPng(path, ftpConfig.getFtpPath() + resourcePath + fileName, suffixName);
                    return new ResultMsg(SystemUtil.getFileURL(resourcePath, fileName));
                }
                ImgUtil.zipWidthHeightImageFile(path, newPath, 0, 0, 1f);
            }
            return new ResultMsg(SystemUtil.getFileURL(resourcePath, newName));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ResultMsg(ResultCode.CODE_40002, null);
    }


    /**
     * 导入数据使用的excel模板下载
     *
     * @param response
     */
    @RequestMapping(value = "/download", method = RequestMethod.GET)
    public void upload(HttpServletResponse response) {
        InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream("遗址概况模板.xls");
        downloadPathFile(in, response, "遗址概况模板");
    }

    @RequestMapping(value = "/downloadMz", method = RequestMethod.GET)
    public void downloadMz(HttpServletResponse response) {
        InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream("墓葬数据库.xls");
        downloadPathFile(in, response, "墓葬数据库");
    }

    @RequestMapping(value = "/downloadWx", method = RequestMethod.GET)
    public void downloadWx(HttpServletResponse response) {
        InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream("文献资料数据库.xls");
        downloadPathFile(in, response, "文献资料数据库");
    }

    @RequestMapping(value = "/downloadCt", method = RequestMethod.GET)
    public void downloadCt(HttpServletResponse response) {
        InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream("出土文物数据库.xls");
        downloadPathFile(in, response, "出土文物数据库");
    }

    @RequestMapping(value = "/downloadYz", method = RequestMethod.GET)
    public void downloadYz(HttpServletResponse response) {
        InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream("遗址管理.xls");
        downloadPathFile(in, response, "遗址管理");
    }

    @RequestMapping(value = "/downloadYzMz", method = RequestMethod.GET)
    public void downloadYzMz(HttpServletResponse response) {
        InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream("遗址墓葬管理.xls");
        downloadPathFile(in, response, "遗址墓葬管理");
    }

    /**
     * @Description:下载
     * @Param downloadPath 文件路径
     */
    public static Boolean downloadPathFile(InputStream fis, HttpServletResponse response, String fileName) {
        //判断文件是否存在
        if (fis != null) {
            fileName = fileName + ".xls";
            try {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } catch (Exception e) {
                log.error("URLEncoder encode error" + e);
            }
            response.setContentType("application/force-download");// 设置强制下载不打开
            response.addHeader("Content-Disposition", "attachment;fileName=" + fileName);// 设置文件名
            byte[] buffer = new byte[1024];
            BufferedInputStream bis = null;
            try {
                bis = new BufferedInputStream(fis);
                OutputStream os = response.getOutputStream();
                int i = bis.read(buffer);
                while (i != -1) {
                    os.write(buffer, 0, i);
                    i = bis.read(buffer);
                }
                return true;
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (bis != null) {
                    try {
                        bis.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                if (fis != null) {
                    try {
                        fis.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return false;
    }

}