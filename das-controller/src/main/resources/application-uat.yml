#启动设置
server:
  # 地址
  host: "127.0.0.1"
  # 端口号
  port: 6510
  servlet:
    encoding:
      charset: UTF-8
      force: true

spring:
  datasource:
    #mysql spi自动注入driver
    #    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************************
    username: root
    password: Das667788*
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      max-lifetime: 120000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  main:
    banner-mode: "off"
  jmx:
    enabled: false
  servlet:
    multipart:
      enabled: true
      max-file-size: 4096MB
      max-request-size: 4096MB
  redis:
    # Redis数据库索引（默认为0）
    database: 0
    # Redis服务器地址
    host: 127.0.0.1
    # Redis服务器连接端口
    port: 6379
    # Redis服务器连接密码（默认为空）
    password: das667788*
    # Redis是否开启（0：关闭；1：开启）
    isRedis: 0
    jedis:
      pool:
        # 连接池最大连接数（使用负值表示没有限制）
        max-active: -1
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接超时时间（毫秒）
        #timeout: 0
    expire: 36000

  elasticsearch:
    rest:
      uris: 127.0.0.1:9200

# 添加日志打印
logging:
  level:
    com.das: debug
    org.springframework.web: info

mybatis-plus:
  #  mapper-locations: classpath*:/mapper/*.xml
  mapper-locations: classpath*:/mapper/**/*Mapper.xml
  global-config:
    banner: false

ftp:
  upload-conf:
    #  资源访问地址
    ftpUrl: /cjResource/
    #  资源存储地址
    ftpPath: /home/<USER>/data/

# 系统定时备份任务需要读取配置
mysql:
  back:
    ip: 127.0.0.1
    port: 3306
    name: cj_gis_02
    username: root
    password: Das667788*
    address: /home/<USER>/data
    savePath: /home/<USER>/data   #备份路径
    mysqldumpPath: /usr/bin   #linux下mysql安装bin目录