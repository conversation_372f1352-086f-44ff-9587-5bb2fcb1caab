package com.das.utils.tasks;

import com.das.utils.common.Threads;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;

import java.util.TimerTask;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Title: AsyncManager
 * @Package com.dszh.grottoes.threads.tasks
 * @Description: TODO 异步任务管理器
 * <AUTHOR>
 * @date 2020/9/6 22:36
 */
public class AsyncManager {

    //延时执行时间
    private final int OPERATE_DELAY_TIME = 20;

    // 核心线程池大小
    private int corePoolSize = 50;

    //线程池
    private ScheduledExecutorService executor = scheduledExecutorService();

    private static AsyncManager me = new AsyncManager();

    public static AsyncManager me(){
        return me;
    }

    /**
     * 执行任务
     * @param task
     */
    public void executor(TimerTask task){
        executor.schedule(task, OPERATE_DELAY_TIME, TimeUnit.MILLISECONDS);
    }

    public void shutdown(){
        Threads.shutdownAndAwaitTermination(executor);
    }

    private ScheduledExecutorService scheduledExecutorService() {
        return new ScheduledThreadPoolExecutor(corePoolSize,
                new BasicThreadFactory.Builder().namingPattern("schedule-pool-%d").daemon(true).build()) {
            @Override
            protected void afterExecute(Runnable r, Throwable t) {
                super.afterExecute(r, t);
                Threads.printException(r, t);
            }
        };
    }

}
