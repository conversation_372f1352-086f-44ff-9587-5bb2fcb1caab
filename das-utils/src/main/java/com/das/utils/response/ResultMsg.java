package com.das.utils.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 请求返回实体
 *
 * <AUTHOR>
 * @since 2020-04-19
 */
@Data
public class ResultMsg implements Serializable  {

	private static final long serialVersionUID = -7943341373848133717L;
	private Integer code = 0;
	private String msg;
	private Object obj;

	public ResultMsg(Object obj) {
		super();
		this.code = 0;
		this.msg = "请求成功";
		this.obj = obj;
	}
	public ResultMsg(ResultCode resultCode, Object obj) {
		this.code = resultCode.getCode();
		this.msg = resultCode.getDesc();
		this.obj = obj;
	}

	public ResultMsg(ResultCode resultCode,String msg,Boolean f) {
		this.code = resultCode.getCode();
		this.msg = msg;
		this.obj = f;
	}

}
