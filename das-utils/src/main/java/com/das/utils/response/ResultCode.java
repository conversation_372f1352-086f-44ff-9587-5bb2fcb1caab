package com.das.utils.response;

/**
 * Description 系统状态码
 *
 * <AUTHOR>
 * @since 2020/4/19
 */
public enum ResultCode {

    /*******************************************************
     ***********************系统状态码**********************
     * 1xxx: 用户登录、用户权限状态码
     * 2xxx: 系统请求异常状态码
     * 3xxx: 系统业务状态码
     * 4xxx: 系统文件操作异常状态码
     * 5xxx: 藏品系统错误码
     *******************************************************
     *******************************************************
     */
    CODE_10000(10000, "SUCCESS"),
    CODE_10001(10001, "登录信息已失效，请重新登录。"),
    CODE_10002(10002, "账号不存在，请确认账号信息是否正确。"),
    CODE_10003(10003, "密码不正确，请重新输入密码。"),
    CODE_10004(10004, "账号已存在，可以换个账号试试~~"),
    CODE_10005(10005, "账号已锁定，请联系管理员。"),
    CODE_10006(10006, "您还没有该权限，请联系管理员。"),
    CODE_10007(10007, "用户名已存在，可以换个名称试试~~"),
    CODE_10008(10008, "手机号已存在，可以换个手机号试试~~"),
    CODE_10009(10009, "该部门已存在~~"),
    CODE_10010(10010, "该角色已存在~~"),
    CODE_10011(10011, "名称已存在~~"),
    CODE_10012(10012, "编号已存在~~"),
    CODE_10013(10013, "排序号已存在~~"),
    CODE_10014(10013, "请登录后再操作~~"),

    CODE_20001(20001, "参数不正确，请联系管理员。"),
    CODE_20002(20002, "请求失败！"),
    CODE_20003(20003, "请求路径不存在！"),

    CODE_30001(30001,"字典类型已存在，可以换个类型名称试试~~"),
    CODE_30002(30002,"该流程存在审核进程，暂时还不能删除哦~~"),
    CODE_30003(30002,"该凭证已经在审核中或者审核通过了，暂时还不能删除哦~~"),
    CODE_30004(30004,"该资源已被其他用户申请，且正在审核中，暂时还不能修改发布状态哦~~"),
    CODE_30005(30005,"已有用户存在该审批流中~~"),
    CODE_30006(30006,"请先将该用户从审批流中删除后再进行该操作~~"),


    CODE_40001(40001, "文件不存在！"),
    CODE_40002(40002, "文件上传失败！"),
    CODE_40003(40003, "上传文件为空，请重新选择！"),
    CODE_40004(40004, "数据导入失败！"),

    CODE_50001(50001,"总登号已经注册过来了，换个总登号试试~~"),
    CODE_50002(50002,"总登号输入有误,请检查！"),
    CODE_50003(50003,"未查询到数据"),
    CODE_50004(50004,"数据字典不存在的藏品类别！"),
    CODE_50005(50005,"数据字典不存在的藏品等级！"),
    CODE_50006(50006,"数据字典不存在的入藏年代范围!"),
    CODE_50007(50007,"数据字典不存在的质量范围!"),
    CODE_50008(50008,"数据字典不存在的质量单位!"),
    CODE_50009(50009,"数据字典不存在的来源"),
    CODE_500010(500010,"数据字典不存在的完残程度"),
    CODE_500011(500011,"数据字典不存在的保存状态"),
    CODE_500012(500012,"数据字典不存在的质地"),
    CODE_500013(500013,"存在子节点，请先删除子节点"),
    CODE_600001(600001,"json文件解析失败,properties节点中必须含有mc或Name属性,请检查后再上传！"),
    CODE_600002(600002,"已存在同类型地图，请勿重复添加！");


    private int code;
    private String desc;

    ResultCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
