package com.das.utils.common;


import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import java.io.*;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * XML工具类
 *
 * <AUTHOR>
 * @since 2022-05-26
 */
public class XmlUtil {

    public static void readXml() throws DocumentException {
        SAXReader reader = new SAXReader();
        Document doc = reader.read("D:\\Code\\db\\dsj.xml");
        Element e = doc.getRootElement();
        Iterator it = e.elementIterator();
        List<String> array = new ArrayList<>(10000);
        while (it.hasNext()) {
            Element book = (Element) it.next();
            Iterator itt = book.elementIterator();
            StringBuffer str = new StringBuffer();
            StringBuffer ins = new StringBuffer();
            while (itt.hasNext()) {
                Element element = (Element) itt.next();
                ins.append(element.getName() + ",");
                str.append("'" + element.getStringValue().trim().replace("\n", "") + "',");
             }
            array.add("(" + ins + ") values(" + str.toString() + ");");
        }

        writeTxt(array);
    }


    public static void writeTxt(List<String> array) {
        try {
            File file = new File("D:\\Code\\db\\data.txt");
            if (!file.exists()) {
                file.createNewFile();
            }
            FileOutputStream fos = new FileOutputStream(file, true);
            OutputStreamWriter osw = new OutputStreamWriter(fos);
            BufferedWriter bw = new BufferedWriter(osw);
            for (String str : array) {
                bw.write(str);
                bw.newLine();
            }
            bw.flush();
            bw.close();
            osw.close();
            fos.close();
        } catch (FileNotFoundException e1) {
            e1.printStackTrace();
        } catch (IOException e2) {
            e2.printStackTrace();
        }
    }


    public static void main(String[] args) {
        try {
            readXml();
        } catch (DocumentException e) {
            e.printStackTrace();
        }
    }


}
