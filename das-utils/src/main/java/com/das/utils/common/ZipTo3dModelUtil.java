package com.das.utils.common;

import com.das.yml.FtpConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.LinkedList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * 前提条件：服务器需安装node.js，执行npm install -g obj2gltf;npm install -g gltf-pipeline 两个命令
 * 模型zip文件转换成可访问的3d模型文件
 * 1. 解压模型
 * 2. .mtl  .obj 两个必须存在
 * 3. .jpg  .png 图片类型必须存在一个图片，可以有多个
 * 2. 执行命令：obj2gltf -i ***.obj -o gltf/model.gltf -s   将解压后的文件放回uuid文件夹下
 * 3. 删除转换前的文件
 * 4. 保留zip文件
 * 5. 验证是否转换成功 .bin  .gltf
 */
@Slf4j
@Component
public class ZipTo3dModelUtil {

    private static ZipTo3dModelUtil myUtil;

    //文件存储地址
    private static String url = "";

    @Autowired
    private FtpConfig ftpConfig;

    @PostConstruct
    public void getConfig() {
        myUtil = this;
        myUtil.ftpConfig = this.ftpConfig;
        myUtil.url = myUtil.ftpConfig.getFtpPath();
    }

    private static LinkedList<File> filesPath;

    /**
     * zip文件转换成可访问的3d模型文件
     * @param zipPath 压缩包路径
     * @return 返回get3d文件路径
     */
    public static String zipToModel(String zipPath){
        String returnStr = "";
        if(StringUtils.isBlank(zipPath)){
            return returnStr;
        }
        //解压后的资源路径
        String resourcePath = FileUtil.ftpUrlToFtpPath(zipPath.substring(0,zipPath.lastIndexOf(".")));
        //解压zip文件
        log.debug("开始解压 压缩包！！！");
        zipPath = FileUtil.ftpUrlToFtpPath(zipPath);
        unzip(zipPath,resourcePath);
        //获取解压后的文件夹中后缀为.obj文件路径
        filesPath = new LinkedList<>();
        getFilesPath(resourcePath);
        String objPath = "";
        for(int i = 0 ;i < filesPath.size();i++){
            if(filesPath.get(i).getPath().endsWith("tileset.json")
                    || filesPath.get(i).getPath().endsWith("tileset.JSON")){
                objPath = filesPath.get(i).getPath();
            }
        }
        return objPath;
    }

    /**
     * zip文件转换成可访问的3d模型文件
     * @param zipPath 压缩包路径
     * @return 返回get3d文件路径
     */
    public static String zipToCollectionModel(String zipPath){
        String returnStr = "";
        if(StringUtils.isBlank(zipPath)){
            return returnStr;
        }
        //解压后的资源路径
        String resourcePath = FileUtil.ftpUrlToFtpPath(zipPath.substring(0,zipPath.lastIndexOf(".")));
        //解压zip文件
        zipPath = FileUtil.ftpUrlToFtpPath(zipPath);
        unzip(zipPath,resourcePath);
        //获取解压后的文件夹中后缀为.obj文件路径
        filesPath = new LinkedList<>();
        getFilesPath(resourcePath);
        String objPath = "";
        for(int i = 0 ;i < filesPath.size();i++){
            if(filesPath.get(i).getPath().endsWith(".obj") || filesPath.get(i).getPath().endsWith(".OBJ")){
                objPath = filesPath.get(i).getPath();
            };
        }

        //通过cmd命令执行转换
        List<String> params = new ArrayList<>();
        params.add("cmd");
        params.add("/c");
        params.add("obj2gltf");
        params.add("-i");
        params.add(objPath.replace("/",File.separator));
        params.add("-o");
        //转换后输出的文件路径
        String outPath = resourcePath+"/gltf";
        params.add((outPath+"/model.gltf").replace("/",File.separator));
        params.add("-s");
        CommandUtil.command(params);

        //检查是否转换成功
        if(!new File(outPath+"/model.gltf").exists()){
            log.error("转码失败，未输出GLTF文件！");
            return returnStr;
        }
        //将文件存储路径下的model.get3d公共文件复制到outPath路径下
        List<String> params2 = new ArrayList<>();
        params2.add("cmd");
        params2.add("/c");
        params2.add("copy");
        params2.add((url+"model.get3d").replace("/",File.separator));
        params2.add(outPath.replace("/",File.separator));
        CommandUtil.command(params2);
        if(!new File(outPath+"/model.get3d").exists()){
            log.error("复制文件失败！");
            return returnStr;
        }
        returnStr = outPath+"/model.get3d";
        return returnStr;
    }

    /**
     * 获取指定文件夹下的所有文件路径集合
     * @param dir
     */
    private static void getFilesPath(String dir) {
        File file = new File(dir);
        if(file.exists()){
            if(file.isDirectory()){
                File[] listFiles = file.listFiles();
                for(int i = 0 ; i < listFiles.length ; i++ ){
                    getFilesPath(listFiles[i].getAbsolutePath());
                }
            }else{
                filesPath.add(file);
            }
        }
    }



    /**
     * 将zip文件解压
     * @param zipPath zip文件路径
     * @param resourcePath 解压到目标路径
     */
    public static void unzip(String zipPath,String resourcePath){
        //判断生成目录是否生成，如果没有就创建
        File pathFile = new File(resourcePath);
        if(!pathFile.exists()){
            pathFile.mkdirs();
        }
        ZipFile zp;
        try{
            //指定编码，否则压缩包里面不能有中文目录
            zp=new ZipFile(zipPath, Charset.forName("gbk"));
            //遍历里面的文件及文件夹
            Enumeration entries=zp.entries();
            while(entries.hasMoreElements()){
                ZipEntry entry= (ZipEntry) entries.nextElement();
                String zipEntryName=entry.getName();
                InputStream in=zp.getInputStream(entry);
                String outPath = (resourcePath+"/"+zipEntryName).replace("/",File.separator);
                //判断路径是否存在，不存在则创建文件路径
                File file = new  File(outPath.substring(0,outPath.lastIndexOf(File.separator)));
                if(!file.exists()){
                    file.mkdirs();
                }
                //判断文件全路径是否为文件夹,如果是,不需要解压
                if(new File(outPath).isDirectory())
                    continue;
                OutputStream out=new FileOutputStream(outPath);
                byte[] bf=new byte[2048];
                int len;
                while ((len=in.read(bf))>0){
                    out.write(bf,0,len);
                }
                in.close();
                out.close();
            }
            zp.close();
        }catch ( Exception e){
            e.printStackTrace();
        }
    }

}
