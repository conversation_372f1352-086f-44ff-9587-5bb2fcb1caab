package com.das.utils.common;

import java.util.HashMap;
import java.util.Map;

/**
 * Description 平台基础常量工具类
 *
 * <AUTHOR>
 * @since 2020/4/20
 */
public class Constants {

    /**
     * 平台用登录session记录，管理端用户登录session
     */
    public static final String MANAGER_USER = "manager_user";

    /**
     * 百度api access_token
     */
    public static final String ACCESS_TOKEN = "access_token";

    /**
     * 平台密码加盐值
     */
    public static final String NAVIGATION_SALT = "navigation_salt";
    public static final int HASH_ITERATIONS = 2;

    /**
     * 数字资源路径
     */
    private static final String DIGIT = "digit/";

    /**
     * 藏品资源路径
     */
    private static final String COLLECTION = "collection/";


    // 系统资源详细路径
    /**
     * 通用路径
     */
    public static final String COMMON = "common/";

    /**
     * 通用路径
     */
    public static final String IMG = "img/";

    /**
     * 视频路径
     */
    public static final String VIDEO = "video/";

    /**
     * 音频路径
     */
    public static final String AUDIO = "audio/";

    /**
     * 文档路径
     */
    public static final String DOCUMENT = "document/";

    /**
     * PDF路径
     */
    public static final String PDF = "pdf/";

    public static final String THREE = "three/";

    /**
     * 三维路径
     */
    public static final String DATA_3D = "data3d/";

    public static final Map<String, String> MODEL_MAP;

    static {
        MODEL_MAP = new HashMap<>();
        MODEL_MAP.put(null, COMMON);
        MODEL_MAP.put("", COMMON);
        MODEL_MAP.put("digit", DIGIT);
        MODEL_MAP.put("collection", COLLECTION);
    }

    public static final Map<String, String> PATH_MAP;

    static {
        PATH_MAP = new HashMap<>();
        PATH_MAP.put(null, COMMON);
        PATH_MAP.put("", COMMON);
        PATH_MAP.put("common", COMMON);
        PATH_MAP.put("img", IMG);
        PATH_MAP.put("video", VIDEO);
        PATH_MAP.put("audio", AUDIO);
        PATH_MAP.put("document", DOCUMENT);
        PATH_MAP.put("data3d", DATA_3D);
        PATH_MAP.put("pdf", PDF);
        PATH_MAP.put("three", THREE);
    }
}
