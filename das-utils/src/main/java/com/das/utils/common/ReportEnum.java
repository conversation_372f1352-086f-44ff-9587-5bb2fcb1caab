package com.das.utils.common;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 自定义报表枚举
 */
@Getter
public enum ReportEnum {

    //行列指标
    TIME_FRAME_FIELD("MCR_INSTORESCOPE","a.time_frame","入藏年代范围","timeframe"),//入藏年代范围
    SAVE_STATUS_FIELD("MCR_PROTECTEDTYPE","b.save_status","保存状态","savestatus"),//保存状态
    COMPLETE_FIELD("MCR_FULLDISABLE_OPTION","b.complete","完残程度","complete"),//完残程度
    SOURCE_FIELD("MCR_COLLECTIONMETHOD_OPTION","b.source","藏品来源类别","source"),//来源
    LEVEL_FIELD("MCR_FCRCLASS_OPTION","a.level","藏品级别","level"),//级别
    CLASSIFY("CLASSIFY_DRM_30","a.classify_number","藏品分类","classify"),//分类号
    AGE("MCR_AGE","a.age","藏品年代","age"),//年代
    TEXTURE("MCR_MATTERTYPE","b.texture","藏品质地","texture"),//质地
    ROOM("ROOM","c.room_id","藏品库房","room"),//库房
    TYPE_FIELD("MCR_CULTURERELICTYPE","a.type","类别（一普）","type"),//类别


    //填充项
    NOT_SELECTED("NOTSELECTED",null,"未定义类型","notselected"),//未选择的
    HE_JI("HEJI",null,"合计","heji");//合计


    private String code; //字典表code码
    private String field; //sql对应字段
    private String desc; //描述
    private String result; //sql做返回字段

    ReportEnum(String code, String field,String desc,String result) {
        this.code = code;
        this.field = field;
        this.desc = desc;
        this.result = result;
    }

    public static ReportEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (ReportEnum reportEnum : ReportEnum.values()) {
            if (StringUtils.isBlank(reportEnum.code)) {
                continue;
            }
            if (StringUtils.equals(reportEnum.code , code)) {
                return reportEnum;
            }
        }
        return null;
    }

}
