package com.das.utils.common;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 日期工具类
 *
 * <AUTHOR>
 * @since  2020-04-24 12:12
 */

public class DateUtil {
    /**
     * 日期对象转字符串
     *
     * @param date 日期
     * @param format 格式化
     * @return String
     */
    public static String formatDate(Date date, String format) {
        String result = "";
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        if (date != null) {
            result = sdf.format(date);
        }
        return result;
    }

    /**
     * 字符串转日期对象
     *
     * @param str 字符串
     * @param format 格式化
     * @return Date
     * @throws Exception 异常
     */
    public static Date formatString(String str, String format) throws Exception {
        if (StringHandle.isEmpty(str)) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.parse(str);
    }

    /**
     * 将2021-06-03T08:16:39.945Z格式时间转成 YYYY-MM-DD HH-MM-SS
     * @param oldDateStr
     * @return
     * @throws ParseException
     */
    public static String FormatDate(String oldDateStr) throws ParseException {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        Date  date = df.parse(oldDateStr);
        SimpleDateFormat df1 = new SimpleDateFormat ("EEE MMM dd HH:mm:ss Z yyyy", Locale.UK);
        Date date1 =  df1.parse(date.toString());
        DateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return df2.format(date1);
    }

    public static String getCurrentDateStr(){
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddhhmmss");
        return sdf.format(date);
    }

    public static String getNextDay() {
        Date date = new Date();
        date.setDate(date.getDate() + 1);
        date.setHours(date.getHours());
        date.setMinutes(date.getMinutes());
        date.setSeconds(date.getSeconds());
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return format.format(date);
    }

    public static String getDay() {
        Date date = new Date();
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return format.format(date);
    }

    public static String getCurrentDatePath() {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd/");
        return sdf.format(date);
    }

    public static String getCurrentDatePathDate() {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(date);
    }

    public static String getFormatDate() {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(date);
    }

    public static String formDate_CN(String date) throws Exception {
        Date d =  formatString(date,"yyyy-MM-dd");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String[] str = sdf.format(d).split("-");
        return str[0] + "年" + str[1] + "月" + str[2] + "日";
    }

}
