package com.das.utils.common;

import com.das.yml.FtpConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;

import java.io.*;
import java.net.*;
import java.util.Enumeration;
import java.util.Map;


/**
 * TODO 系统公共类
 *
 * <AUTHOR>
 * @since 2020/7/22 0022 15:59
 *
 */
@Slf4j
public class SystemUtil {

    private static FtpConfig ftpConfig = (FtpConfig) SpringUtil.getBean("ftpConfig");

    /**
     * 获取登录系统用户信息
     *
     * @return SysUser
     */
    public static Object getLoginUser() {
        return SecurityUtils.getSubject().getSession().getAttribute(Constants.MANAGER_USER);
    }


    /**
     * 统一配置文件存储位置
     *
     * @param type   文件类型分类(默认common类型)
     * @param suffix 文件后缀
     * @return String
     */
    public static String getFilePath(String type, String suffix) {
        if (type == "" || type == null) {
            type = Constants.COMMON;
        } else if (type.equals("img")) {
            type = Constants.COMMON + "0";
        }
        return ftpConfig.getFtpPath() + type + StringHandle.createUUID() + "." + suffix;
    }

    /**
     * 统一配置文件访问位置
     *
     * @param type     文件类型分类(默认common类型)
     * @param fileName 文件名称
     * @return
     */
    public static String getFileURL(String type, String fileName) {
        if (type == "" || type == null) {
            type = Constants.COMMON;
        }
        return ftpConfig.getFtpUrl() + type + fileName;
    }


    /**
     * 获取文件下载地址
     *
     * @param fileName
     * @param type
     * @return
     */
    public static String getDownloadPath(String fileName, String type) {
        return ftpConfig.getFtpPath() + type + fileName;
    }


    /**
     * 发送HTTP请求
     *
     * @param urlParam
     * @param method
     * @param param
     * @return
     * @throws Exception
     */
    public static String sendHttp(String urlParam, String method, Map<String, String> header, String param) {
        try {
            URL url = new URL(urlParam);
            HttpURLConnection urlConnection = (HttpURLConnection) url.openConnection();
            urlConnection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
            urlConnection.setRequestMethod(method);
            urlConnection.setDoInput(true);
            urlConnection.setDoOutput(true);
            header.forEach((k, v) -> {
                urlConnection.setRequestProperty(k, v);
            });
            urlConnection.connect();
            if (!"GET".equals(method)) {
                OutputStream os = urlConnection.getOutputStream();
                OutputStreamWriter osw = new OutputStreamWriter(os);
                BufferedWriter bw = new BufferedWriter(osw);
                bw.write(param);
                bw.flush();
            }

            InputStream is = urlConnection.getInputStream();
            InputStreamReader inputStreamReader = new InputStreamReader(is);
            BufferedReader br = new BufferedReader(inputStreamReader);
            StringBuffer sb = new StringBuffer();
            String line = "";
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }
            log.info(sb.toString());
            return sb.toString();
        } catch (ConnectException e) {
            log.error("连接出错");
        } catch (Exception e) {
            e.printStackTrace();
            return "{message:'error'}";
        }
        return "";
    }

    public static void main(String[] args) {
        try {
            long start = System.currentTimeMillis();
            StringBuffer aa = new StringBuffer();
            for (int i = 0; i < 1000000; i++) {
                aa.append("aa");
            }

            long end = System.currentTimeMillis();
            System.out.println(end - start);

        } catch (Exception e1) {
            e1.printStackTrace();
        }
    }

    /**
     * 获取本机IP地址
     *
     * @return
     */
    public static String getIpAddress() {
        try {
            Enumeration<NetworkInterface> allNetInterfaces = NetworkInterface.getNetworkInterfaces();
            InetAddress ip = null;
            while (allNetInterfaces.hasMoreElements()) {
                NetworkInterface netInterface = (NetworkInterface) allNetInterfaces.nextElement();
                if (netInterface.isLoopback() || netInterface.isVirtual() || !netInterface.isUp()) {
                    continue;
                } else {
                    Enumeration<InetAddress> addresses = netInterface.getInetAddresses();
                    while (addresses.hasMoreElements()) {
                        ip = addresses.nextElement();
                        if (ip != null && ip instanceof Inet4Address) {
                            return ip.getHostAddress();
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("IP地址获取失败" + e.toString());
        }
        return "";
    }

}
