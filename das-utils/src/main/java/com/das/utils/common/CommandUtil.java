package com.das.utils.common;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.List;

/**
 * 命令行工具
 *
 * <AUTHOR>
 */
@Slf4j
public class CommandUtil {

    /**
     * 对cmd的封装
     *
     * @param params 参数列表
     * @return 退出码 0 为 正确；其余为错误
     */
    public static int command(List<String> params) {
        if (params == null || params.isEmpty()) {
            return -1;
        }
        // 1.拼装调用可执行应用程序的参数
        int paramSize = params.size();
        String[] cmd = params.toArray(new String[paramSize]);

        // 2.调用可执行应用程序
        ProcessBuilder processBuilder = new ProcessBuilder(cmd);
        processBuilder.redirectErrorStream(true);
        int exitCode = -1;
        try {
            Process process = processBuilder.start();
            BufferedReader inBr = new BufferedReader(new InputStreamReader(process.getInputStream(), "UTF-8"));
            String lineStr;
            while ((lineStr = inBr.readLine()) != null) {
                log.debug("program print information: {}", lineStr);
            }
            inBr.close();
            exitCode = process.waitFor();
        } catch (Exception e) {
            log.info("program process error: {}", e.getMessage());
            return -1;
        }
        return exitCode;
    }
}
