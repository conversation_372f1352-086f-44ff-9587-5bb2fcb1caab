package com.das.utils.common;

import com.das.yml.FtpConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.jaudiotagger.audio.mp3.MP3AudioHeader;
import org.jaudiotagger.audio.mp3.MP3File;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.Objects;

/**
 * Description TODO
 *
 * <AUTHOR>
 * @since 2020/4/28
 */
@Slf4j
public class FileUtil {

    private static FtpConfig ftpConfig = (FtpConfig) SpringUtil.getBean("ftpConfig");

    public static MultipartFile base64toMultipart(String data, String fileName) {
        if (isBase64(data)) {
            String[] baseStr = data.split((","));
            byte[] b = Base64.decodeBase64(baseStr[1]);
            for (int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {
                    b[i] += 256;
                }
            }
            return new Base64MultipartFile(
                    b, baseStr[0],
                    fileName == null
                            ? ("base64File." + getSuffixByBase64(data))
                            : fileName
            );
        }
        return null;
    }

    private static boolean isBase64(String strBase64) {
        return strBase64 != null
                && !strBase64.isEmpty()
                && ("data:").equals(strBase64.substring(0, 5))
                && strBase64.contains(";base64,");
    }

    private static String getSuffixByBase64(String strBase64) {
        if (isBase64(strBase64)) {
            return strBase64.substring(strBase64.indexOf("data:") + 5, strBase64.indexOf(";base64,")).split(("/"))[1];
        }
        return null;
    }

    public static String uploadFile(String path, MultipartFile file) throws IOException {
        String fileName = file.getOriginalFilename();
        String suffix = Objects.requireNonNull(fileName).substring(fileName.lastIndexOf("."));
        String newFileName = StringHandle.createUUID() + suffix;
        File dest = new File(path + newFileName);
        file.transferTo(dest);

        return newFileName;
    }

    /**
     * 获取资源上传路径
     *
     * @return String
     */
    public static String getResourcePath(String fileName, String file, String temp) throws Exception {
        MultipartFile multipartFile = FileUtil.base64toMultipart(file, fileName);
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        String uuid = StringHandle.createUUID();
        String path = ftpConfig.getFtpPath() + temp + uuid + "." + suffix;
        String resourcePath = ftpConfig.getFtpUrl() + temp + uuid + "." + suffix;
        //UploadHandle.uploadFile(multipartFile, path);
        return resourcePath;
    }

    /**
     * 获取资源上传路径
     *
     * @return String
     */
    public static String getPath(String fileName, String file, String temp) throws Exception {
        MultipartFile multipartFile = FileUtil.base64toMultipart(file, fileName);
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        String uuid = StringHandle.createUUID();
        String path = ftpConfig.getFtpPath() + temp + uuid + "." + suffix;

        return path;
    }

    /**
     * 判断音频格式是否正确
     *
     * @return String
     */
    public static boolean getAudioPath(String audio) throws Exception {
        if (audio.equalsIgnoreCase("CD") || audio.equalsIgnoreCase("WAVE")
                || audio.equalsIgnoreCase("AIFF") || audio.equalsIgnoreCase("MPEG")
                || audio.equalsIgnoreCase("MP3") || audio.equalsIgnoreCase("MPEG-4")
                || audio.equalsIgnoreCase("MIDI") || audio.equalsIgnoreCase("WMA")
                || audio.equalsIgnoreCase("REALAUDIO") || audio.equalsIgnoreCase("VQF")
                || audio.equalsIgnoreCase("OGGVORBIS") || audio.equalsIgnoreCase("AMR")
                || audio.equalsIgnoreCase("APE") || audio.equalsIgnoreCase("FLAC")
                || audio.equalsIgnoreCase("AAC") || audio.equalsIgnoreCase("WAV")
        ) {
            return true;
        } else {
            return false;
        }

    }

    /**
     * 判断视频格式是否正确
     *
     * @return String
     */
    public static boolean getVideoPath(String audio) throws Exception {
        if (audio.equalsIgnoreCase("avi") || audio.equalsIgnoreCase("wmv")
                || audio.equalsIgnoreCase("mpg") || audio.equalsIgnoreCase("MPEG")
                || audio.equalsIgnoreCase("mov") || audio.equalsIgnoreCase("rm")
                || audio.equalsIgnoreCase("ram") || audio.equalsIgnoreCase("swf")
                || audio.equalsIgnoreCase("flv") || audio.equalsIgnoreCase("mp4")) {
            return true;
        } else {
            return false;
        }

    }


    public static int getMp3FileTime(MultipartFile multipartFile) throws Exception {
        File file = null;
        try {
            InputStream inputStream = multipartFile.getInputStream();
            if (multipartFile.getOriginalFilename() != null) {
                file = new File(multipartFile.getOriginalFilename());
                FileUtil.inputStreamToFile(inputStream, file);
            }
            MP3File mp3File = new MP3File(file);
            MP3AudioHeader audioHeader = (MP3AudioHeader) mp3File.getAudioHeader();
            return audioHeader.getTrackLength();
        } catch (Exception e) {
            log.error(StringHandle.getExceptionMessage("读取视频长度失败", e));
        } finally {
            try {
                if (file != null) {
                    file.delete();
                }
            } catch (Exception e) {
                log.error(StringHandle.getExceptionMessage("读取视频长度失败", e));
            }
        }
        return 0;
    }

    private static void inputStreamToFile(InputStream ins, File file) {
        try {
            OutputStream os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取系统路径
     *
     * @return
     */
    public static String getFtpPath() {
        return ftpConfig.getFtpPath();
    }

    /**
     * 删除系统文件
     *
     * @param loadPath 文件加载路径
     * @return boolean
     */
    public static boolean deleteFile(String loadPath) {
        String path = ftpConfig.getFtpPath() + loadPath.replaceAll(ftpConfig.getFtpUrl(), "");
        File file = new File(path);
        if (file.exists() && file.isFile()) {
            return file.delete();
        }
        return false;
    }

    public static void writeFileToResponse(HttpServletResponse response, File file){
        OutputStream outp = null;
        FileInputStream in = null;
        try {
            outp = response.getOutputStream();
            in = new FileInputStream(file);
            byte[] b = new byte[1024];
            int i = 0;
            while ((i = in.read(b)) > 0) {
                outp.write(b, 0, i);
            }
            outp.flush();
        } catch (Exception e) {
        } finally {
            if (in != null) {
                try {
                    in.close();
                    in = null;
                } catch (IOException e) {
                    e.printStackTrace();
                }
                if (outp != null) {
                    try {
                        outp.close();
                        outp = null;
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    /**
     * 虚拟路径转换成文件服务真实路径
     *
     * @return
     */
    public static String ftpUrlToFtpPath(String loadPath) {
        return loadPath.replace(ftpConfig.getFtpUrl(), ftpConfig.getFtpPath());
    }

}
