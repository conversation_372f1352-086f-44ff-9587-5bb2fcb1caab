package com.das.utils.common;

/*import com.sun.image.codec.jpeg.JPEGCodec;
import com.sun.image.codec.jpeg.JPEGEncodeParam;
import com.sun.image.codec.jpeg.JPEGImageEncoder;*/

import com.das.yml.FtpConfig;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * TODO 图片处理工具类
 *
 * <AUTHOR>
 * @since 2020/9/27 0:00
 */
@Slf4j
public class ImgUtil {

    private static FtpConfig ftpConfig = (FtpConfig) SpringUtil.getBean("ftpConfig");

    /**
     * 压缩指定大小图片
     *
     * @param oldPath  临时图片路径
     * @param copyPath 压缩图片保存路径
     * @param width    宽度
     * @param height   高度
     * @param quality  高清度
     * @return
     * @throws Exception
     */
    public static Boolean zipWidthHeightImageFile(String oldPath, String copyPath, int width, int height, float quality) {
        Boolean sta = false;
        float minScale = 0.1f;
        File oldFile = new File(oldPath);
        File newFile = new File(copyPath);
        if (oldFile == null) {
            return null;
        }
        String newImage = null;
        try {
            /** 对服务器上的临时文件进行处理 */
            Image srcFile = ImageIO.read(oldFile);
            float w = srcFile.getWidth(null) * minScale;
            float h = srcFile.getHeight(null) * minScale;
            w = w < 164 ? 164 : w;
            h = h < 120 ? 120 : h;
            /** 宽,高设定 */
            BufferedImage tag = new BufferedImage((int) w, (int) h, BufferedImage.TYPE_INT_RGB);
            tag.getGraphics().drawImage(srcFile, 0, 0, (int) w, (int) h, null);
            //String filePrex = oldFile.substring(0, oldFile.indexOf('.'));
            /** 压缩后的文件名 */
            //newImage = filePrex + smallIcon+ oldFile.substring(filePrex.length());

            /** 压缩之后临时存放位置 */
            FileOutputStream out = new FileOutputStream(newFile);

            /*JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(out);
            JPEGEncodeParam jep = JPEGCodec.getDefaultJPEGEncodeParam(tag);
            *//** 压缩质量 *//*
            jep.setQuality(quality, true);
            encoder.encode(tag, jep);*/
            out.close();
            sta = true;
        } catch (Exception e) {
            e.printStackTrace();
            sta = false;
        }
        return sta;
    }


    public static void main(String[] aa) {
//        zipWidthHeightImageFile("E:\\Temp\\1.PNG", "E:\\Temp\\1-1.PNG",291,738, 1f);
        String oldPath = "E:/Code/1.tif";
        String newPath = "E:/Code/1.png";
        tiffToPng(oldPath, newPath, "png");
    }

    /**
     * 将tif图片转换成其他类型图片
     *
     * @param oldPath tif图片
     * @param newPath 转换后的图片
     * @param type    转换的类型
     */
    public static void tiffToPng(String oldPath, String newPath, String type) {
        try {
            long time1 = System.currentTimeMillis();
            BufferedImage bufferegImage = ImageIO.read(new File(oldPath));
            ImageIO.write(bufferegImage, type, new File(newPath));//可以是png等其它图片格式
            long time2 = System.currentTimeMillis();
            System.out.println("耗时: " + (time2 - time1) / 1000 + "s");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取缩略图
     *
     * @param loadPath
     * @return
     */
    public static String getThumbnail(String loadPath) {
        String fileName = loadPath.substring(loadPath.lastIndexOf("/") + 1);
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        String resourcePath = Constants.PATH_MAP.get("img");
        try {
            String filePath = SystemUtil.getFilePath(resourcePath, suffix);
            File dest = new File(filePath);
            if (!dest.getParentFile().exists()) {
                dest.getParentFile().mkdirs();
            }
            FileInputStream fileInputStream =
                    new FileInputStream(loadPath.replace(ftpConfig.getFtpUrl(), ftpConfig.getFtpPath()));
            try {
                Thumbnails.of(fileInputStream)
                        .scale(0.25f)
                        .toFile(filePath);
            } catch (IOException e) {
                log.error("压缩图片失败,{}", loadPath);
                return SystemUtil.getFileURL(resourcePath, loadPath.substring(loadPath.lastIndexOf("/") + 1));
            }
            return SystemUtil.getFileURL(resourcePath, filePath.substring(filePath.lastIndexOf("/") + 1));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return "";
    }

}
