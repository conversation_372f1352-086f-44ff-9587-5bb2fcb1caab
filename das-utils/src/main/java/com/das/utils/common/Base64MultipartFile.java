package com.das.utils.common;

import org.springframework.web.multipart.MultipartFile;

import java.io.*;

/**
 * Description TODO
 *
 * <AUTHOR>
 * @since 2020/4/28
 */
public class Base64MultipartFile implements MultipartFile {
    private final byte[] imgContent;

    private final String header;

    private final String fileName;

    public Base64MultipartFile(byte[] imgContent, String header, String fileName){
        this.imgContent = imgContent;
        this.header = header.split(";")[0];
        this.fileName = fileName;
    }

    @Override
    public String getName() {
        return System.currentTimeMillis() + Math.random() + "." + header.split("/")[1];
    }

    @Override
    public String getOriginalFilename() {
        return fileName;
    }

    @Override
    public String getContentType() {
        return header.split(":")[1];
    }

    @Override
    public boolean isEmpty() {
        return imgContent == null || imgContent.length == 0;
    }

    @Override
    public long getSize() {
        return imgContent.length;
    }

    @Override
    public byte[] getBytes() throws IOException {
        return imgContent;
    }

    @Override
    public InputStream getInputStream() throws IOException {
        return new ByteArrayInputStream(imgContent);
    }

    @Override
    public void transferTo(File dest) throws IOException, IllegalStateException {
        new FileOutputStream(dest).write(imgContent);
    }
}
