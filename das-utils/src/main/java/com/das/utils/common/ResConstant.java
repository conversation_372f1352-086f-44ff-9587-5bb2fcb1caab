package com.das.utils.common;

public class ResConstant {
    //审核相关状态
    public final static int NO_SUBMIT = 0; //未提交
    public final static int CHECKING = 1; //审核中
    public final static int PASS = 2;    //审核通过
    public final static int NO_PASS = 3; //审核不通过
    public final static int REVOKE = 4; //撤销
    public final static int RETURN = 5; //退回重审

    //审核类型
    public final static int TYPE = 73; //资源申请审核

    public final static String CHECK_FLAG = "000000000000";//审核标记初始状态；

    public final static String INIT_CHECK_FLAG = "100000000000";//一审；

    //审核意见
    public final static int AGREE = 0;//同意
    public final static int DISAGREE = 1;//不同意

    //审核流程
    public final static int CheckFlow_69 = 69; //征集审核
    public final static int CheckFlow_71 = 71; //入馆审核
    public final static int CheckFlow_72 = 72; //鉴定审核
    public final static int CheckFlow_73 = 73; //申请审核
    public final static int CheckFlow_75 = 75; //入藏审核
    public final static int CheckFlow_76 = 76; //登录审核
    public final static int CheckFlow_204 = 204; //出入库审核
    public final static int CheckFlow_205 = 205; //注销审核
    public final static int CheckFlow_265 = 265; //在展文物审核


}
