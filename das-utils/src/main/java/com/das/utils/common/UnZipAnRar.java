package com.das.utils.common;

import com.github.junrar.Archive;
import com.github.junrar.rarfile.FileHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * @author: SL
 * @create: 2021-09-16 15:28
 **/
public class UnZipAnRar {

    private static final Logger logger = LoggerFactory.getLogger(UnZipAnRar.class);

    /**
     * 解压
     *
     * @param zipFile 带解压文件
     * @param desDirectory 解压到的目录
     * @throws Exception
     */
    public static void unZip(File zipFile, String desDirectory) throws Exception {
        File desDir = new File(desDirectory);
        if (!desDir.exists()) {
            boolean mkdirSuccess = desDir.mkdir();
            if (!mkdirSuccess) {
                throw new Exception("创建解压目标文件夹失败");
            }
        }
        // 读入流
        ZipInputStream zipInputStream = new ZipInputStream(new FileInputStream(zipFile));
        // 遍历每一个文件
        ZipEntry zipEntry = zipInputStream.getNextEntry();
        long startTime = System.currentTimeMillis();
        logger.info("开始解压...");
        while (zipEntry != null) {
            // 文件夹
            String unzipFilePath = desDirectory + File.separator + zipEntry.getName();
            if (zipEntry.isDirectory()) {
                // 直接创建
                mkdir(new File(unzipFilePath));
            } else { // 文件
                File file = new File(unzipFilePath);
                // 创建父目录
                mkdir(file.getParentFile());
                // 写出文件流
                BufferedOutputStream bufferedOutputStream =
                        new BufferedOutputStream(new FileOutputStream(unzipFilePath));
                byte[] bytes = new byte[1024];
                int readLen;
                while ((readLen = zipInputStream.read(bytes)) != -1) {
                    bufferedOutputStream.write(bytes, 0, readLen);
                }
                bufferedOutputStream.close();
            }
            zipInputStream.closeEntry();
            zipEntry = zipInputStream.getNextEntry();
        }
        long endTime = System.currentTimeMillis();
        logger.info("解压完成...共执行了{}秒",(endTime-startTime)/1000);
        zipInputStream.close();
    }

    // 如果父目录不存在则创建
    private static void mkdir(File file) {
        if (null == file || file.exists()) {
            return;
        }
        mkdir(file.getParentFile());
        file.mkdir();
    }

    public static void unRar(File rarFile, String outDir) throws Exception {
        String fileName = "";
        Archive archive = new Archive(new FileInputStream(rarFile));
        FileHeader fileHeader = archive.nextFileHeader();
        long startTime = System.currentTimeMillis();
        logger.info("开始解压...");
        while (fileHeader != null) {
            if (fileHeader.isDirectory()) {
                fileHeader = archive.nextFileHeader();
                continue;
            }
            fileName = outDir + "/" + fileHeader.getFileNameString();
            File out = new File(fileName);
            if (!out.exists()) {
                if (!out.getParentFile().exists()) {
                    out.getParentFile().mkdirs();
                }
                out.createNewFile();
            }
            FileOutputStream os = new FileOutputStream(out);
            archive.extractFile(fileHeader, os);

            os.close();
            fileHeader = archive.nextFileHeader();
        }
        long endTime = System.currentTimeMillis();
        logger.info("解压完成...共执行了{}秒",(endTime-startTime)/1000);
        archive.close();
    }
}