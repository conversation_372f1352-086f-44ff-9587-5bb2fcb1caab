package com.das.utils.common;

public class CangPConstant {
    //审核相关状态
    public final static int NO_SUBMIT = 0; //未提交
    public final static int CHECKING = 1; //审核中
    public final static int PASS = 2;    //审核通过
    public final static int NO_PASS = 3; //审核不通过
    public final static int REVOKE = 4; //撤销
    public final static int RETURN = 5; //退回重审

    public final static String INIT_CHECK_FLAG = "000000000000";//审核标记初始状态；

    public final static String CHECK_FLAG_START = "100000000000";//审核标记初始状态；

    //审核意见
    public final static int SUBMIT = 1;//提交
    public final static int AGREE = 2;//同意
    public final static int DISAGREE = 3;//不同意
    public final static int RETRIAL = 5;//退回重审

    //藏品从征集到入藏的审批状态
    public final static int ZJ_CHECK = 1;  //藏品征集審核過程中
    public final static int ZJ_PASS = 2;   //藏品征集審核通過

    //public final static int JD_CHECK = 3;  //鉴定审核中
    //public final static int JD_PASS =4;  //鉴定审核通过

    public final static int RG_CHECK = 3;  //入馆审核中
    public final static int RG_PASS =4;  //入館審核通過

    public final static int RC_CHECK = 5;  //入藏审核中
    public final static int RC_PASS =6;  //入藏審核通過

    //凭证跟藏品关联类型
    public final static int REF_COLLECT = 1; //征集关联类型
    public final static int REF_MUSEUM = 2; //入馆关联
    public final static int REF_IDENTIFICATION =3; //鉴定关联类型
    public final static int REF_COLLECTING = 4; //入藏关联

    //藏品附件类型
    public final static int COL_PICTURE = 1; //图片
    public final static int COL_RESOURCE = 2; //附件

    //藏品入馆状态
    public final static int COL_OUT_MUSEUM = 1;   //未入馆
    public final static int COL_INTO_MUSEUM = 2;    //已入馆
    public final static int COL_ALLOT_KEEPER = 3;    //已分配保管员

    //藏品状态
    public final static int COL_RESULT_NO_IN = 1;   //未入藏
    public final static int COL_RESULT_ALLOT = 1;   //已分配保管员
    public final static int COL_RESULT_CHECK = 1;   //已查收
    public final static int COL_RESULT_RETURN = 1;   //已退回

    //藏品入馆方式
    public final static int ZJ_INTO = 1;   //征集录入
    public final static int RG_INTO = 2;    //入馆录入
    public final static int RC_INTO = 3;    //入藏录入

    //藏品登陆的相关藏品状态
    public final static int NO_LAND = 1;    //未登录
    public final static int LAND = 2;    //已登录
    public final static int IN_ROOM = 3;    //已入库
    public final static int OUT_ROOM = 4;    //出库
    public final static int INNER_USE = 5;    //内部提供
    public final static int CANCEL  = 6;    //已注销

    //藏品入柜状态
    public final static int COL_OUT_STATUS = 1;//未入柜
    public final static int COL_IN_STATUS = 2;//已入柜

    //藏品登陆附件的关联类型（1：研究著录；2：鉴定信息；3：损坏记录；4:装裱记录；5：附属物；6：拓片信息；7：绘图；8：器物；9：古籍文献；10：图片；11：视频；12：音频）
    public final static int REF_COLLECTION_BOOKS = 1;
    public final static int REF_COLLECTION_IDENTIFY = 2;
    public final static int REF_COLLECTION_DAMAGE = 3;
    public final static int REF_COLLECTION_MOUNT = 4;
    public final static int REF_COLLECTION_APPENDAGES = 5;
    public final static int REF_COLLECTION_RUBBING = 6;
    public final static int REF_COLLECTION_MAPPING = 7;
    public final static int REF_COLLECTION_UTENSILS = 8;
    public final static int REF_COLLECTION_ANCIENT_BOOKS = 9;
    public final static int REF_COLLECTION_PICTURE = 10;
    public final static int REF_COLLECTION_VIDEO = 11;
    public final static int REF_COLLECTION_AUDIO = 12;

    public final static String ORGANIZATION_NAME = "安徽楚文化博物馆";

    //年代根类别
    public final static String BEAN_AGE_FIELD = "MCR_AGE";
    //年代第一层类别
    public final static String FIRST_AGE_FIELD = "MCR_AGE_OPTION";

    //质地根类别
    public final static String BEAN_TEXTURE_FIELD = "MCR_MATTERTYPE";
    //质地第一类别
    public final static String FIRST_TEXTURE_FIELD = "MCR_MATTERTYPE_D_OPTION";
    //有机质
    public final static String TEXTURE_ORGANIC_FIELD = "MCR_MATTERTYPE_D_Y_OPTION";
    public final static String TEXTURE_INORGANIC_FIELD = "MCR_MATTERTYPE_D_W_OPTION";

    //分类号
    public final static String COLLECTION_TYPE = "MCR_CULTURERELICTYPE";
    //级别
    public final static String LEVEL_FIELD = "MCR_FCRCLASS_OPTION";
    //著作级别
    public final static String BOOK_LEVEL = "BOOK_LEVEL";
    //文物类别
    public final static String TYPE_FIELD = "MCR_CULTURERELICTYPE";
    //藏品级别
    public final static String CP_LEVEL_FIELD = "MCR_FCRCLASS_OPTION";
    //藏品完残程度
    public final static String CP_COMPLETE_FIELD = "MCR_FULLDISABLE_OPTION";
    //藏品来源
    public final static String CP_SOURCE_FIELD = "MCR_COLLECTIONMETHOD_OPTION";
    //藏品分类号
    public final static String CLASSIFY_DRM_30 = "CLASSIFY_DRM_30";
    //入藏年代范围
    public final static String TIME_FRAME_FIELD = "MCR_INSTORESCOPE";
    //质量范围
    public final static String QUALITY_FIELD = "MCR_MASSSCOPT";
    //质量单位
    public final static String QUALITY_UNIT_FIELD = "MCR_MASSUNIT_OPTION";
    //来源
    public final static String SOURCE_FIELD = "MCR_COLLECTIONMETHOD_OPTION";
    //保存状态
    public final static String SAVE_STATUS_FIELD = "MCR_PROTECTEDTYPE";
    //完残程度
    public final static String COMPLETE_FIELD = "MCR_FULLDISABLE_OPTION";

    //藏品展览类别
    public final static String EXHIBITION_TYPE = "EXHIBITION_TYPE";

    //根节点可展开
    public final static String IS_LAST_CHILD_0 = "0";

    //叶子节点不可展开
    public final static String IS_LAST_CHILD_1 = "1";

    //中国历史学年代
    public final static String MCR_AGE_LISHI_OPTION = "457";

    //统计分类字段
    public final static String[] RuinsType = new String[]{"可移动文物", "不可移动文物", "历史地点", "古道路"};

    public final static String[] ReportType = new String[]{"文档", "图片"};

    //单一质地
    public final static String TEXTURE = "MCR_MATTERTYPE_D_OPTION";

    //三维模型标识后缀名
    public final static String THREETYPE = "get3d";

    //数据小类
    public final static String MINORTYPE = "minorType";

    //发觉时间
    public final static String EXCAVATEAGE = "excavateAge";

    //考古学年代
    public final static String RUINSAGE = "ruinsAge";
    //考古学年代标志
    public final static int RUINSAGE_ID = 2;



    //仓库出入库
    public final static int STORAGE_STATUS = 0; //在库
    public final static int PRE_STORAGE_STATUS = 1; //预出库
    public final static int OUT_STORAGE_STATUS = 2; //出库
    public final static int IN_STORAGE_STATUS = 3; //入库

    public final static int OUT_RELATION_TYPE = 6; //出库
    public final static int IN_RELATION_TYPE = 7; //入库

    public final static int PUBLISH = 1; //发布

    public final static int UNPUBLISH = 2; //未发布


}
