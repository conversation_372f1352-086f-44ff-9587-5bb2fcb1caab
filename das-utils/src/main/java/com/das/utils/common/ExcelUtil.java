package com.das.utils.common;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.merge.LoopMergeStrategy;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @Title: excel工具类
 * @Description:
 */
@Slf4j
public class ExcelUtil {

    /**
     * excel后缀
     */
    public static final String XLSX = ".xlsx";
    public static final String XLS = ".xls";

    /**
     * 按模型导出Excel
     *
     * @param response  response
     * @param list      数据集
     * @param fileName  文件名
     * @param sheetName sheet
     * @param clazz     模型
     */
    public static void export(HttpServletResponse response, List list, String fileName, String sheetName, Class clazz) {
        try {
            if (StringUtils.isEmpty(fileName)) {
                fileName = String.valueOf(System.currentTimeMillis());
            }

            if (StringUtils.isEmpty(sheetName)) {
                sheetName = "sheet";
            }
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            //设置头居中
            headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            //内容策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            //设置 水平居中
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                    new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            // 每隔2行会合并 把eachColumn 设置成 3 也就是我们数据的长度，所以就第一列会合并。当然其他合并策略也可以自己写
            // 2代表合并2行，1是索引 ，从第二个字段开始合并 序号字段不合并。
            LoopMergeStrategy loopMergeStrategy = new LoopMergeStrategy(2, 2);
            response.setHeader("Content-disposition", "attachment;filename="
                    + URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()) + XLSX);
            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream(), clazz)
                    .autoCloseStream(Boolean.TRUE)
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .sheet(sheetName)
                    .doWrite(list);
        } catch (IOException e) {
            log.error("export IOException: ", e);
        }
    }

}
