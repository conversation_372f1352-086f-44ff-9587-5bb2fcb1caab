package com.das.utils.common;

import org.apache.shiro.crypto.hash.Md5Hash;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.UUID;

/**
 * Description TODO
 *
 * <AUTHOR>
 * @since 2020/4/20
 */
public class StringHandle {
    /**
     * 生成MD5密码
     *
     * @return String
     */
    public static String createMD5(String passWord, String userName) {
        if (passWord == null) {
            return null;
        }
        return new Md5Hash(passWord, userName + Constants.NAVIGATION_SALT, Constants.HASH_ITERATIONS).toString();
    }

    /**
     * 生成32位uuid
     *
     * @return String
     */
    public static String createUUID() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    /**
     * 获取文件的真实名称
     *
     * @param file 文件
     * @return path
     */
    public static String getRealFileName(MultipartFile file) {
        String filename = file.getOriginalFilename();
        if (StringUtils.isEmpty(filename)) {
            return null;
        }
        int unixSep = filename.lastIndexOf('/');
        int winSep = filename.lastIndexOf('\\');
        int pos = (winSep > unixSep ? winSep : unixSep);
        if (pos != -1) {
            filename = filename.substring(pos + 1);
        }
        return filename;
    }

    /**
     * 获取文件类型
     *
     * @param suffix 文件后缀
     * @return String
     */
    public static String getFileType(String suffix) {
        switch (suffix) {
            case "doc":
            case "docx":
                return "文档";
            case "mp3":
            case "mp4":
                return "音频";
            case "png":
            case "jpg":
                return "图片";
            default:
                break;
        }
        return "其它";
    }

    /**
     * 获取异常详细信息
     *
     * @param msg 异常介绍
     * @param e   异常类
     * @return String
     */
    public static String getExceptionMessage(String msg, Exception e) {
        return "[" + msg + "]" + getExceptionMessage(e);
    }

    private static String getExceptionMessage(Exception e) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        e.printStackTrace(pw);
        StringBuffer buffer = sw.getBuffer();
        try {
            sw.close();
            pw.close();
        } catch (IOException ex) {
            ex.printStackTrace();
        }
        return buffer.toString();
    }

    /**
     * 判断是否为空字符串
     *
     * @param str 字符串
     * @return boolean
     */
    public static boolean isEmpty(String str) {
        return str == null || "".equals(str.trim());
    }
    /**
     * 判断是否不是空
     * @param str 字符串参数
     * @return boolean
     */
    public static boolean isNotEmpty(String str){
        return  (str!=null)&&!"".equals(str.trim());
    }
}
