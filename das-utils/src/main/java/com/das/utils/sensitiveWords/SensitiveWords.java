package com.das.utils.sensitiveWords;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * Description: 读取敏感词库类
 * @since 2019/9/16
 */
public class SensitiveWords {
    private static final Logger logger = LoggerFactory.getLogger(SensitiveWordUtil.class);
    public static Set<String> getSensitiveWords() {
        Set<String> sensitiveWordSet = new HashSet<>();
        //屏蔽的是liunx敏感词汇路径
//        String pathRoot = new File("").getAbsolutePath();
//        String newPathRoot = pathRoot.replace("bin", "");
//        String path = newPathRoot + "webapps/grottoes/WEB-INF/classes/words.txt";
        try {
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(
                 new FileInputStream(new File("src/main/resources/words.txt")),"UTF-8"));
                  //  new FileInputStream(new File(path)), "UTF-8"));
            String lineTxt = null;
            while ((lineTxt = bufferedReader.readLine()) != null) {
                sensitiveWordSet.add(lineTxt);
            }
            bufferedReader.close();
        } catch (Exception e) {
            logger.error("读取敏感词库失败...");
        }
        return sensitiveWordSet;
    }
}
