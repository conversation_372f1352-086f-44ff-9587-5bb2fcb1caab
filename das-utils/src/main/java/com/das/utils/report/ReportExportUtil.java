package com.das.utils.report;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.fastjson.JSON;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


@Slf4j
public class ReportExportUtil {

    /**
     *
     * @param filePath 文件名
     * @param response
     * @param head 表头
     * @param data 数据
     * @param handler 单元格样式
     * @param mergeColumeIndex 需要合并的列
     * @param mergeRowIndex 需要合并的行（从第几行开始）
     */
    public static void getFilePart(String filePath ,HttpServletResponse response, List head , List data , CellWriteHandler handler, int[] mergeColumeIndex, int mergeRowIndex) {
        ServletOutputStream outputStream = null;
        try {
            String fileName = filePath;
            fileName = URLEncoder.encode(fileName , "UTF-8");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition" , "attachment;fileName=" + fileName + ".xls" + ";fileName*=utf-8''" + fileName + ".xls");
            outputStream = response.getOutputStream();
            EasyExcel
                    .write(outputStream)
                    .excelType(ExcelTypeEnum.XLS)
                    .autoCloseStream(Boolean.TRUE)
                    .head(head)
                    .registerWriteHandler(new ExcelFillCellMergeStrategy(mergeRowIndex, mergeColumeIndex))
                    .registerWriteHandler(handler)
                    .registerWriteHandler(new CustomCellWriteHandler())// 自定义列宽度，有数字会
                    .sheet(filePath)
                    .doWrite(data);
        } catch (Exception e) {
            log.error("导出异常" , e);
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("流关闭异常" , e);
                }
            }
        }
    }


    /***
     * 模仿log.info的方法,将参数传入括号内
     * @param msg
     * @param params
     * @return
     */
    public static String formatHead(String msg , Object... params) {
        if (StringUtils.isBlank(msg)) {
            return msg;
        }
        return StrUtil.format(msg , params);
    }

    public static List<List<Object>> buildData(Map<String, String> headMap , List dataMap) {
        List<List<Object>> resultList = new ArrayList<>();
        for (Object o : dataMap) {
            if (o == null) {
                continue;
            }
            List<Object> singleRow = new ArrayList<>();
            Map map = JSON.parseObject(JSON.toJSONString(o) , Map.class);
            headMap.keySet().forEach(s -> singleRow.add(setEmptyIfNull(map.get(s))));
            resultList.add(singleRow);
        }
        return resultList;
    }

    public static Object setEmptyIfNull(Object o) {
        if (o == null) {
            return StringUtils.EMPTY;
        }
        return o;
    }


    /**
     * 构建二段式的头
     *
     * @param topHead    第一段头
     * @param secondHead 第二段头
     * @return 构建后的头
     */
    public static List<List<String>> buildSecondHead(String topHead , List<String> secondHead) {
        topHead = topHead == null ? StringUtils.EMPTY : topHead;
        if (secondHead == null) {
            secondHead = new ArrayList<>();
        }
        List<List<String>> resultHead = new ArrayList<>();
        for (int i = 0; i < secondHead.size(); i++) {
            resultHead.add(Arrays.asList(topHead , secondHead.get(i)));
        }
        return resultHead;
    }

    /**
     * 构建三段式的头
     *
     * @param topHead    第一段头
     * @param secondHead 第二段头
     * @param thirdHead  第三段头
     * @return 构建后的头
     */
    public static List<List<String>> buildHead(String topHead , List<String> secondHead , List<String> thirdHead) {
        topHead = topHead == null ? StringUtils.EMPTY : topHead;
        if (secondHead == null) {
            secondHead = new ArrayList<>();
        }
        if (thirdHead == null) {
            thirdHead = new ArrayList<>();
        }
        List<List<String>> resultHead = new ArrayList<>();
        for (int i = 0; i < thirdHead.size(); i++) {
            resultHead.add(Arrays.asList(topHead , getSecondHead(i , secondHead , thirdHead) , thirdHead.get(i)));
        }
        return resultHead;
    }

    private static String getSecondHead(int thirdHeadIndex , List<String> secondHead , List<String> thirdHead) {
        return getSecondHead(thirdHeadIndex , secondHead , thirdHead.size());
    }

    private static String getSecondHead(int thirdHeadIndex , List<String> secondHead , int thirdHeadSize) {
        boolean secondSizeIsZero = secondHead.size() == 0;
        if (secondSizeIsZero) {
            return StringUtils.EMPTY;
        }
        if (thirdHeadSize == 0) {
            return StringUtils.EMPTY;
        }
        int avg = thirdHeadSize / secondHead.size();
        //看下thirdHeadIndex是否超出范围了
        if (thirdHeadIndex > thirdHeadSize - 1) {
            log.warn("表头已超出第三个head的范围,取最后一个");
            return secondHead.get(secondHead.size() - 1);
        }
        int position = thirdHeadIndex / avg;
        if (position > secondHead.size() - 1) {
            return secondHead.get(secondHead.size() - 1);
        }
        return secondHead.get(position);
    }
}
