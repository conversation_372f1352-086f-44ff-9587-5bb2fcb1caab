package com.das.utils.report;

import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.util.StyleUtil;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.AbstractCellStyleStrategy;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.ArrayList;
import java.util.List;

/**
 * 头部样式设置
 */
public class CommonHeadStyleStrategy extends AbstractCellStyleStrategy {

    public static final String FONT_NAME = "Calibri";

    private WriteCellStyle headWriteCellStyle;
    private List<WriteCellStyle> contentWriteCellStyleList;

    private CellStyle headCellStyle;
    private List<CellStyle> contentCellStyleList;

    private int[] mergeColumnIndex = {0};
    private int mergeRowIndex = 1;

    public CommonHeadStyleStrategy(int mergeRowIndex) {
        this.mergeRowIndex = mergeRowIndex;
        initCommonStyle();
    }

    public CommonHeadStyleStrategy(int[] mergeColumnIndex , int mergeRowIndex) {
        this.mergeColumnIndex = mergeColumnIndex;
        this.mergeRowIndex = mergeRowIndex;
        initCommonStyle();
    }

    private void initCommonStyle() {
        //设置默认的headStyle
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.index);
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setBold(false);
        headWriteFont.setFontHeightInPoints((short) 11);
        headWriteFont.setFontName(FONT_NAME);
        headStyle.setWriteFont(headWriteFont);
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        WriteCellStyle contentStyle = new WriteCellStyle();
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontName(FONT_NAME);
        contentWriteFont.setFontHeightInPoints((short) 11);
        contentStyle.setWriteFont(contentWriteFont);
        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headWriteCellStyle = headStyle;
        if (contentWriteCellStyleList == null) {
            contentWriteCellStyleList = new ArrayList<>();
        }
        contentWriteCellStyleList.add(contentStyle);
    }

    public CommonHeadStyleStrategy() {
        initCommonStyle();
    }

    @Override
    protected void initCellStyle(Workbook workbook) {
        if (headWriteCellStyle != null) {
            headCellStyle = StyleUtil.buildHeadCellStyle(workbook , headWriteCellStyle);
        }
        if (contentWriteCellStyleList != null && !contentWriteCellStyleList.isEmpty()) {
            contentCellStyleList = new ArrayList<CellStyle>();
            for (WriteCellStyle writeCellStyle : contentWriteCellStyleList) {
                contentCellStyleList.add(StyleUtil.buildContentCellStyle(workbook , writeCellStyle));
            }
        }
    }

    @Override
    protected void setHeadCellStyle(Cell cell , Head head , Integer relativeRowIndex) {
        if (headCellStyle == null) {
            return;
        }
        if (relativeRowIndex == 0) {
            Row row = cell.getRow();
            row.setHeight((short) 600);
            Workbook workbook = row.getSheet().getWorkbook();
            CellStyle cellStyle = workbook.createCellStyle();
            Font font = workbook.createFont();
            font.setBold(true);
            font.setFontHeight((short) 360);
            cellStyle.setFont(font);
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            return;
        }
        if (relativeRowIndex == 1) {
            Workbook workbook = cell.getRow().getSheet().getWorkbook();
            WriteCellStyle headStyle = new WriteCellStyle();
            headStyle.setFillForegroundColor(IndexedColors.SEA_GREEN.index);
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setBold(false);
            headWriteFont.setFontHeightInPoints((short) 11);
            headWriteFont.setFontName(FONT_NAME);
            headStyle.setWriteFont(headWriteFont);
            headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            CellStyle cellStyle = StyleUtil.buildHeadCellStyle(workbook , headStyle);
            cell.setCellStyle(cellStyle);
            return;
        }
        if (relativeRowIndex == 2) {
            cell.getRow().setHeight((short) 300);
        }
        cell.setCellStyle(headCellStyle);
    }

    @Override
    protected void setContentCellStyle(Cell cell , Head head , Integer relativeRowIndex) {
        if (contentCellStyleList == null || contentCellStyleList.isEmpty()) {
            return;
        }
        cell.setCellStyle(contentCellStyleList.get(relativeRowIndex % contentCellStyleList.size()));
    }

}
