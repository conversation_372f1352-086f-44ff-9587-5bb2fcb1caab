package com.das.utils.advanced;

import com.das.utils.common.Constants;
import com.das.utils.common.StringHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.configurationprocessor.json.JSONObject;

import javax.servlet.ServletContext;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.Map;

/**
 * Description TODO
 *
 * <AUTHOR>
 * @since 2020/5/27
 */
@Slf4j
public class AccessTokenUtil {
    private static final String apiKey = "TCVeaXVI1TVrsAw1KlNpDKBz";
    private static final String secretKey = "AKsqdWlRuXl6qXHjGRe0opPlzFxBCB4W";

    public static String initAndSetAccessToken() {
        // 获取token地址
        String authHost = "https://aip.baidubce.com/oauth/2.0/token?";
        String getAccessTokenUrl = authHost
                // 1. grant_type为固定参数
                + "grant_type=client_credentials"
                // 2. 官网获取的 API Key
                + "&client_id=" + apiKey
                // 3. 官网获取的 Secret Key
                + "&client_secret=" + secretKey;
        try {
            URL realUrl = new URL(getAccessTokenUrl);
            // 打开和URL之间的连接
            HttpURLConnection connection = (HttpURLConnection) realUrl.openConnection();
            connection.setRequestMethod("GET");
            connection.connect();
            // 获取所有响应头字段
            Map<String, List<String>> map = connection.getHeaderFields();
            // 遍历所有的响应头字段

            // 定义 BufferedReader输入流来读取URL的响应
            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String result = "";
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
            JSONObject jsonObject = new JSONObject(result);
            String access_token = jsonObject.getString("access_token");
            if (null != access_token) {
                ServletContext sc = ServletContextUtil.get();
                sc.removeAttribute(Constants.ACCESS_TOKEN);
                sc.setAttribute(Constants.ACCESS_TOKEN, access_token);
            }
            return access_token;
        } catch (Exception e) {
            log.error(StringHandle.getExceptionMessage("获取token失败", e));
        }
        return null;
    }

    public static String getAccessToken() {
        String accessToken = (String) ServletContextUtil.get().getAttribute(Constants.ACCESS_TOKEN);
        if (StringHandle.isEmpty(accessToken)) {
            return initAndSetAccessToken();
        }
        return accessToken;
    }
}
