package com.das.utils.advanced;

import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

/**
 * Description TODO
 *
 * <AUTHOR>
 * @since 2020/5/27
 */
@Component
public class AccessTokenListener implements ApplicationListener<ContextRefreshedEvent> {
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (event.getApplicationContext().getParent() == null) {

            /*Runnable runnable = new Runnable() {
                public void run() {
                    AccessTokenUtil.initAndSetAccessToken();
                }
            };
            ScheduledExecutorService service = Executors.newSingleThreadScheduledExecutor();
            service.scheduleAtFixedRate(runnable, 1, 7000, TimeUnit.SECONDS);*/
        }
    }
}
