package com.das.utils.download;

import com.aspose.words.Document;
import com.aspose.words.License;
import com.aspose.words.SaveFormat;
import com.das.utils.common.FileUtil;
import freemarker.core.XMLOutputFormat;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.springframework.util.ResourceUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.Base64;
import java.util.Map;

/**
 * @Title: DownLoadWord
 * @Package com.das.mural.utils.downLoad
 * @Description: TODO 导出Word文档公共类
 * <AUTHOR>
 * @date 2021/5/17 16:32
 */
public class DownLoadWord {


    private static File createExcel(Map<?, ?> dataMap, Template template) {
        String name = FileUtil.getFtpPath() + "/template/tempDoc.xls";
        File f = new File(name);
        Template t = template;
        try {
            Writer w = new OutputStreamWriter(new FileOutputStream(f), "utf-8");
            t.process(dataMap, w);
            w.close();
        } catch (TemplateException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return f;
    }


    private static File createDoc(Map<?, ?> dataMap, Template template) {
        String name = FileUtil.getFtpPath() + "/template/tempDoc.doc";
        File f = new File(name);
        Template t = template;
        try {
            Writer w = new OutputStreamWriter(new FileOutputStream(f), "utf-8");
            t.process(dataMap, w);
            w.close();
        } catch (TemplateException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return f;
    }

    /**
     * 创建Excel文档
     *
     * @param dataMap
     * @param response
     * @param expName
     */
    public void createExcel(Map dataMap, HttpServletResponse response, String expName, String ftl) {
        Configuration configuration = new Configuration();
        Template t = null;
        try {
            configuration.setDirectoryForTemplateLoading(new File(ResourceUtils.getURL("classpath:").getPath() + "template/"));
            t = configuration.getTemplate(ftl);
            t.setEncoding("utf-8");
            response.setContentType("application/x-download");
            response.setCharacterEncoding("UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(expName + ".xls", "UTF-8"));
            OutputStream fos = response.getOutputStream();
            createExcel(dataMap, t);
            FileInputStream fin = new FileInputStream(createDoc(dataMap, t));
            byte[] buffer = new byte[512];
            int bytesToRead = -1;
            while ((bytesToRead = fin.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesToRead);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 创建word文档
     *
     * @param dataMap
     * @param response
     * @param expName
     */
    public void createDoc(Map dataMap, HttpServletResponse response, String expName, String ftl) {
        Configuration configuration = new Configuration();
        Template t = null;
        try {
            configuration.setDirectoryForTemplateLoading(new File(ResourceUtils.getURL("classpath:").getPath() + "template/"));
            configuration.setOutputFormat(XMLOutputFormat.INSTANCE);
            t = configuration.getTemplate(ftl);
            t.setEncoding("utf-8");
            response.setContentType("application/x-download");
            response.setCharacterEncoding("UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(expName + ".docx", "UTF-8"));
            OutputStream fos = response.getOutputStream();
            createDoc(dataMap, t);
            FileInputStream fin = new FileInputStream(DownLoadWord.docToDocx(FileUtil.getFtpPath() + "/template/tempDoc.doc", FileUtil.getFtpPath() + "tempDocx.docx"));
            byte[] buffer = new byte[512];
            int bytesToRead = -1;
            while ((bytesToRead = fin.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesToRead);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static boolean getLicense() {
        boolean result = false;
        try {
            // 凭证
            String licenseStr =
                    "<License>\n" +
                            "  <Data>\n" +
                            "    <Products>\n" +
                            "      <Product>Aspose.Total for Java</Product>\n" +
                            "      <Product>Aspose.Words for Java</Product>\n" +
                            "    </Products>\n" +
                            "    <EditionType>Enterprise</EditionType>\n" +
                            "    <SubscriptionExpiry>20991231</SubscriptionExpiry>\n" +
                            "    <LicenseExpiry>20991231</LicenseExpiry>\n" +
                            "    <SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber>\n" +
                            "  </Data>\n" +
                            "  <Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature>\n" +
                            "</License>";
            InputStream license = new ByteArrayInputStream(licenseStr.getBytes("UTF-8"));
            License asposeLic = new License();
            asposeLic.setLicense(license);
            result = true;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 格式转换
     *
     * @param inPath
     * @param outPath
     * @return
     */
    private static File docToDocx(String inPath, String outPath) {
        if (!getLicense()) { // 验证License 若不验证则转化出的pdf文档会有水印产生
            return null;
        }
        FileOutputStream os = null;
        File file = null;
        try {
            file = new File(outPath);
            os = new FileOutputStream(file);
            Document doc = new Document(inPath);
            doc.save(os, SaveFormat.DOCX);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (os != null) {
                try {
                    os.flush();
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return file;
    }

    /**
     * 本地文件图片获取base64码
     *
     * @param src
     * @return
     */
    public static String getImageBase(String src) {
        if (src == null || src == "")
            return "";
        File file = new File(src);
        if (!file.exists())
            return "";
        InputStream in = null;
        byte[] data = null;
        try {
            in = new FileInputStream(file);
            data = new byte[in.available()];
            in.read(data);
            in.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        Base64.Encoder encoder = Base64.getEncoder();
        return encoder.encodeToString(data);
    }


}
