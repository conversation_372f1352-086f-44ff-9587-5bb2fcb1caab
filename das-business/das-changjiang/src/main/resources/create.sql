CREATE TABLE `cj_digital_maps` (
                                   `id` bigint AUTO_INCREMENT PRIMARY KEY,
                                   `map_name` VARCHAR(256) NOT NULL COMMENT '地图名称',
                                   `map_type_group` VARCHAR(64) NOT NULL COMMENT '地图类型及分组,多个用英文逗号隔开',
                                   `map_file` VARCHAR(255) NOT NULL COMMENT '地图文件路径',
                                   `longitude` VARCHAR(32) COMMENT '经度',
                                   `latitude` VARCHAR(32) COMMENT '纬度',
                                   `description` VARCHAR(1024) COMMENT '地图文字说明',
                                   `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                   `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                   `publish_status` int DEFAULT '0' COMMENT '发布状态：0未发布，1已发布'
) COMMENT='数字地图表';


CREATE TABLE `cj_place_name_data` (
                                      `id` bigint AUTO_INCREMENT PRIMARY KEY,
                                      `name` VARCHAR(256) NOT NULL COMMENT '地名',
                                      `map_type_group` VARCHAR(64) NOT NULL COMMENT '地图类型及分组,多个用英文逗号隔开',
                                      `ancient_region_category` int COMMENT '古地域类别，1遗址, 2墓葬, 3其他',
                                      `region_category_content` int COMMENT '古地域类别为3时填写',
                                      `province` VARCHAR(50) COMMENT '省份',
                                      `city` VARCHAR(50) COMMENT '城市',
                                      `district` VARCHAR(50) COMMENT '县/区',
                                      `geographic_location` VARCHAR(512) COMMENT '地理位置',
                                      `longitude` VARCHAR(32) COMMENT '经度',
                                      `latitude` VARCHAR(32) COMMENT '纬度',
                                      `age` varchar(32) DEFAULT '' COMMENT '年代,多个用逗号分开',
                                      `administrative_history` VARCHAR(512)  COMMENT '建制沿革',
                                      `introduction` VARCHAR(512) COMMENT '简介',
                                      `info_source` VARCHAR(150) COMMENT '信息来源',
                                      `remarks` VARCHAR(150) COMMENT '备注信息',
                                      `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                      `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                      `publish_status` int DEFAULT '0' COMMENT '发布状态：0未发布，1已发布'
) COMMENT='地名资料表';


CREATE TABLE `cj_history_maps` (
                                   `id` bigint AUTO_INCREMENT PRIMARY KEY,
                                   `map_name` VARCHAR(256) NOT NULL COMMENT '地图名称',
                                   `map_from` VARCHAR(64) NOT NULL COMMENT '地图来源,多个用英文逗号隔开',
                                   `map_content` TEXT COMMENT '资料详情',
                                   `age` bigint COMMENT '年代',
                                   `map_file` VARCHAR(255) NOT NULL COMMENT '地图文件路径',
                                   `remarks` VARCHAR(150) COMMENT '备注信息',
                                   `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                   `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                   `publish_status` int DEFAULT '0' COMMENT '发布状态：0未发布，1已发布'
) COMMENT='历史地图表';


CREATE TABLE `cj_problem_feedback` (
                                       `id` bigint AUTO_INCREMENT PRIMARY KEY,
                                       `title` VARCHAR(128) NOT NULL COMMENT '标题',
                                       `send_content` VARCHAR(512) NOT NULL COMMENT '反馈内容',
                                       `reply_content` VARCHAR(512) COMMENT '回复内容',
                                       `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                       `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                       `back_status` int DEFAULT '0' COMMENT '问题状态：0未回复，1已回复'
) COMMENT='问题反馈表';