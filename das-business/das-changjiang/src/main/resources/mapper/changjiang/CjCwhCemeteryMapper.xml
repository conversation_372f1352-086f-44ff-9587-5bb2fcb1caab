<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.business.changjiang.mapper.CjCwhCemeteryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjCwhCemetery">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="age" property="age" />
        <result column="age_name" property="ageName" />
        <result column="direction" property="direction" />
        <result column="area" property="area" />
        <result column="coffin_chongchu" property="coffinChongchu" />
        <result column="coffin_number" property="coffinNumber" />
        <result column="steps" property="steps" />
        <result column="sex" property="sex" />
        <result column="musical_instrument" property="musicalInstrument" />
        <result column="pottery" property="pottery" />
        <result column="use_pottery" property="usePottery" />
        <result column="chariot_horse_implement" property="chariotHorseImplement" />
        <result column="weaponry" property="weaponry" />
        <result column="lacquer_wood_ware" property="lacquerWoodWare" />
        <result column="jade" property="jade" />
        <result column="other" property="other" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
            id,
            name,
            age,
            (select description from sys_code where id = a.age)age_name,
            direction,
            area,
            coffin_chongchu,
            coffin_number,
            steps,
            sex,
            musical_instrument,
            pottery,
            use_pottery,
            chariot_horse_implement,
            weaponry,
            lacquer_wood_ware,
            jade,
            other,
            remark,
            create_time,
            update_time
        from cj_cwh_cemetery a
          where 1 = 1
            <if test="params.keyWord != null and params.keyWord != ''">
                and concat(IFNULL(name, ''), '|',
                IFNULL(age, ''), '|',
                IFNULL(area, ''), '|',
                IFNULL(coffin_chongchu, '|'),
                IFNULL(coffin_number, '|'),
                IFNULL(steps, '|'),
                IFNULL(musical_instrument, '|'),
                IFNULL(pottery, '|'),
                IFNULL(use_pottery, '|'),
                IFNULL(chariot_horse_implement, '|'),
                IFNULL(weaponry, '|'),
                IFNULL(lacquer_wood_ware, '|'),
                IFNULL(jade, '|'),
                IFNULL(other, '|'),
                IFNULL(remark, '|')
                ) like concat('%', #{params.keyWord},'%')
            </if>
            <if test="params.sex!= null and params.sex!= '' and params.sex != 0">
                and sex = #{params.sex}
            </if>
            <if test="params.material!= null and params.material!= ''">
                and concat(
                  IFNULL(musical_instrument, '|'),
                  IFNULL(pottery, '|'),
                  IFNULL(use_pottery, '|'),
                  IFNULL(chariot_horse_implement, '|'),
                  IFNULL(weaponry, '|'),
                  IFNULL(lacquer_wood_ware, '|'),
                  IFNULL(jade, '|')
                ) like concat('%', #{params.material},'%')
            </if>
            <if test="params.direction != null and params.direction != ''">
                and direction &gt;= SUBSTRING_INDEX(#{params.direction}, '-', 1) and direction &lt;= SUBSTRING_INDEX(#{params.direction}, '-', -1)
            </if>
            <if test="params.area!= null and params.area!= ''">
                and area &gt;= SUBSTRING_INDEX(#{params.area}, '-', 1) and area &lt;= SUBSTRING_INDEX(#{params.area}, '-', -1)
            </if>
            <if test="params.age!= null and params.age!= ''">
                and age in (${params.age})
            </if>
        order by ${sort} ${order}
    </select>

</mapper>
