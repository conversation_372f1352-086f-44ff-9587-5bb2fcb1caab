<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.business.changjiang.mapper.CjRuinsInfoMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjRuinsInfo">
		<id column="id" property="id"/>
		<result column="id" property="id"/>
		<result column="parentId" property="parent_id"/>
		<result column="name" property="name"/>
		<result column="category" property="category"/>
		<result column="city" property="city"/>
		<result column="county" property="county"/>
		<result column="address" property="address"/>
		<result column="longitude" property="longitude"/>
		<result column="latitude" property="latitude"/>
		<result column="altitude" property="altitude"/>
		<result column="discoverTombs" property="discover_tombs"/>
		<result column="proveTombs" property="prove_tombs"/>
		<result column="discoverTime" property="discover_time"/>
		<result column="age" property="age"/>
		<result column="discoverLog" property="discover_log"/>
		<result column="tombsLayout" property="tombs_layout"/>
		<result column="environment" property="environment"/>
		<result column="surroundingRuins" property="surrounding_ruins"/>
		<result column="report" property="report"/>
		<result column="otherInfo" property="other_info"/>
		<result column="layoutPlanUrl" property="layout_plan_url"/>
		<result column="createTime" property="create_time"/>
		<result column="updateTime" property="update_time"/>
	</resultMap>

	<select id="findByPage" resultMap="BaseResultMap">
		select 
			id,
			parent_id,
			name,
			category,
			city,
			county,
			address,
			longitude,
			latitude,
			altitude,
			discover_tombs,
			prove_tombs,
			discover_time,
			age,
			discover_log,
			tombs_layout,
			environment,
			surrounding_ruins,
			report,
			other_info,
			layout_plan_url,
			create_time,
			update_time
		from cj_ruins_info where del_flag = 0
		<if test="params.name != null and params.name != ''">
			and name like concat(#{params.name}, '%')
		</if>
	</select>

	<select id="execCustomSql" resultType="java.util.Map">
		${execCustomSql}
	</select>

</mapper>
