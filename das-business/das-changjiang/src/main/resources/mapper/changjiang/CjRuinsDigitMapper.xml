<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.business.changjiang.mapper.CjRuinsDigitMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjRuinsDigit">
        <result column="id" property="id"/>
        <result column="ruins_id" property="ruinsId"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="load_path" property="loadPath"/>
        <result column="status" property="status"/>
        <result column="model" property="model"/>
        <result column="cover" property="cover"/>
        <result column="thumbnail" property="thumbnail"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="typeName" property="typeName" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getCjRuinsDigitList" resultMap="BaseResultMap">
        select id,ruins_id,name,type,load_path,status,model,cover,thumbnail,create_time,update_time,
        ( select description from sys_code where id = type ) typeName
        From cj_ruins_digit
        where 1 = 1
        <if test="params.ruinsId != '' and params.ruinsId != null">
            and ruins_id = #{params.ruinsId}
        </if>
        <if test="params.type != '' and params.type != null">
            and type = #{params.type}
        </if>
        <if test="params.name != null and params.name !='' ">
            and name like CONCAT('%',#{params.name,jdbcType=VARCHAR},'%')
        </if>
        <if test="sort != null and sort != ''">
            order by ${sort} ${order}
        </if>
    </select>

    <select id="getTypeAndCount" resultType="com.das.business.changjiang.entity.responseEntity.TypeAndCount">
        SELECT * FROM
            (
                SELECT type,( SELECT description FROM sys_code WHERE id = type ) typeName,COUNT( type ) count
                FROM `cj_ruins_digit` WHERE ruins_id = #{ruinsId} GROUP BY type

                UNION ALL

                SELECT id,description,0 count
                FROM sys_code
                WHERE
                    field = 'digitType' AND is_last_child = 1
                  AND id NOT IN ( SELECT type FROM `cj_ruins_digit` WHERE ruins_id = #{ruinsId} GROUP BY type )) a
        ORDER BY type
    </select>

    <update id="batchUpdate" parameterType="map">
        update `cj_ruins_digit` set status = 1 where id in ${ids}
    </update>

    <select id="getDigitNames" resultType="string">
        select name from cj_ruins_digit where ruins_id = #{ruinsId}
    </select>

    <select id="getDigitTypes" resultType="string">
        SELECT DISTINCT
            b.description
        FROM
            cj_ruins_digit a LEFT JOIN sys_code b ON a.type = b.id
        WHERE
            ruins_id = #{ruinsId}
    </select>

</mapper>
