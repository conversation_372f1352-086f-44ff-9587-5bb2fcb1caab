<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://Mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.business.changjiang.mapper.CjReportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjReport">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="theme" property="theme"/>
        <result column="range" property="range"/>
        <result column="age" property="age"/>
        <result column="contributor" property="contributor"/>
        <result column="type" property="type"/>
        <result column="data_type" property="dataType"/>
        <result column="view_img" property="viewImg"/>
        <result column="is_download" property="isDownload"/>
        <result column="file" property="file"/>
        <result column="file_size" property="fileSize"/>
        <result column="gps" property="gps"/>
        <result column="publish" property="publish"/>
        <result column="page_view" property="pageView"/>
        <result column="publish_time" property="publishTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
        <collection property="cjFiles" javaType="java.util.List" ofType="com.das.business.changjiang.entity.CjFile"
                    select="com.das.business.changjiang.mapper.CjFileMapper.getCjFile" column="resId=id">
        </collection>
    </resultMap>

    <select id="getCjReportList" resultMap="BaseResultMap">
        select cj.id, cj.name, cj.description, cj.theme, cj.range, cj.age, cj.contributor, cj.type,cj.gps,
        cj.data_type,cj.view_img, cj.is_download,cj.publish_time,cj.publish,cj.file_size,
        cj.page_view, cj.file, cj.update_time, cj.create_time,
        if((select id from cj_collect where source_id = cj.id and user_id = #{params.userId}),1,0) isCollect,
        (select description from sys_code where id = cj.theme) as themeName,
        (select description from sys_code where id = cj.`range`) as rangeName,
        (select description from sys_code where id = cj.age) as ageName,
        (select description from sys_code where id = cj.type) as typeName,
        (select description from sys_code where id = cj.data_type) as dataTypeName
        from cj_report cj
        where 1 = 1

        <if test="params.RoleType ==0">
            and user_id = #{params.userId}
        </if>

        <!-- 主题词 -->
        <if test="params.theme != null and params.theme.size() > 0">
            and cj.theme  in
            <foreach collection="params.theme" item="item" separator="," open="(" close=")">
                    #{item}
            </foreach>
        </if>
        <!-- 空间范围 -->
        <if test="params.range != null and params.range.size() >0 ">
            and cj.range in
            <foreach collection="params.range" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!-- 时代 -->
        <if test="params.age != null and params.age.size() > 0">
            and cj.age in
            <foreach collection="params.age" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!-- 数据类型 -->
        <if test="params.dataType != null and params.dataType.size() >0">
            and cj.data_type in
            <foreach collection="params.dataType" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!-- 数据分类 -->
        <if test="params.type != '' and params.type != null and params.type != -1">
            and cj.type = #{params.type}
        </if>
        <!-- 数据检  标题 -->
        <if test="params.name != '' and params.name != null">
            and cj.name like CONCAT('%',#{params.name},'%')
        </if>
        <!-- 数据时间范围的起始时间 -->
        <if test="params.startTime !='' and params.startTime !=null ">
            and DATE_FORMAT(cj.create_time,'%Y-%m-%d') &gt;= DATE_FORMAT( #{params.startTime}, '%Y-%m-%d' )
        </if>
        <!-- 数据时间范围的终止时间 -->
        <if test="params.endTime !='' and params.endTime !=null ">
            and DATE_FORMAT(cj.create_time,'%Y-%m-%d') &lt;= DATE_FORMAT( #{params.endTime}, '%Y-%m-%d' )
        </if>
        <!-- 是否显示下载数据 -->
        <if test="params.isDownload != '' and params.isDownload != null">
            and cj.is_download = 0
        </if>
        <!-- 发布状态 -->
        <if test="params.publish != '' and params.publish != null">
            and cj.publish = #{params.publish}
        </if>
        order by cj.${sort} ${order}
    </select>

    <select id="getCjReportById" resultMap="BaseResultMap">
        select cj.id, cj.name, cj.description, cj.theme, cj.range, cj.age, cj.contributor, cj.type,cj.gps,
        cj.data_type,cj.view_img, cj.is_download,cj.publish_time,cj.publish,cj.file_size,
        cj.page_view, cj.file, cj.update_time, cj.create_time,
        if((select id from cj_collect where source_id = cj.id and user_id = #{userId}),1,0) isCollect,
        (select description from sys_code where id = cj.theme) as themeName,
        (select description from sys_code where id = cj.`range`) as rangeName,
        (select description from sys_code where id = cj.age) as ageName,
        (select description from sys_code where id = cj.type) as typeName,
        (select description from sys_code where id = cj.data_type) as dataTypeName
        from cj_report cj
        where id = #{id}
    </select>

    <select id="getCjReportByRuinsId" resultMap="BaseResultMap">
        select cj.id, cj.name, cj.description, cj.theme, cj.range, cj.age, cj.contributor, cj.type,cj.gps,
        cj.data_type,cj.view_img, cj.is_download,cj.publish_time,cj.publish,cj.file_size,
        cj.page_view, cj.file, cj.update_time, cj.create_time,
        if((select id from cj_collect where source_id = cj.id and user_id = #{userId}),1,0) isCollect,
        (select description from sys_code where id = cj.theme) as themeName,
        (select description from sys_code where id = cj.`range`) as rangeName,
        (select description from sys_code where id = cj.age) as ageName,
        (select description from sys_code where id = cj.type) as typeName,
        (select description from sys_code where id = cj.data_type) as dataTypeName
        from cj_report cj
        where ruins_id = #{id}
    </select>

</mapper>
