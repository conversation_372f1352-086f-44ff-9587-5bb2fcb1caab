<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://Mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.business.changjiang.mapper.CjCollectionInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjCollectionInfo">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="level" property="level"/>
        <result column="type" property="type"/>
        <result column="classify" property="classify"/>
        <result column="excavated" property="excavated"/>
        <result column="excavated_time" property="excavatedTime"/>
        <result column="address" property="address"/>
        <result column="information" property="information"/>
        <result column="age" property="age"/>
        <result column="age_detail" property="ageDetail"/>
        <result column="color" property="color"/>
        <result column="author" property="author"/>
        <result column="appendages" property="appendages"/>
        <result column="length" property="length"/>
        <result column="width" property="width"/>
        <result column="height" property="height"/>
        <result column="presence" property="presence"/>
        <result column="bore" property="bore"/>
        <result column="boot_bore" property="bootBore"/>
        <result column="complete" property="complete"/>
        <result column="texture" property="texture"/>
        <result column="texture_type" property="textureType"/>
        <result column="texture_info" property="textureInfo"/>
        <result column="img_url" property="imgUrl"/>
        <result column="three_url" property="threeUrl"/>
        <result column="status" property="status"/>
        <result column="model" property="model"/>
        <result column="file_name" property="fileName"/>
        <result column="gps" property="gps"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <select id="getByPage" resultType="com.das.business.changjiang.entity.CjCollectionInfo">
        select s.* from (
        select
        a.id,a.name,a.level,a.type,a.excavated,a.excavated_time,a.address,a.information,a.age,a.age_detail,a.color,a.author,a.appendages,
        a.length,a.width,a.height,a.presence,a.bore,a.boot_bore,a.complete,a.texture,a.texture_info,a.img_url,a.classify,
        a.three_url,a.gps,a.update_time,a.create_time,a.file_name,
        case a.classify when 1 then '可移动文物' when 2 then '不可移动文物' end classifyName,
        (select description From sys_code where id = a.type) typeName,
        (select description From sys_code where id = a.level) levelName,
        (select description From sys_code where id = a.age) ageName,
        (select description From sys_code where id = a.complete) completeName,
        (select description From sys_code where id = a.texture) textureName,
        a.status,a.model
        from cj_collection_info a
        ) s
        where 1=1
        <if test="params.id != '' and params.id !=null">
            and s.id = #{params.id}
        </if>
        <if test="params.name != '' and params.name !=null">
            and s.name like "%"#{params.name}"%"
        </if>
        <if test="params.age !=null and params.age.size() > 0">
            and s.age in
            <foreach collection="params.age" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="params.texture !=null and params.texture.size() > 0">
            and s.texture in
            <foreach collection="params.texture" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="params.level !=null and params.level.size() > 0">
            and s.level in
            <foreach collection="params.level" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="params.type !='' and params.type !=null">
            and s.type = #{params.type}
        </if>
        <if test="params.classify !='' and params.classify !=null">
            and s.classify = #{params.classify}
        </if>
        <if test="params.val !='' and params.val !=null ">
           and CONCAT_WS('|',s.name,s.excavated,s.address,s.information,s.age_detail,s.color,s.author,s.appendages,
                s.texture_info) like CONCAT('%',#{params.val},'%')
        </if>
        order by s.${sort} ${order}
    </select>

    <select id="getGroupByData" resultType="java.util.Map">
        select count(1) count,type from cj_collection_info group by type
    </select>

    <select id="getInfoById" resultType="com.das.business.changjiang.entity.CjCollectionInfo">
        select
        a.id,a.name,a.level,a.type,a.excavated,a.excavated_time,a.address,a.information,a.age,a.age_detail,a.color,a.author,a.appendages,
        a.length,a.width,a.height,a.presence,a.bore,a.boot_bore,a.complete,a.texture,a.texture_info,a.img_url,a.classify,
        a.three_url,a.gps,a.update_time,a.create_time,a.file_name,
        case a.classify when 1 then '可移动文物' when 2 then '不可移动文物' end classifyName,
        (select description From sys_code where id = a.type) typeName,
        (select description From sys_code where id = a.level) levelName,
        (select description From sys_code where id = a.age) ageName,
        (select description From sys_code where id = a.complete) completeName,
        (select description From sys_code where id = a.texture) textureName,
        a.status,a.model
        from cj_collection_info a
        where id = #{id}
    </select>

    <select id="getInfoByRuinsId" resultType="com.das.business.changjiang.entity.CjCollectionInfo">
        select
        a.id,a.name,a.level,a.type,a.excavated,a.excavated_time,a.address,a.information,a.age,a.age_detail,a.color,a.author,a.appendages,
        a.length,a.width,a.height,a.presence,a.bore,a.boot_bore,a.complete,a.texture,a.texture_info,a.img_url,a.classify,
        a.three_url,a.gps,a.update_time,a.create_time,a.file_name,
        case a.classify when 1 then '可移动文物' when 2 then '不可移动文物' end classifyName,
        (select description From sys_code where id = a.type) typeName,
        (select description From sys_code where id = a.level) levelName,
        (select description From sys_code where id = a.age) ageName,
        (select description From sys_code where id = a.complete) completeName,
        (select description From sys_code where id = a.texture) textureName,
        a.status,a.model
        from cj_collection_info a
        where ruins_id = #{id}
    </select>

    <update id="batchUpdate" parameterType="map">
        update `cj_collection_info` set status = 1 where id in ${ids}
    </update>

</mapper>
