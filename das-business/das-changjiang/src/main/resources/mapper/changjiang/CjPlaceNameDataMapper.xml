<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.business.changjiang.mapper.CjPlaceNameDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjPlaceNameData">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="map_type" property="mapType"/>
        <result column="map_group_big" property="mapGroupBig"/>
        <result column="map_group_small" property="mapGroupSmall"/>
        <result column="ancient_region_category" property="ancientRegionCategory"/>
        <result column="region_category_content" property="regionCategoryContent"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="district" property="district"/>
        <result column="geographic_location" property="geographicLocation"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="age" property="age"/>
        <result column="administrative_history" property="administrativeHistory"/>
        <result column="introduction" property="introduction"/>
        <result column="info_source" property="infoSource"/>
        <result column="remarks" property="remarks"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="publish_status" property="publishStatus"/>
        <result column="publish_status_name" property="publishStatusName"/>
        <result column="ancient_region_category_name" property="ancientRegionCategoryName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, map_type, map_group_small,map_group_big,ancient_region_category, region_category_content,
        province, city, district, geographic_location, longitude, latitude, age, 
        administrative_history, introduction, info_source, remarks, 
        create_time, update_time, publish_status
    </sql>

    <!-- 分页查询地名资料列表 -->
    <select id="getCjPlaceNameDataList" resultMap="BaseResultMap">
        SELECT 
            pnd.id, pnd.name, pnd.map_type, pnd.map_group_small,pnd.map_group_big, pnd.ancient_region_category, pnd.region_category_content,
            pnd.province, pnd.city, pnd.district, pnd.geographic_location, pnd.longitude, pnd.latitude,
            pnd.age, pnd.administrative_history, pnd.introduction, pnd.info_source, pnd.remarks,
            pnd.create_time, pnd.update_time, pnd.publish_status,
            CASE pnd.publish_status 
                WHEN 0 THEN '未发布' 
                WHEN 1 THEN '已发布' 
                ELSE '未知' 
            END AS publish_status_name,
            CASE pnd.ancient_region_category 
                WHEN 1 THEN '遗址' 
                WHEN 2 THEN '墓葬' 
                WHEN 3 THEN '其他' 
                ELSE '未知' 
            END AS ancient_region_category_name
        FROM cj_place_name_data pnd
        <where>
            <if test="params.name != null and params.name != ''">
                AND pnd.name LIKE CONCAT('%', #{params.name}, '%')
            </if>
            <if test="params.mapType != null">
                AND pnd.map_type = #{params.mapType}
            </if>
            <if test="params.mapGroupBig != null">
                AND pnd.map_group_big = #{params.mapGroupBig}
            </if>
            <if test="params.mapGroupSmall != null">
                AND pnd.map_group_small = #{params.mapGroupSmall}
            </if>
            <if test="params.ancientRegionCategory != null and params.ancientRegionCategory != ''">
                AND pnd.ancient_region_category = #{params.ancientRegionCategory}
            </if>
            <if test="params.province != null and params.province != ''">
                AND pnd.province LIKE CONCAT('%', #{params.province}, '%')
            </if>
            <if test="params.city != null and params.city != ''">
                AND pnd.city LIKE CONCAT('%', #{params.city}, '%')
            </if>
            <if test="params.district != null and params.district != ''">
                AND pnd.district LIKE CONCAT('%', #{params.district}, '%')
            </if>
            <if test="params.publishStatus != null and params.publishStatus != ''">
                AND pnd.publish_status = #{params.publishStatus}
            </if>
            <if test="params.startTime != null and params.startTime != ''">
                AND pnd.create_time >= #{params.startTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND pnd.create_time &lt;= #{params.endTime}
            </if>
            <if test="params.keyword != null and params.keyword != ''">
                AND ( pnd.name LIKE CONCAT('%', #{params.keyword}, '%')
                    OR pnd.geographic_location LIKE CONCAT('%', #{params.keyword}, '%')
                    OR CONCAT(pnd.province, pnd.city, pnd.district) LIKE CONCAT('%', #{params.keyword}, '%')
                )
            </if>
        </where>
        <choose>
            <when test="sort != null and sort != '' and order != null and order != ''">
                ORDER BY ${sort} ${order}
            </when>
            <otherwise>
                ORDER BY pnd.create_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 根据ID查询地名资料详情 -->
    <select id="getCjPlaceNameDataById" resultMap="BaseResultMap">
        SELECT 
            pnd.id, pnd.name, pnd.map_type, pnd.map_group_small,pnd.map_group_big, pnd.ancient_region_category, pnd.region_category_content,
            pnd.province, pnd.city, pnd.district, pnd.geographic_location, pnd.longitude, pnd.latitude,
            pnd.age, pnd.administrative_history, pnd.introduction, pnd.info_source, pnd.remarks,
            pnd.create_time, pnd.update_time, pnd.publish_status,
            CASE pnd.publish_status 
                WHEN 0 THEN '未发布' 
                WHEN 1 THEN '已发布' 
                ELSE '未知' 
            END AS publish_status_name,
            CASE pnd.ancient_region_category 
                WHEN 1 THEN '遗址' 
                WHEN 2 THEN '墓葬' 
                WHEN 3 THEN '其他' 
                ELSE '未知' 
            END AS ancient_region_category_name
        FROM cj_place_name_data pnd
        WHERE pnd.id = #{id}
    </select>

</mapper>
