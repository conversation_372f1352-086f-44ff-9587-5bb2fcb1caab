<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.business.changjiang.mapper.CjDictionariesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjDictionaries">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="class1" property="class1"/>
        <result column="class2" property="class2"/>
        <result column="class3" property="class3"/>
        <result column="annotation" property="annotation"/>
        <collection property="fileList" select="findFile" column="dicId=id"/>
    </resultMap>

    <!-- 类型结果 -->
    <resultMap id="DictionariesTypeMap" type="com.das.business.changjiang.entity.CjDictionariesType">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="parent_id" property="parentId"/>
        <result column="is_leaf" property="isLeaf"/>
        <result column="is_show" property="isShow"/>
        <collection property="children" select="getTypeChildren" column="parentId=id" />
    </resultMap>


    <select id="getCjDictionariesList" resultMap="BaseResultMap">
        select id,title,class1,class2,class3,annotation
        from cj_dictionaries
        where title != ''
        <if test="params.class1 != null and params.class1 != ''">
            and concat(IFNULL(class1,''),IFNULL(class2,''),IFNULL(class3,'')) like concat('%',#{params.class1},'%')
        </if>
        <if test="params.title != null and params.title != ''">
            and concat(IFNULL(title,''),IFNULL(class1,''),IFNULL(class2,''),IFNULL(class3,''),IFNULL(annotation,''))
            like concat('%', #{params.title},'%')
        </if>
        order by ${sort} ${order}
    </select>

    <select id="findFile" resultType="com.das.business.changjiang.entity.CjDictionariesFile">
        select id, name, save_name as saveName, load_path as loadPath, dic_id as dicId, file_type as fileType, file_size as fileSize
        from cj_dictionaries_file
        where dic_id = #{dicId}
    </select>

    <select id="getCjDictionariesTree" resultMap="DictionariesTypeMap">
        select id, name, parent_id, is_leaf, is_show
        from cj_dictionaries_type
        where parent_id = 0
    </select>

    <select id="getTypeChildren" resultType="com.das.business.changjiang.entity.CjDictionariesType">
        select id, name, parent_id, is_leaf, is_show
        from cj_dictionaries_type
        where parent_id = #{parentId}
    </select>

</mapper>
