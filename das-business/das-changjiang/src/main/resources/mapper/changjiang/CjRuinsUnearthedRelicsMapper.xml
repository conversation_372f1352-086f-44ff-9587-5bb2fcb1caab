<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://Mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.business.changjiang.mapper.CjRuinsUnearthedRelicsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjRuinsUnearthedRelics">
        <result column="id" property="id"/>
        <result column="ruinsId" property="ruinsId"/>
        <result column="cover" property="cover"/>
        <result column="thumbnail" property="thumbnail"/>
        <result column="name" property="name"/>
        <result column="unearthed_time" property="unearthedTime"/>
        <result column="type" property="type"/>
        <result column="description" property="description"/>
        <result column="age" property="age"/>
        <result column="texture" property="texture"/>
        <result column="length" property="length"/>
        <result column="width" property="width"/>
        <result column="high" property="high"/>
        <result column="complete" property="complete"/>
        <result column="level" property="level"/>
        <result column="classify_number" property="classifyNumber"/>
        <result column="registration_number" property="registrationNumber"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="typeName" property="typeName" jdbcType="VARCHAR"/>
        <result column="ageName" property="ageName" jdbcType="VARCHAR"/>
        <result column="completeName" property="completeName" jdbcType="VARCHAR"/>
        <result column="levelName" property="levelName" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getCjRuinsRelicsList" resultMap="BaseResultMap">
        select id,ruins_id,cover,thumbnail,name,unearthed_time,type,description,age,texture,length,width,high,complete,level
        classify_number,registration_number,create_time,update_time,
        ( select description from sys_code where id = type ) typeName,
        ( select description from sys_code where id = age ) ageName,
        ( select description from sys_code where id = complete ) completeName,
        ( select description from sys_code where id = level ) levelName
        From cj_ruins_unearthed_relics
        where 1 = 1
        <if test="params.ruinsId != '' and params.ruinsId != null">
            and ruins_id = #{params.ruinsId}
        </if>
        <if test="params.type != '' and params.type != null">
            and type = #{params.type}
        </if>
        <if test="params.name != null and params.name !='' ">
            and name like CONCAT('%',#{params.name,jdbcType=VARCHAR},'%')
        </if>
        <if test="sort != null and sort != ''">
            order by ${sort} ${order}
        </if>
    </select>

    <select id="getTypeAndCount" resultType="com.das.business.changjiang.entity.responseEntity.TypeAndCount">
        SELECT * FROM
        (
        SELECT type,( SELECT description FROM sys_code WHERE id = type ) typeName,COUNT( type ) count
        FROM `cj_ruins_unearthed_relics` WHERE ruins_id = #{ruinsId} GROUP BY type

        UNION ALL

        SELECT id,description,0 count
        FROM sys_code
        WHERE
        field = 'MCR_CULTURERELICTYPE' AND is_last_child = 1
        AND id NOT IN ( SELECT type FROM `cj_ruins_unearthed_relics` WHERE ruins_id = #{ruinsId} GROUP BY type )
        ) a
        ORDER BY type
    </select>

    <select id="getRelicNames" resultType="string">
        select name from cj_ruins_unearthed_relics where ruins_id = #{ruinsId}
    </select>



</mapper>
