<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.business.changjiang.mapper.CjRuinsDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjRuinsDetail">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="desc" property="desc"/>
        <result column="excavate_age" property="excavateAge"/>
        <result column="ruins_age" property="ruinsAge"/>
        <result column="gps" property="gps"/>
        <result column="type" property="type"/>
        <result column="line_set" property="lineSet"/>
        <result column="color" property="color"/>
        <result column="parent_id" property="parentId"/>
    </resultMap>

    <select id="getAgeRuinsDetail" resultType="java.util.Map">
        select '1' as type, id, ruins_age as age, name as title, gps, line_set
        from cj_ruins_detail
          where ruins_age &gt;= #{start}
            and ruins_age &lt; #{end}
        union all
        select '2' as type, id, age, title, gps, description as line_set
        from dsj
          where age &gt;= #{start}
            and age &lt; #{end}
    </select>

</mapper>
