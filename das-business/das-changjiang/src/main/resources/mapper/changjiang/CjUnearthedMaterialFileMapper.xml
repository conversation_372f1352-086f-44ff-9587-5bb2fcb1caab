<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://Mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.business.changjiang.mapper.CjUnearthedMaterialFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjUnearthedMaterialFile">
        <result column="id" property="id"/>
        <result column="material_id" property="materialId"/>
        <result column="file_name" property="fileName"/>
        <result column="file_type" property="fileType"/>
        <result column="file_path" property="filePath"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <select id="getMaterialFileNames" resultType="string">
        SELECT
        file_name
        FROM
        cj_ruins_unearthed_material a
        LEFT JOIN cj_unearthed_material_file b ON a.id = b.material_id
        WHERE
        a.ruins_id = #{ruinsId}
    </select>

</mapper>
