<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.business.changjiang.mapper.CjRuinsInfoOldMapper">

    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjRuinsInfoOld">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="category" column="category" jdbcType="BIGINT"/>
        <result property="city" column="city" jdbcType="VARCHAR"/>
        <result property="county" column="county" jdbcType="VARCHAR"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <result property="longitude" column="longitude" jdbcType="DOUBLE"/>
        <result property="latitude" column="latitude" jdbcType="DOUBLE"/>
        <result property="altitude" column="altitude" jdbcType="DOUBLE"/>
        <result property="level" column="level" jdbcType="BIGINT"/>
        <result property="acreage" column="acreage" jdbcType="DOUBLE"/>
        <result property="age" column="age" jdbcType="VARCHAR"/>
        <result property="owner" column="owner" jdbcType="VARCHAR"/>
        <result property="subjection" column="subjection" jdbcType="VARCHAR"/>
        <result property="monomerDescription" column="monomer_description" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="preserveStatus" column="preserve_status" jdbcType="VARCHAR"/>
        <result property="statusDescription" column="status_description" jdbcType="VARCHAR"/>
        <result property="naturalReason" column="natural_reason" jdbcType="VARCHAR"/>
        <result property="artificialReason" column="artificial_reason" jdbcType="VARCHAR"/>
        <result property="reasonDescription" column="reason_description" jdbcType="VARCHAR"/>
        <result property="environment" column="environment" jdbcType="VARCHAR"/>
        <result property="humanity" column="humanity" jdbcType="VARCHAR"/>
        <result property="importTime" column="import_time" jdbcType="TIMESTAMP"/>
        <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="levelName" column="levelName" jdbcType="VARCHAR"/>
        <result property="categoryName" column="categoryName" jdbcType="VARCHAR"/>
        <result property="gps" column="gps" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="selectList" type="com.das.business.changjiang.entity.responseEntity.RuinsInfoResponse">
        <id property="tid" column="tid" jdbcType="BIGINT"/>
        <result property="ruinsName" column="ruinsName" jdbcType="VARCHAR"/>
        <result property="gps" column="gps" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,name,category,
        city,county,address,
        longitude,latitude,altitude,
        level,acreage,age,
        owner,subjection,monomer_description,
        description,preserve_status,status_description,
        natural_reason,artificial_reason,reason_description,
        environment,humanity,import_time,
        parent_id,create_time,update_time
    </sql>

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into cj_ruins_info (
        name,category,
        city,county,address,
        longitude,latitude,altitude,
        level,acreage,age,
        owner,subjection,monomer_description,
        description,preserve_status,status_description,
        natural_reason,artificial_reason,reason_description,
        environment,humanity,import_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.name},
            #{item.category},
            #{item.city},
            #{item.county},
            #{item.address},
            #{item.longitude},
            #{item.latitude},
            #{item.altitude},
            #{item.level},
            #{item.acreage},
            #{item.age},
            #{item.owner},
            #{item.subjection},
            #{item.monomerDescription},
            #{item.description},
            #{item.preserveStatus},
            #{item.statusDescription},
            #{item.naturalReason},
            #{item.artificialReason},
            #{item.reasonDescription},
            #{item.environment},
            #{item.humanity},
            #{item.importTime}
            )
        </foreach>
    </insert>

    <select id="getCjRuinsInfoList" resultMap="BaseResultMap">
        select id,name,category,city,county,address,longitude,latitude,altitude,level,acreage,age,
        owner,subjection,monomer_description,description,preserve_status,status_description,
        natural_reason,artificial_reason,reason_description,environment,humanity,import_time,
        parent_id,create_time,update_time,
        ( select description from sys_code where id = level ) levelName,
        ( select description from sys_code where id = category ) categoryName
        From cj_ruins_info
        where 1 = 1
        <if test="params.level != '' and params.level != null">
            and level = #{params.level}
        </if>
        <if test="params.category != '' and params.category != null">
            and category = #{params.category}
        </if>
        <if test="params.age != null and params.age.size() > 0">
            and (
            <trim prefixOverrides="or">
                <foreach collection="params.age" item="item">
                    or age like CONCAT('%',#{item},'%')
                </foreach>
            </trim>
            )
        </if>
        <if test="params.name != null and params.name !='' ">
            and name like CONCAT('%',#{params.name,jdbcType=VARCHAR},'%')
        </if>
        <if test="sort != null and sort != ''">
            order by ${sort} ${order}
        </if>
    </select>

    <select id="getCjRuinsInfoAll" resultMap="selectList">
        select id tid, name ruinsName
        From cj_ruins_info
        where category = #{type}
    </select>

    <select id="selectChildren" resultType="com.das.business.changjiang.entity.CjRuinsInfoOld">
        select id, name
        From cj_ruins_info
        where parent_id = #{id}
    </select>

    <select id="getRuinsDetail" resultMap="BaseResultMap">
        select id, name, CONCAT(longitude, ',', latitude) gps
        from cj_ruins_info where 1=1
        <if test="category != null and category != ''">
            and category in ${category}
        </if>
        <if test="agesList != null">
            and (
              <foreach collection="agesList" separator="or" item="item" index="index">
                  age like concat('%',#{item},'%')
              </foreach>
            )
        </if>
    </select>

    <select id="getCjRuinsInfo" resultMap="selectList">
        SELECT DISTINCT
            x.id tid,
            x.`name` ruinsName,
            CONCAT(x.longitude, ',', x.latitude) gps
        FROM
            (
                SELECT
                    a.id,
                    a.`name`,
                    ( SELECT description FROM sys_code WHERE id = a.category ) categoryName,
                    a.city,
                    a.county,
                    a.address,
                    a.longitude,
                    a.latitude,
                    a.altitude,
                    ( SELECT description FROM sys_code WHERE id = a.`level` ) levelName,
                    a.acreage,
                    a.age,
                    a.`owner`,
                    a.subjection,
                    a.monomer_description,
                    a.description,
                    a.preserve_status,
                    a.status_description,
                    a.natural_reason,
                    a.artificial_reason,
                    a.reason_description,
                    a.environment,
                    a.humanity,
                    a.import_time,
                    b.`name` relicsName,
                    c.`name` materialName,
                    ( SELECT description FROM sys_code WHERE id = c.type ) materialType,
                    d.file_name materialFileName,
                    e.`name` digitName,
                    ( SELECT description FROM sys_code WHERE id = e.type ) digitType
                FROM
                    cj_ruins_info a
                        LEFT JOIN cj_ruins_unearthed_relics b ON a.id = b.ruins_id
                        LEFT JOIN cj_ruins_unearthed_material c ON a.id = c.ruins_id
                        LEFT JOIN cj_unearthed_material_file d ON c.id = d.material_id
                        LEFT JOIN cj_ruins_digit e ON a.id = e.ruins_id
            ) x
        <where>
            <if test="keyword != null and keyword !='' ">
                or x.`name` like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.categoryName like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.city like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.county like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.address like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.levelName like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.age like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.`owner` like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.subjection like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.monomer_description like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.description like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.preserve_status like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.status_description like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.natural_reason like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.artificial_reason like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.reason_description like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.environment like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.humanity like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.relicsName like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.materialName like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.materialType like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.materialFileName like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.digitName like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
                or x.digitType like CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
            </if>


        </where>
    </select>

</mapper>
