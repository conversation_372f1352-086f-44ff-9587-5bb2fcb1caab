<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.business.changjiang.mapper.CjCwhUnearthedRelicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjCwhUnearthedRelic">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="cover" property="cover" />
        <result column="classify" property="classify" />
        <result column="classify_name" property="classifyName" />
        <result column="material_quality" property="materialQuality" />
        <result column="material_quality_name" property="materialQualityName" />
        <result column="be_unearthed" property="beUnearthed" />
        <result column="unearthed_unit" property="unearthedUnit" />
        <result column="main_decoration" property="mainDecoration" />
        <result column="age" property="age" />
        <result column="collection_unit" property="collectionUnit" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
            id,
            name,
            cover,
            classify,
            (select description from sys_code where field = 'musical_type' and code = a.classify) as classify_name,
            material_quality,
            (select description from sys_code where field = 'material_type' and code = a.material_quality) as material_quality_name,
            be_unearthed,
            unearthed_unit,
            main_decoration,
            age,
            (select description from sys_code where id = a.age)age_name,
            collection_unit,
            create_time,
            update_time
        from cj_cwh_unearthed_relic a
        where 1 = 1
        <if test="params.keyWord != null and params.keyWord != ''">
            and concat(IFNULL(name, ''), '|',
            IFNULL(cover, ''), '|',
            IFNULL(classify, ''), '|',
            IFNULL(material_quality, '|'),
            IFNULL(be_unearthed, '|'),
            IFNULL(unearthed_unit, '|'),
            IFNULL(main_decoration, '|'),
            IFNULL(collection_unit, '|')
            ) like concat('%', #{params.keyWord},'%')
        </if>
        <if test="params.material!= null and params.material!= ''">
            and material_quality = #{params.material}
        </if>
        <if test="params.startTime!= null and params.startTime!= ''">
            and create_time &gt;= #{params.startTime} and create_time &lt;= #{params.endTime}
        </if>
        <if test="params.age!= null and params.age!= ''">
            and age in (${params.age})
        </if>
        order by ${sort} ${order}
    </select>

</mapper>
