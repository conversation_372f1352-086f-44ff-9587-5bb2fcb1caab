<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.business.changjiang.mapper.CjRuinsUnearthedMaterialMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjRuinsUnearthedMaterial">
        <result column="id" property="id"/>
        <result column="ruinsId" property="ruinsId"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="cover" property="cover"/>
        <result column="thumbnail" property="thumbnail"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="typeName" property="typeName" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getCjRuinsMaterialList" resultMap="BaseResultMap">
        select id,ruins_id,name,type,cover,thumbnail,create_time,update_time,
        ( select description from sys_code where id = type ) typeName
        From cj_ruins_unearthed_material
        where 1 = 1
        <if test="params.ruinsId != '' and params.ruinsId != null">
            and ruins_id = #{params.ruinsId}
        </if>
        <if test="params.type != '' and params.type != null">
            and type = #{params.type}
        </if>
        <if test="params.name != null and params.name !='' ">
            and name like CONCAT('%',#{params.name,jdbcType=VARCHAR},'%')
        </if>
        <if test="sort != null and sort != ''">
            order by ${sort} ${order}
        </if>
    </select>

    <select id="getTypeAndCount" resultType="com.das.business.changjiang.entity.responseEntity.TypeAndCount">
        SELECT * FROM
        (
        SELECT type,( SELECT description FROM sys_code WHERE id = type ) typeName,COUNT( type ) count
        FROM `cj_ruins_unearthed_material` WHERE ruins_id = #{ruinsId} GROUP BY type

        UNION ALL

        SELECT id,description,0 count
        FROM sys_code
        WHERE
        field = 'unearthedType' AND is_last_child = 1
        AND id NOT IN ( SELECT type FROM `cj_ruins_unearthed_material` WHERE ruins_id = #{ruinsId} GROUP BY type )) a
        ORDER BY type
    </select>

    <select id="getMaterialNames" resultType="string">
        select name from cj_ruins_unearthed_material where ruins_id = #{ruinsId}
    </select>

    <select id="getMaterialTypes" resultType="string">
        SELECT DISTINCT
            b.description
        FROM
            cj_ruins_unearthed_material a LEFT JOIN sys_code b ON a.type = b.id
        WHERE
            ruins_id = #{ruinsId}
    </select>

</mapper>
