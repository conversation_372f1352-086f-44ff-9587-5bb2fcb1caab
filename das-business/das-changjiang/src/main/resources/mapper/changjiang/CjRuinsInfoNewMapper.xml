<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.business.changjiang.mapper.CjRuinsInfoNewMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjRuinsInfoNew">
        <id column="id" property="id" />
        <result column="parent_id" property="parentId" />
        <result column="name" property="name" />
        <result column="category" property="category" />
        <result column="city" property="city" />
        <result column="county" property="county" />
        <result column="address" property="address" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="altitude" property="altitude" />
        <result column="discover_tombs" property="discoverTombs" />
        <result column="prove_tombs" property="proveTombs" />
        <result column="discover_time" property="discoverTime" />
        <result column="age" property="age" />
        <result column="discover_log" property="discoverLog" />
        <result column="tombs_layout" property="tombsLayout" />
        <result column="environment" property="environment" />
        <result column="surrounding_ruins" property="surroundingRuins" />
        <result column="report" property="report" />
        <result column="other_info" property="otherInfo" />
        <result column="layout_plan_url" property="layoutPlanUrl" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="getRuinsDetail" resultType="com.das.business.changjiang.controller.vo.CjRuinsInfoMapVO">
        select id, name, CONCAT(longitude, ',', latitude) gps,discover_time,age,report
        from cj_ruins_info where 1=1
        <!-- <if test="category != null and category != ''">
            and category in ${category}
        </if>
        <if test="agesList != null">
            and (
            <foreach collection="agesList" separator="or" item="item" index="index">
                age like concat('%',#{item},'%')
            </foreach>
            )
        </if> -->
    </select>

</mapper>
