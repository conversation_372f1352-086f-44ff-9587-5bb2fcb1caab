<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.business.changjiang.mapper.CjRuinsInfoTombsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjRuinsInfoTombs">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="dynasty" property="dynasty" />
        <result column="steal_info" property="stealInfo" />
        <result column="fief_have" property="fiefHave" />
        <result column="fief_shape" property="fiefShape" />
        <result column="fief_bottom_diameter" property="fiefBottomDiameter" />
        <result column="fief_height" property="fiefHeight" />
        <result column="hole_shape" property="holeShape" />
        <result column="hole_direction" property="holeDirection" />
        <result column="hole_top_long" property="holeTopLong" />
        <result column="hole_top_width" property="holeTopWidth" />
        <result column="hole_bottom_length" property="holeBottomLength" />
        <result column="hole_bottom_width" property="holeBottomWidth" />
        <result column="hole_bottom_height" property="holeBottomHeight" />
        <result column="hole_step" property="holeStep" />
        <result column="hole_passage_have" property="holePassageHave" />
        <result column="hole_passage_number" property="holePassageNumber" />
        <result column="hole_passage_total_length" property="holePassageTotalLength" />
        <result column="hole_passage_top_length" property="holePassageTopLength" />
        <result column="hole_passage_top_length_length" property="holePassageTopLengthLength" />
        <result column="hole_passage_top_short_length" property="holePassageTopShortLength" />
        <result column="hole_passage_bottom_length" property="holePassageBottomLength" />
        <result column="hole_passage_angle" property="holePassageAngle" />
        <result column="fill_soil" property="fillSoil" />
        <result column="other" property="other" />
        <result column="coffin_number" property="coffinNumber" />
        <result column="divide_number" property="divideNumber" />
        <result column="way" property="way" />
        <result column="sex" property="sex" />
        <result column="age" property="age" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
            id,
            ruins_id,
            name,
            dynasty,
            steal_info,
            fief_have,
            fief_shape,
            fief_bottom_diameter,
            fief_height,
            hole_shape,
            hole_direction,
            hole_top_long,
            hole_top_width,
            hole_bottom_length,
            hole_bottom_width,
            hole_bottom_height,
            hole_step,
            hole_passage_have,
            hole_passage_number,
            hole_passage_total_length,
            hole_passage_top_length,
            hole_passage_top_length_length,
            hole_passage_top_short_length,
            hole_passage_bottom_length,
            hole_passage_angle,
            fill_soil,
            other,
            coffin_number,
            divide_number,
            way,
            sex,
            age,
            remark,
            create_time,
            update_time
        from cj_ruins_info_tombs
        where ruins_id = #{params.ruinsId}
    </select>

</mapper>
