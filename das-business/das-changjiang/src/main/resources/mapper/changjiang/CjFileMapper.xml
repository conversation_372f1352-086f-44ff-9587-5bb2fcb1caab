<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://Mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.das.business.changjiang.mapper.CjFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjFile">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="save_name" property="saveName" />
        <result column="load_path" property="loadPath" />
        <result column="res_id" property="resId" />
        <result column="create_time" property="createTime" />
        <result column="file_type" property="fileType" />
        <result column="parent_id" property="parentId" />
    </resultMap>

    <select id="getCjFile" resultType="com.das.business.changjiang.entity.CjFile">
        select id ,name ,save_name , load_path , res_id , create_time , file_type , parent_id from cj_file
        where 1=1
        <if test="resId != ''">
            and res_id =#{resId}
        </if>
    </select>

</mapper>
