<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://Mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.das.business.changjiang.mapper.CjRuinsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjRuins">
        <id column="id" property="id" />
        <result column="major_id" property="majorId" />
        <result column="minor_id" property="minorId" />
        <result column="name" property="name" />
        <result column="desc" property="desc" />
        <result column="excavate_age" property="excavateAge" />
        <result column="ruins_age" property="ruinsAge" />
        <result column="parent_id" property="parentId" />
        <result column="leaf_node" property="leafNode" />
        <result column="create_time" property="createTime" />
        <collection property="children" column="{parentId=id}" select="findChildren"/>
    </resultMap>

    <select id="findChildren" resultType="com.das.business.changjiang.entity.CjRuins">
        select *
        from cj_ruins
        where parent_id = #{id}
    </select>

    <select id="findRuins" resultMap="BaseResultMap" parameterType="com.das.business.changjiang.entity.CjRuins">
        select id, major_id, minor_id, name, `desc`, excavate_age, ruins_age, parent_id,create_time, leaf_node
               from cj_ruins where major_id = #{cjRuins.majorId} and minor_id = #{cjRuins.minorId}
        <choose>
            <when test="cjRuins.parentId!=null and cjRuins.parentId!=0">
                and parent_id = #{cjRuins.parentId}
            </when>
            <otherwise>
                and parent_id != 0
                <choose>
                    <when test="cjRuins.minorId==4">
                        and leaf_node = 1
                    </when>
                    <otherwise>
                        and leaf_node = 0
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
        <!--<if test="cjRuins.leafNode!=null and cjRuins.leafNode!=''">
            and leafNode = #{cjRuins.leafNode}
        </if>-->
        <if test="cjRuins.name!=null and cjRuins.name!=''">
            and name like concat('%',#{cjRuins.name},'%')
        </if>

    </select>


    <select id="getRuinsList" resultType="com.das.business.changjiang.entity.CjRuins">
        select id, major_id, minor_id, name, `desc`, excavate_age, ruins_age, parent_id, leaf_node,create_time,
        case when major_id = 1 then '湖北地区史前遗址点'
            when major_id = 2 then '西南茶马古道遗址'
        end majorIdName,
        name as majorIdName,
        case when minor_id = 1 then '可移动文物'
            when minor_id = 2 then '不可移动文物'
            when minor_id = 3 then '历史地点'
            when minor_id = 4 then '古道路'
        end minorIdName
        from cj_ruins
        where 1 = 1
        <if test="params.majorId != '' and params.majorId != null">
            and major_id = #{params.majorId}
        </if>
        <if test="params.minorId != '' and params.minorId != null">
            and minor_id = #{params.minorId}
        </if>
        order by ${sort} ${order}
    </select>


    <select id="getRuinsTotal" resultType="java.util.HashMap">
        SELECT if(major_id = 1,'湖北地区史前遗址点','西南茶马古道遗址') name,
               SUM(IF(minor_id = 1,1,0)) as 'moveRelic',
                SUM(IF(minor_id = 2,1,0)) as 'unmoveRelic',
                SUM(IF(minor_id = 3,1,0)) as 'history',
                SUM(IF(minor_id = 4,1,0)) as 'road'
        FROM cj_ruins
        where 1 = 1
        <if test="startTime !='' and startTime !=null ">
            and DATE_FORMAT(create_time,'%Y-%m-%d') &gt;= DATE_FORMAT( #{startTime}, '%Y-%m-%d' )
        </if>
        <if test="endTime !='' and endTime !=null ">
            and DATE_FORMAT(create_time,'%Y-%m-%d') &lt;= DATE_FORMAT( #{endTime}, '%Y-%m-%d' )
        </if>
        GROUP BY major_id
    </select>

    <select id="getCjReportTotal" resultType="java.util.HashMap">
        SELECT
        (select description from sys_code where id = type) name,
        SUM(IF( data_type = 135, 1, 0 )) AS 'file',
        SUM(IF( data_type = 137, 1, 0 )) AS 'picture'
        FROM
        cj_report
        where 1 = 1
        <if test="startTime !='' and startTime !=null ">
            and DATE_FORMAT(create_time,'%Y-%m-%d') &gt;= DATE_FORMAT( #{startTime}, '%Y-%m-%d' )
        </if>
        <if test="endTime !='' and endTime !=null ">
            and DATE_FORMAT(create_time,'%Y-%m-%d') &lt;= DATE_FORMAT( #{endTime}, '%Y-%m-%d' )
        </if>
        GROUP BY type
    </select>

    <select id="getCollectInfo" resultType="java.util.HashMap">
        SELECT
        (select description From sys_code where id = type ) name,
        count(1) as num
        FROM
        cj_collection_info
        where 1 = 1
        <if test="startTime !='' and startTime !=null ">
            and DATE_FORMAT(create_time,'%Y-%m-%d') &gt;= DATE_FORMAT( #{startTime}, '%Y-%m-%d' )
        </if>
        <if test="endTime !='' and endTime !=null ">
            and DATE_FORMAT(create_time,'%Y-%m-%d') &lt;= DATE_FORMAT( #{endTime}, '%Y-%m-%d' )
        </if>
        GROUP BY type
    </select>

    <select id="getRuinsById" resultType="com.das.business.changjiang.entity.CjRuins">
        select id, major_id, minor_id, name, `desc`, excavate_age, ruins_age, parent_id, leaf_node,create_time,
        case when major_id = 1 then '湖北地区史前遗址点'
        when major_id = 2 then '西南茶马古道遗址'
        end majorIdName,
        case when minor_id = 1 then '可移动文物'
        when minor_id = 2 then '不可移动文物'
        when minor_id = 3 then '历史地点'
        when minor_id = 4 then '古道路'
        end minorIdName
        from cj_ruins
        where id =#{id}
    </select>


</mapper>
