<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://Mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.das.business.changjiang.mapper.CjMarkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjMark">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="content" property="content"/>
        <result column="type" property="type"/>
        <result column="color" property="color"/>
        <result column="user_id" property="userId"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <select id="getCjMarkList" resultMap="BaseResultMap">
        select id, name, content, type, color, update_time, create_time,user_id,
        (select description from sys_code where id = type) typeName
        from cj_mark
        where 1 = 1
         and user_id = #{params.userId}
        <if test="params.name != null and params.name != ''">
           and name like CONCAT('%',#{params.name},'%')
        </if>
        <!-- 数据时间范围的起始时间 -->
        <if test="params.startTime !='' and params.startTime !=null ">
            and DATE_FORMAT(create_time,'%Y-%m-%d') &gt;= DATE_FORMAT( #{params.startTime}, '%Y-%m-%d' )
        </if>
        <!-- 数据时间范围的终止时间 -->
        <if test="params.endTime !='' and params.endTime !=null ">
            and DATE_FORMAT(create_time,'%Y-%m-%d') &lt;= DATE_FORMAT( #{params.endTime}, '%Y-%m-%d' )
        </if>
        order by ${sort} ${order}
    </select>

    <select id="getCjMarkById" resultMap="BaseResultMap">
        select id, name, content, type, color, update_time, create_time,user_id,
        (select description from sys_code where id = type) typeName
        from cj_mark
        where id =#{id}
    </select>

</mapper>
