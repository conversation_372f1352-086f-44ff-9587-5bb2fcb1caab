<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.business.changjiang.mapper.CjCwhLiteratureMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjCwhLiterature">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="keyword" property="keyword" />
        <result column="author" property="author" />
        <result column="sign_unit" property="signUnit" />
        <result column="publication_year" property="publicationYear" />
        <result column="unearthed_unit" property="unearthedUnit" />
        <result column="press" property="press" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
            id,
            name,
            keyword,
            author,
            sign_unit,
            publication_year,
            unearthed_unit,
            press,
            create_time,
            update_time
        from cj_cwh_literature
        where 1 = 1
        <if test="params.keyWord != null and params.keyWord != ''">
            and concat(IFNULL(name, ''), '|',
            IFNULL(keyword, ''), '|',
            IFNULL(author, ''), '|',
            IFNULL(sign_unit, '|'),
            IFNULL(publication_year, '|'),
            IFNULL(unearthed_unit, '|'),
            IFNULL(press, '|')
            ) like concat('%', #{params.keyWord},'%')
        </if>
        <if test="params.material!= null and params.material!= ''">
            and material = #{params.material}
        </if>
        <if test="params.year!= null and params.year!= ''">
            and publication_year &gt;= SUBSTRING_INDEX(#{params.year}, '-', 1) and publication_year &lt;= SUBSTRING_INDEX(#{params.year}, '-', -1)
        </if>
        <if test="params.startTime!= null and params.startTime!= ''">
            and create_time &gt;= #{params.startTime} and create_time &lt;= #{params.endTime}
        </if>
        order by ${sort} ${order}
    </select>

</mapper>
