<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.business.changjiang.mapper.CjHistoryMapsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjHistoryMaps">
        <id column="id" property="id"/>
        <result column="map_name" property="mapName"/>
        <result column="map_type" property="mapType"/>
        <result column="map_content" property="mapContent"/>
        <result column="age" property="age"/>
        <result column="map_file" property="mapFile"/>
        <result column="remarks" property="remarks"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="publish_status" property="publishStatus"/>
        <result column="publish_status_name" property="publishStatusName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, map_name, map_type, map_content, age, map_file, remarks,
        create_time, update_time, publish_status
    </sql>

    <!-- 分页查询历史地图列表 -->
    <select id="getCjHistoryMapsList" resultMap="BaseResultMap">
        SELECT 
            hm.id, hm.map_name, hm.map_type, hm.map_content, hm.age, hm.map_file,
            hm.remarks, hm.create_time, hm.update_time, hm.publish_status,
            CASE hm.publish_status 
                WHEN 0 THEN '未发布' 
                WHEN 1 THEN '已发布' 
                ELSE '未知' 
            END AS publish_status_name
        FROM cj_history_maps hm
        <where>
            <if test="params.mapName != null and params.mapName != ''">
                AND hm.map_name LIKE CONCAT('%', #{params.mapName}, '%')
            </if>
            <if test="params.mapType != null ">
                AND hm.map_type = #{params.mapType}
            </if>
            <if test="params.age != null ">
                AND hm.age = #{params.age}
            </if>
            <if test="params.publishStatus != null ">
                AND hm.publish_status = #{params.publishStatus}
            </if>
            <if test="params.startTime != null and params.startTime != ''">
                AND hm.create_time >= #{params.startTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND hm.create_time &lt;= #{params.endTime}
            </if>
        </where>
        <choose>
            <when test="sort != null and sort != '' and order != null and order != ''">
                ORDER BY ${sort} ${order}
            </when>
            <otherwise>
                ORDER BY hm.create_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 根据ID查询历史地图详情 -->
    <select id="getCjHistoryMapsById" resultMap="BaseResultMap">
        SELECT 
            hm.id, hm.map_name, hm.map_type, hm.map_content, hm.age, hm.map_file,
            hm.remarks, hm.create_time, hm.update_time, hm.publish_status,
            CASE hm.publish_status 
                WHEN 0 THEN '未发布' 
                WHEN 1 THEN '已发布' 
                ELSE '未知' 
            END AS publish_status_name
        FROM cj_history_maps hm
        WHERE hm.id = #{id}
    </select>

</mapper>
