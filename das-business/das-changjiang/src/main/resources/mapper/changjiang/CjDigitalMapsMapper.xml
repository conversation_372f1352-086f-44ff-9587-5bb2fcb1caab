<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.business.changjiang.mapper.CjDigitalMapsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjDigitalMaps">
        <id column="id" property="id"/>
        <result column="map_name" property="mapName"/>
        <result column="map_type_group" property="mapTypeGroup"/>
        <result column="map_file" property="mapFile"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="description" property="description"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="publish_status" property="publishStatus"/>
        <result column="publish_status_name" property="publishStatusName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, map_name, map_type_group, map_file, longitude, latitude, description, 
        create_time, update_time, publish_status
    </sql>

    <!-- 分页查询数字地图列表 -->
    <select id="getCjDigitalMapsList" resultMap="BaseResultMap">
        SELECT 
            dm.id, dm.map_name, dm.map_type_group, dm.map_file, dm.longitude, dm.latitude, 
            dm.description, dm.create_time, dm.update_time, dm.publish_status,
            CASE dm.publish_status 
                WHEN 0 THEN '未发布' 
                WHEN 1 THEN '已发布' 
                ELSE '未知' 
            END AS publish_status_name
        FROM cj_digital_maps dm
        <where>
            <if test="params.mapName != null and params.mapName != ''">
                AND dm.map_name LIKE CONCAT('%', #{params.mapName}, '%')
            </if>
            <if test="params.mapTypeGroup != null and params.mapTypeGroup != ''">
                AND dm.map_type_group LIKE CONCAT('%', #{params.mapTypeGroup}, '%')
            </if>
            <if test="params.publishStatus != null and params.publishStatus != ''">
                AND dm.publish_status = #{params.publishStatus}
            </if>
            <if test="params.startTime != null and params.startTime != ''">
                AND dm.create_time >= #{params.startTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND dm.create_time &lt;= #{params.endTime}
            </if>
        </where>
        <choose>
            <when test="sort != null and sort != '' and order != null and order != ''">
                ORDER BY ${sort} ${order}
            </when>
            <otherwise>
                ORDER BY dm.create_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 根据ID查询数字地图详情 -->
    <select id="getCjDigitalMapsById" resultMap="BaseResultMap">
        SELECT 
            dm.id, dm.map_name, dm.map_type_group, dm.map_file, dm.longitude, dm.latitude, 
            dm.description, dm.create_time, dm.update_time, dm.publish_status,
            CASE dm.publish_status 
                WHEN 0 THEN '未发布' 
                WHEN 1 THEN '已发布' 
                ELSE '未知' 
            END AS publish_status_name
        FROM cj_digital_maps dm
        WHERE dm.id = #{id}
    </select>

</mapper>
