<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.business.changjiang.mapper.CjProblemFeedbackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjProblemFeedback">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="send_content" property="sendContent"/>
        <result column="reply_content" property="replyContent"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="back_status" property="backStatus"/>
        <result column="back_status_name" property="backStatusName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, send_content, reply_content, create_time, update_time, back_status
    </sql>

    <!-- 分页查询问题反馈列表 -->
    <select id="getCjProblemFeedbackList" resultMap="BaseResultMap">
        SELECT 
            pf.id, pf.title, pf.send_content, pf.reply_content, 
            pf.create_time, pf.update_time, pf.back_status,
            CASE pf.back_status 
                WHEN 0 THEN '未回复' 
                WHEN 1 THEN '已回复' 
                ELSE '未知' 
            END AS back_status_name
        FROM cj_problem_feedback pf
        <where>
            <if test="params.title != null and params.title != ''">
                AND pf.title LIKE CONCAT('%', #{params.title}, '%')
            </if>
            <if test="params.sendContent != null and params.sendContent != ''">
                AND pf.send_content LIKE CONCAT('%', #{params.sendContent}, '%')
            </if>
            <if test="params.backStatus != null and params.backStatus != ''">
                AND pf.back_status = #{params.backStatus}
            </if>
            <if test="params.startTime != null and params.startTime != ''">
                AND pf.create_time >= #{params.startTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND pf.create_time &lt;= #{params.endTime}
            </if>
        </where>
        <choose>
            <when test="sort != null and sort != '' and order != null and order != ''">
                ORDER BY ${sort} ${order}
            </when>
            <otherwise>
                ORDER BY pf.create_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 根据ID查询问题反馈详情 -->
    <select id="getCjProblemFeedbackById" resultMap="BaseResultMap">
        SELECT 
            pf.id, pf.title, pf.send_content, pf.reply_content, 
            pf.create_time, pf.update_time, pf.back_status,
            CASE pf.back_status 
                WHEN 0 THEN '未回复' 
                WHEN 1 THEN '已回复' 
                ELSE '未知' 
            END AS back_status_name
        FROM cj_problem_feedback pf
        WHERE pf.id = #{id}
    </select>

</mapper>
