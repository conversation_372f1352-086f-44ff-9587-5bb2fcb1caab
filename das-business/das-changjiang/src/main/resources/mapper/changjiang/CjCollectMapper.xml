<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://Mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.das.business.changjiang.mapper.CjCollectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.das.business.changjiang.entity.CjCollect">
        <id column="id" property="id"/>
        <result column="source_id" property="sourceId"/>
        <result column="type" property="type"/>
        <result column="create_time" property="createTime"/>
        <result column="user_id" property="userId"/>
    </resultMap>
    <select id="getCjCollectList" resultMap="BaseResultMap">
        select id, source_id, type, create_time,user_id,
        case when type = 1 then (select name from cj_report where id = source_id)
             when type = 0 then (select name from cj_collection_info where id = source_id)
        end name
        From cj_collect
        where 1 = 1
        <if test="params.type != null and params.type != ''">
            and type = #{params.type}
        </if>

        <if test="params.userId != null and params.userId != ''">
            and user_id = #{params.userId}
        </if>

        <!-- 数据时间范围的起始时间 -->
        <if test="params.startTime !='' and params.startTime !=null ">
            and DATE_FORMAT(create_time,'%Y-%m-%d') &gt;= DATE_FORMAT( #{params.startTime}, '%Y-%m-%d' )
        </if>
        <!-- 数据时间范围的终止时间 -->
        <if test="params.endTime !='' and params.endTime !=null ">
            and DATE_FORMAT(create_time,'%Y-%m-%d') &lt;= DATE_FORMAT( #{params.endTime}, '%Y-%m-%d' )
        </if>
        order by ${sort} ${order}
    </select>

    <select id="getCjCollectById" resultMap="BaseResultMap">
        select id, source_id, type, create_time,user_id,
        case when type = 1 then (select name from cj_report where id = source_id)
        when type = 0 then (select name from cj_collection_info where id = source_id)
        end name
        From cj_collect
        where id =#{id}
    </select>
</mapper>
