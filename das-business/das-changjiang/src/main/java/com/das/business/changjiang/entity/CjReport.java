package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
@Data
public class CjReport extends Model<CjReport> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 主题词
     */
    private Integer theme;

    /**
     * 范围（全国、湖北、云南）
     */
    @TableField(value = "`range`")
    private Integer range;

    /**
     * 年代
     */
    private Integer age;

    /**
     * 数据贡献者
     */
    private String contributor;

    /**
     * 数据分类（研究、发掘等）
     */
    private Integer type;

    /**
     * 数据类型（文档、图片）
     */
    private Integer dataType;

    /**
     * 预览图
     */
    private String viewImg;

    /**
     * 是否可下载（0可下载 1不可下载）
     */
    private Integer isDownload;

    /**
     * 是否收藏(0:否;1:是
     */
    @TableField(exist = false)
    private Integer isCollect;

    /**
     * 附件（图片或者PDF地址）
     */
    private String file;

    /**
     *文件大小
     */
    private int fileSize;

    /**
     * 时空定位
     */
    private String gps;

    /**
     * 发布
     */
    private int publish;

    /**
     * 访问量
     */
    private int pageView;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 发布时间
     */
    private String publishTime;

    /**
     * 创建时间
     */
    private String createTime;

    @TableField(exist = false)
    private String themeName;

    @TableField(exist = false)
    private String rangeName;

    @TableField(exist = false)
    private String ageName;

    @TableField(exist = false)
    private String typeName;

    @JsonIgnore
    @TableField(exist = false)
    private String startTime;

    @JsonIgnore
    @TableField(exist = false)
    private String endTime;

    @TableField(exist = false)
    private List<CjFile> cjFiles;

    @TableField(exist = false)
    private int flag;

    @TableField(exist = false)
    private String dataTypeName;

}
