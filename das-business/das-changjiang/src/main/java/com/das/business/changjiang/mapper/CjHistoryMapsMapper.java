package com.das.business.changjiang.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.entity.CjHistoryMaps;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * 历史地图表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
public interface CjHistoryMapsMapper extends BaseMapper<CjHistoryMaps> {

    /**
     * 分页查询历史地图列表
     *
     * @param page   分页参数
     * @param params 查询参数
     * @param sort   排序字段
     * @param order  排序方式
     * @return 分页结果
     */
    IPage<CjHistoryMaps> getCjHistoryMapsList(@Param(value = "page") Page<CjHistoryMaps> page,
                                              @Param(value = "params") Map<String, Object> params,
                                              @Param(value = "sort") String sort, 
                                              @Param(value = "order") String order);

    /**
     * 根据ID查询历史地图详情
     *
     * @param id 历史地图ID
     * @return 历史地图详情
     */
    CjHistoryMaps getCjHistoryMapsById(@Param(value = "id") Long id);
}
