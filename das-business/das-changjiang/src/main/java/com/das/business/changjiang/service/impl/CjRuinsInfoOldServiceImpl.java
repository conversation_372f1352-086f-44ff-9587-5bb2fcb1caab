package com.das.business.changjiang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.business.changjiang.entity.*;
import com.das.business.changjiang.mapper.CjRuinsInfoOldMapper;
import com.das.business.changjiang.service.*;
import com.das.es.entity.EsEntity;
import com.das.es.service.EsService;
import com.das.system.entity.SysCode;
import com.das.system.mapper.SysCodeMapper;
import com.das.system.service.ISysCodeService;
import com.das.utils.common.ExcelUtil;
import com.das.utils.response.PageEntity;
import lombok.val;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【cj_ruins_info(考古遗址基础信息表)】的数据库操作Service实现
 * @createDate 2022-05-20 17:04:24
 */
//@Service
public class CjRuinsInfoOldServiceImpl  implements CjRuinsInfoOldService {

//    @Resource
    private CjRuinsInfoOldMapper cjRuinsInfoMapper;

    @Resource
    private ICjRuinsDigitService cjRuinsDigitService;

    @Resource
    private ICjRuinsUnearthedMaterialService cjRuinsUnearthedMaterialService;

    @Resource
    private ICjRuinsUnearthedRelicsService cjRuinsUnearthedRelicsService;

    @Resource
    private SysCodeMapper sysCodeMapper;

    @Resource
    private EsService esService;

    @Resource
    private ISysCodeService sysCodeService;

    @Resource
    private ICjUnearthedMaterialFileService materialFileService;

    @Override
    public IPage<CjRuinsInfoOld> getCjRuinsInfoList(PageEntity pageEntity) {
        return cjRuinsInfoMapper.getCjRuinsInfoList(
                new Page<CjRuinsInfoOld>(pageEntity.getCurrent(), pageEntity.getSize()),
                pageEntity.getParams(),
                pageEntity.getSort(),
                pageEntity.getOrder());
    }

    @Override
    public void ExportCjRuinsInfoList(String level, String category, String age, String name, String[] ids, HttpServletResponse response) {
        Set<CjRuinsInfoOld> cjRuinsInfos = new HashSet<>();

        List<CjRuinsInfoView> list = new ArrayList<>();

        String[] ages = {};
        if (StringUtils.isNotBlank(age)) {
            ages = age.split(";");
        }

        if (ages.length > 0) {
            for (int i = 0; i < ages.length; i++) {
                //获取数据列表
                QueryWrapper<CjRuinsInfoOld> wrapper = new QueryWrapper<>();

                if (StringUtils.isNotBlank(level)) {
                    wrapper.eq("level", level);
                }
                if (StringUtils.isNotBlank(category)) {
                    wrapper.eq("category", category);
                }
                if (StringUtils.isNotBlank(name)) {
                    wrapper.like("name", name);
                }
                if (ids != null && ids.length != 0) {
                    wrapper.in("id", ids);
                }
                wrapper.like("age", ages[i]);
                List<CjRuinsInfoOld> resultList = null;//cjRuinsInfoMapper.selectList(wrapper);
                cjRuinsInfos.addAll(resultList);
            }
        } else {
            //获取数据列表
            QueryWrapper<CjRuinsInfoOld> wrapper = new QueryWrapper<>();

            if (StringUtils.isNotBlank(level)) {
                wrapper.eq("level", level);
            }
            if (StringUtils.isNotBlank(category)) {
                wrapper.eq("category", category);
            }
            if (StringUtils.isNotBlank(name)) {
                wrapper.like("name", name);
            }
            if (ids != null && ids.length != 0) {
                wrapper.in("id", ids);
            }

            List<CjRuinsInfoOld> resultList = null;//cjRuinsInfoMapper.selectList(wrapper);
            cjRuinsInfos.addAll(resultList);
        }


        //获取导入的参数字典
        //类型
        Map<Long, String> categoryMap = new HashMap<>();
        //级别
        Map<Long, String> levelMap = new HashMap<>();
        //年代
        //Map<Long, String> ageMap = new HashMap<>();
        //保存状况
        Map<Long, String> preserveStatusMap = new HashMap<>();

        QueryWrapper<SysCode> wrapper2 = new QueryWrapper();
        List<SysCode> codeList = sysCodeMapper.selectList(wrapper2);
        for (SysCode sysCode : codeList) {
            if (sysCode.getParentId() == 610) {
                categoryMap.put(sysCode.getId(), sysCode.getDescription());
            }

            if (sysCode.getParentId() == 614) {
                levelMap.put(sysCode.getId(), sysCode.getDescription());
            }

//            if (sysCode.getParentId() == 457
//                    || sysCode.getParentId() == 461
//                    || sysCode.getParentId() == 463
//                    || sysCode.getParentId() == 464
//                    || sysCode.getParentId() == 466
//                    || sysCode.getParentId() == 467
//                    || sysCode.getParentId() == 492
//                    || sysCode.getParentId() == 493
//                    || sysCode.getParentId() == 469
//                    || sysCode.getParentId() == 470
//                    || sysCode.getParentId() == 480
//                    || sysCode.getId() == 457) {
//                ageMap.put(sysCode.getId(), sysCode.getDescription());
//            }

            if (sysCode.getParentId() == 77) {
                preserveStatusMap.put(sysCode.getId(), sysCode.getDescription());
            }
        }

        for (CjRuinsInfoOld cjRuinsInfo : cjRuinsInfos) {
            CjRuinsInfoView view = new CjRuinsInfoView();
            BeanUtils.copyProperties(cjRuinsInfo, view);
            //字段类型不一致的特殊处理
            view.setCategory(categoryMap.get(cjRuinsInfo.getCategory()));
            view.setLevel(levelMap.get(cjRuinsInfo.getLevel()));
            view.setAge(cjRuinsInfo.getAge());
            view.setPreserveStatus(preserveStatusMap.get(cjRuinsInfo.getPreserveStatus()));
            list.add(view);
        }

        ExcelUtil.export(response, list, "遗址概况", "",
                CjRuinsInfoView.class);
    }

    @Override
    public boolean saveCjRuinsInfo(CjRuinsInfoOld cjRuinsInfo) {
        boolean flag = false;//this.saveOrUpdate(cjRuinsInfo);
        if (flag) {
            // 添加or修改到es中
            CjRuinsInfoOld byId = null;//this.getById(cjRuinsInfo.getId());
            EsEntity esEntity = new EsEntity();
            esEntity.setTid(byId.getId())
                    .setRuinsName(cjRuinsInfo.getName())
                    .setCategory(this.getDesByCode(cjRuinsInfo.getCategory()))
                    .setCity(cjRuinsInfo.getCity())
                    .setCounty(cjRuinsInfo.getCounty())
                    .setAddress(cjRuinsInfo.getAddress())
                    .setLongitude(cjRuinsInfo.getLongitude())
                    .setLatitude(cjRuinsInfo.getLatitude())
                    .setAltitude(cjRuinsInfo.getAltitude())
                    .setLevel(this.getDesByCode(cjRuinsInfo.getLevel()))
                    .setAcreage(cjRuinsInfo.getAcreage())
                    .setAge(cjRuinsInfo.getAge())
                    .setOwner(cjRuinsInfo.getOwner())
                    .setSubjection(cjRuinsInfo.getSubjection())
                    .setMonomerDescription(cjRuinsInfo.getMonomerDescription())
                    .setDescription(cjRuinsInfo.getDescription())
                    .setPreserveStatus(this.getDesByCode(cjRuinsInfo.getPreserveStatus()))
                    .setStatusDescription(cjRuinsInfo.getStatusDescription())
                    .setNaturalReason(cjRuinsInfo.getNaturalReason())
                    .setArtificialReason(cjRuinsInfo.getArtificialReason())
                    .setReasonDescription(cjRuinsInfo.getReasonDescription())
                    .setEnvironment(cjRuinsInfo.getEnvironment())
                    .setHumanity(cjRuinsInfo.getHumanity())
                    .setResourceId(byId.getId());
            esService.saveEs(esEntity);

        }
        return flag;
    }

    @Override
    public boolean delCjRuinsInfo(List<Long> ids) {
        boolean flag = false;//this.removeByIds(ids);

        if (flag) {
            val digitReq = new QueryWrapper<CjRuinsDigit>()
                    .in("ruins_id", ids);
            cjRuinsDigitService.remove(digitReq);
            val materialReq = new QueryWrapper<CjRuinsUnearthedMaterial>()
                    .in("ruins_id", ids);
            List<CjRuinsUnearthedMaterial> ruinsMaterialList = cjRuinsUnearthedMaterialService.list(materialReq);
            if (cjRuinsUnearthedMaterialService.remove(materialReq)) {
                ArrayList<Long> materialIds = new ArrayList<>();
                ruinsMaterialList.forEach(ruinsMaterial -> materialIds.add(ruinsMaterial.getId()));
                val materialFileReq = new QueryWrapper<CjUnearthedMaterialFile>()
                        .in("material_id", ids);
                materialFileService.remove(materialFileReq);
            }
            val relicsReq = new QueryWrapper<CjRuinsUnearthedRelics>()
                    .in("ruins_id", ids);
            cjRuinsUnearthedRelicsService.remove(relicsReq);
            // 删除es中数据
            ids.forEach(id -> esService.delEs(id));
        }
        return true;
    }

    @Override
    public CjRuinsInfoOld getCjRuinsInfoById(Long id) {
        return null;//this.getById(id);
    }

    @Override
    public List<CjRuinsInfoOld> getCjRuinsInfoAll(Long type) {
        return cjRuinsInfoMapper.getCjRuinsInfoAll(type);
    }

    @Override
    public List<CjRuinsInfoOld> getRuinsDetail(Map<String, String> params) {
        String category = null;
        if (params.get("ids") != null) {
            category = "(" + params.get("ids").replace("34", "611").replace("35", "612").replace("36", "613") + ")";
        }
        List<String> argsList = null;
        if (params.get("ages") != null) {
            argsList = Arrays.stream(params.get("ages").split(",")).collect(Collectors.toList());
        }
        if (params.get("parentId") != null) {
            for (String parentId : params.get("parentId").split(",")) {
                SysCode sysCode = sysCodeService.getById(parentId);
                argsList.add(sysCode.getDescription());
            }
        }
        return cjRuinsInfoMapper.getRuinsDetail(category, argsList);
    }

    @Override
    public List<CjRuinsInfoOld> getCjRuinsInfo(String keyword) {
        return cjRuinsInfoMapper.getCjRuinsInfo(keyword);
    }

    private String getDesByCode(Long codeId) {
        if (codeId == null) return "";
        SysCode sysCode = sysCodeService.getById(codeId);
        return sysCode != null ? sysCode.getDescription() : "";
    }

}