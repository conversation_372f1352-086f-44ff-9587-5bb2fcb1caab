package com.das.business.changjiang.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.business.changjiang.entity.CjCwhCemetery;
import com.das.business.changjiang.entity.CjCwhLiterature;
import com.das.business.changjiang.mapper.CjCwhLiteratureMapper;
import com.das.business.changjiang.service.ICjCwhLiteratureService;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultMsg;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 文献资料信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-02
 */
@Service
public class CjCwhLiteratureServiceImpl extends ServiceImpl<CjCwhLiteratureMapper, CjCwhLiterature> implements ICjCwhLiteratureService {

    @Resource
    CjCwhLiteratureMapper literatureMapper;

    @Override
    public IPage<CjCwhLiterature> findByPage(PageEntity pageEntity) {
        return literatureMapper.findByPage(new Page<>(pageEntity.getCurrent(), pageEntity.getSize()),
                pageEntity.getParams(), pageEntity.getSort(), pageEntity.getOrder());
    }

    public boolean importExcel(MultipartFile file) {
        List<CjCwhLiterature> list = new ArrayList<>();
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(inputStream);
            // 循环工作表Sheet
            HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(0);
            // 循环行Row
            for (int rowNum = 1; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
                CjCwhLiterature literature = new CjCwhLiterature();
                HSSFRow hssfRow = hssfSheet.getRow(rowNum);
                if (hssfRow == null) {
                    continue;
                }
                // 名称
                HSSFCell xh0 = hssfRow.getCell(0);
                if (xh0 != null) {
                    xh0.setCellType(CellType.STRING);
                    String stringCellValue = xh0.getStringCellValue();
                    if (StringUtils.isEmpty(stringCellValue)) {
                        continue;
                    }
                    literature.setName(stringCellValue);
                }
                HSSFCell xh1 = hssfRow.getCell(1);
                if (xh1 != null) {
                    xh1.setCellType(CellType.STRING);
                    String stringCellValue = xh1.getStringCellValue();
                    literature.setKeyword(stringCellValue);
                }
                HSSFCell xh2 = hssfRow.getCell(2);
                if (xh2 != null) {
                    xh2.setCellType(CellType.STRING);
                    String stringCellValue = xh2.getStringCellValue();
                    literature.setAuthor(stringCellValue);
                }
                HSSFCell xh3 = hssfRow.getCell(3);
                if (xh3 != null) {
                    xh3.setCellType(CellType.STRING);
                    String stringCellValue = xh3.getStringCellValue();
                    literature.setSignUnit(stringCellValue);
                }
                HSSFCell xh4 = hssfRow.getCell(4);
                if (xh4 != null) {
                    xh4.setCellType(CellType.STRING);
                    String stringCellValue = xh4.getStringCellValue();
                    literature.setPublicationYear(stringCellValue);
                }
                HSSFCell xh5 = hssfRow.getCell(5);
                if (xh5 != null) {
                    xh5.setCellType(CellType.STRING);
                    String stringCellValue = xh5.getStringCellValue();
                    literature.setUnearthedUnit(stringCellValue);
                }
                HSSFCell xh6 = hssfRow.getCell(6);
                if (xh6 != null) {
                    xh6.setCellType(CellType.STRING);
                    String stringCellValue = xh6.getStringCellValue();
                    literature.setPress(stringCellValue);
                }
                list.add(literature);
            }
            return this.saveBatch(list);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                }
            }
        }
        return false;
    }
}
