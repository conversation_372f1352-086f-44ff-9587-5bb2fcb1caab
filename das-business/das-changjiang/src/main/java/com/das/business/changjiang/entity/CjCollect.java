package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
@Data
public class CjCollect extends Model<CjCollect> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 资源编号
     */
    private Long sourceId;

    /**
     * 资源类型（0文物1文献）
     */
    private Integer type;

    /**
     * 收藏时间
     */
    private String createTime;

    /**
     * 用户id
     */
    private Integer userId;

    @JsonIgnore
    @TableField(exist = false)
    private String startTime;

    @JsonIgnore
    @TableField(exist = false)
    private String endTime;

    @TableField(exist = false)
    private String name;

}
