package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 考古遗址基础信息表
 *
 * @TableName cj_ruins_info
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CjRuinsInfoOld implements Serializable {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 文物名称
     */
    @ApiModelProperty(value = "考古遗址名称")
    private String name;

    /**
     * 类别
     */
    @ApiModelProperty(value = "考古遗址类别")
    private Long category;

    /**
     * 市
     */
    @ApiModelProperty(value = "市")
    private String city;

    /**
     * 县
     */
    @ApiModelProperty(value = "县")
    private String county;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    private String address;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private double longitude;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private double latitude;

    /**
     * 海拔高度
     */
    @ApiModelProperty(value = "海拔高度")
    private double altitude;

    /**
     * 级别
     */
    @ApiModelProperty(value = "级别")
    private Long level;

    /**
     * 面积
     */
    @ApiModelProperty(value = "面积")
    private double acreage;

    /**
     * 年代
     */
    @ApiModelProperty(value = "年代")
    private String age;

    /**
     * 使用者
     */
    @ApiModelProperty(value = "使用者")
    private String owner;

    /**
     * 隶属
     */
    @ApiModelProperty(value = "隶属")
    private String subjection;

    /**
     * 单体描述
     */
    @ApiModelProperty(value = "单体描述")
    private String monomerDescription;

    /**
     * 简介
     */
    @ApiModelProperty(value = "简介")
    private String description;

    /**
     * 保存状况
     */
    @ApiModelProperty(value = "保存状况")
    private Long preserveStatus;

    /**
     * 状态描述
     */
    @ApiModelProperty(value = "状态描述")
    private String statusDescription;

    /**
     * 自然损毁原因
     */
    @ApiModelProperty(value = "自然损毁原因")
    private String naturalReason;

    /**
     * 人为损毁原因
     */
    @ApiModelProperty(value = "人为损毁原因")
    private String artificialReason;

    /**
     * 损毁原因描述
     */
    @ApiModelProperty(value = "损毁原因描述")
    private String reasonDescription;

    /**
     * 环境
     */
    @ApiModelProperty(value = "环境")
    private String environment;

    /**
     * 人文
     */
    @ApiModelProperty(value = "人文")
    private String humanity;

    /**
     * 导入时间
     */
    @ApiModelProperty(value="导入时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date importTime;

    /**
     * 所属父类
     */
    @ApiModelProperty(value = "所属父类")
    private Long parentId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * 遗址级别名称
     */
    @TableField(exist = false)
    private String levelName;

    /**
     * 遗址类型名称
     */
    @TableField(exist = false)
    private String categoryName;

    @TableField(exist = false)
    private List<CjRuinsInfoOld> cjRuinsChildren;

    @TableField(exist = false)
    private String gps;
}