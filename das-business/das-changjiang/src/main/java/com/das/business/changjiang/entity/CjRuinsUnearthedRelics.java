package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 遗址出土文物实体
 * </p>
 *
 * <AUTHOR>
 * @since 2022-5-23
 */ 
@Data
public class CjRuinsUnearthedRelics implements Serializable {

    // 主键ID
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(name = "id", value = "主键ID", required = true)
    private Long id;

    @ApiModelProperty(name = "ruinsId", value = "遗址id", required = false)
    private Long ruinsId;

    @ApiModelProperty(name = "cover", value = "封面图片地址", required = false)
    private String cover;

    @ApiModelProperty(name = "thumbnail", value = "封面缩略图", required = false)
    private String thumbnail;

    @ApiModelProperty(name = "name", value = "出土文物名称", required = false)
    private String name;

    @ApiModelProperty(name = "unearthedTime", value = "出土时间", required = false)
    private String unearthedTime;

    @ApiModelProperty(name = "type", value = "出土文物类型", required = false)
    private Long type;

    @ApiModelProperty(name = "description", value = "描述", required = false)
    private String description;

    @ApiModelProperty(name = "age", value = "年代", required = false)
    private Long age;

    @ApiModelProperty(name = "texture", value = "质地", required = false)
    private Long texture;

    @ApiModelProperty(name = "length", value = "长（厘米）", required = false)
    private Double length;

    @ApiModelProperty(name = "width", value = "宽（厘米）", required = false)
    private Double width;

    @ApiModelProperty(name = "high", value = "高（厘米）", required = false)
    private Double high;

    @ApiModelProperty(name = "complete", value = "完残程度", required = false)
    private Long complete;

    @ApiModelProperty(name = "level", value = "级别", required = false)
    private Long level;

    @ApiModelProperty(name = "classifyNumber", value = "分类号", required = false)
    private String classifyNumber;

    @ApiModelProperty(name = "registrationNumber", value = "总登记号", required = false)
    private String registrationNumber;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间", required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(name = "updateTime", value = "修改时间", required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 出土文物类型名称
     */
    @TableField(exist = false)
    private String typeName;

    /**
     * 出土文物年代名称
     */
    @TableField(exist = false)
    private String ageName;

    /**
     * 出土文物完残程度名称
     */
    @TableField(exist = false)
    private String completeName;

    /**
     * 出土文物级别名称
     */
    @TableField(exist = false)
    private String levelName;

}
