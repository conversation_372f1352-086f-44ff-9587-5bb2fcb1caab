package com.das.business.changjiang.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.business.changjiang.entity.CjRuinsUnearthedMaterial;
import com.das.business.changjiang.entity.responseEntity.TypeAndCount;
import com.das.utils.response.PageEntity;

import java.util.List;

/**
 * <p>
 * 服务类接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-5-23
 */
public interface ICjRuinsUnearthedMaterialService extends IService<CjRuinsUnearthedMaterial> {

    /**
     * 分页查询
     */
    IPage<CjRuinsUnearthedMaterial> getCjRuinsMaterialList(PageEntity pageEntity);

    /**
     * 新增or修改考古遗址发掘资料
     */
    boolean saveCjRuinsMaterial(CjRuinsUnearthedMaterial cjRuinsMaterial);

    /**
     * 删除考古遗址发掘资料
     */
    boolean delCjRuinsMaterial(List<Long> ids);

    /**
     * 根据id查询考古遗址发掘资料
     */
    CjRuinsUnearthedMaterial getCjRuinsMaterialById(Long id);

    /**
     * 根据id查询考古遗址发掘资料分类
     *
     * @param ruinsId
     * @return
     */
    List<TypeAndCount> getTypeAndCount(Long ruinsId);

}
