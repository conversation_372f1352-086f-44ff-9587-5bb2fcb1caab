package com.das.business.changjiang.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.entity.CjReport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.das.system.entity.SysCode;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
public interface CjReportMapper extends BaseMapper<CjReport> {

    IPage<CjReport> getCjReportList(@Param(value = "page") Page<CjReport> page,
                             @Param(value = "params") Map<String, Object> params,
                             @Param(value = "sort") String sort, @Param(value = "order") String order);

    CjReport getCjReportById(@Param(value = "id") Long id,@Param(value = "userId") Long userId);

    List<CjReport> getCjReportByRuinsId(@Param(value = "id") Long id, @Param(value = "userId") Long userId);




}
