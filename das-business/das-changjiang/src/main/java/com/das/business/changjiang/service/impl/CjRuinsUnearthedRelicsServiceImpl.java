package com.das.business.changjiang.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.business.changjiang.entity.CjRuinsUnearthedRelics;
import com.das.business.changjiang.entity.responseEntity.TypeAndCount;
import com.das.business.changjiang.mapper.CjRuinsUnearthedRelicsMapper;
import com.das.business.changjiang.service.ICjRuinsUnearthedRelicsService;
import com.das.es.dao.EsEntityRepository;
import com.das.es.entity.EsEntity;
import com.das.utils.common.ImgUtil;
import com.das.utils.response.PageEntity;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-5-23
 */
@Service
@Transactional
@Slf4j
public class CjRuinsUnearthedRelicsServiceImpl extends ServiceImpl<CjRuinsUnearthedRelicsMapper, CjRuinsUnearthedRelics> implements ICjRuinsUnearthedRelicsService {

    @Resource
    private CjRuinsUnearthedRelicsMapper cjRuinsRelicsMapper;

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Resource
    private EsEntityRepository esEntityRepository;

    @Override
    public IPage<CjRuinsUnearthedRelics> getCjRuinsRelicsList(PageEntity pageEntity) {
        return cjRuinsRelicsMapper.getCjRuinsRelicsList(
                new Page<CjRuinsUnearthedRelics>(pageEntity.getCurrent(), pageEntity.getSize()),
                pageEntity.getParams(),
                pageEntity.getSort(),
                pageEntity.getOrder());
    }

    @Override
    public boolean saveCjRuinsRelics(CjRuinsUnearthedRelics cjRuinsRelics) {
        String cover = cjRuinsRelics.getCover();
        cjRuinsRelics.setThumbnail(ImgUtil.getThumbnail(cover));
        boolean flag = this.saveOrUpdate(cjRuinsRelics);
        if (flag) {
            // 添加到es中
            String relicsNames =
                    String.join(",", cjRuinsRelicsMapper.getRelicNames(cjRuinsRelics.getRuinsId()));
            EsEntity esEntity = new EsEntity();
            esEntity.setTid(cjRuinsRelics.getRuinsId())
                    .setRelicsNames(relicsNames)
                    .setResourceId(cjRuinsRelics.getRuinsId());
            //this.saveEs(esEntity);
        }
        return flag;
    }

    @Override
    public boolean updateCjRuinsRelics(CjRuinsUnearthedRelics cjRuinsRelics) {
        CjRuinsUnearthedRelics unearthedRelics = this.getById(cjRuinsRelics.getId());
        if (!unearthedRelics.getCover().equals(cjRuinsRelics.getCover()) || unearthedRelics.getThumbnail() == null) {
            String cover = cjRuinsRelics.getCover();
            cjRuinsRelics.setThumbnail(ImgUtil.getThumbnail(cover));
        }
        boolean flag = this.saveOrUpdate(cjRuinsRelics);
        if (flag) {
            // 修改es中数据
            String relicsNames =
                    String.join(",", cjRuinsRelicsMapper.getRelicNames(cjRuinsRelics.getRuinsId()));
            EsEntity esEntity = new EsEntity();
            esEntity.setTid(cjRuinsRelics.getRuinsId())
                    .setRelicsNames(relicsNames)
                    .setResourceId(cjRuinsRelics.getRuinsId());
            this.saveEs(esEntity);
        }
        return flag;
    }

    @Override
    public boolean delCjRuinsRelics(List<Long> ids) {
        Long ruinsId = this.getById(ids.get(0)).getRuinsId();
        boolean flag = this.removeByIds(ids);
        if (flag) {
            // 修改es中数据
            ids.forEach(id -> {
                String relicsNames = String.join(",", cjRuinsRelicsMapper.getRelicNames(ruinsId));
                EsEntity esEntity = new EsEntity();
                esEntity.setTid(ruinsId)
                        .setRelicsNames(relicsNames)
                        .setResourceId(ruinsId);
                //this.saveEs(esEntity);
            });
        }
        return flag;
    }

    @Override
    public CjRuinsUnearthedRelics getCjRuinsRelicsById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<TypeAndCount> getTypeAndCount(Long ruinsId) {
        return cjRuinsRelicsMapper.getTypeAndCount(ruinsId);
    }

    /**
     * 插入es
     *
     * @param entity
     */
    public void saveEs(EsEntity entity) {
        //查询es中是否存在
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilders.boolQuery()
                        .must(QueryBuilders.matchQuery("tid", entity.getTid()))
                )
                .build();
        SearchHits<EsEntity> searchHits = elasticsearchRestTemplate.search(query, EsEntity.class);
        if (searchHits.getTotalHits() > 0) {
            for (SearchHit<EsEntity> searchHit : searchHits) {
                EsEntity esEntity = searchHit.getContent();
                Document document = Document.create();
                document.put("relicsNames", entity.getRelicsNames());
                elasticsearchRestTemplate.update(UpdateQuery.builder(esEntity.getId())
                        .withDocument(document).build(), IndexCoordinates.of("cj_ruins_info"));
            }
        } else {
            EsEntity es = new EsEntity();
            es.setTid(entity.getTid())
                    .setRelicsNames(entity.getRelicsNames())
                    .setResourceId(entity.getResourceId());
            log.info("插入es成功");
            esEntityRepository.save(es);
        }
    }

}
