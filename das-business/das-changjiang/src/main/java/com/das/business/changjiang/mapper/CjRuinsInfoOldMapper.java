package com.das.business.changjiang.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.entity.CjRuinsInfoOld;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【cj_ruins_info(考古遗址基础信息表)】的数据库操作Mapper
 * @createDate 2022-05-20 17:04:24
 * @Entity com.das.business.changjiang.entity.CjRuinsInfo
 */
public interface CjRuinsInfoOldMapper {

    int batchInsert(List<CjRuinsInfoOld> list);

    /**
     * 分页查询
     *
     * @param page
     * @param params
     * @param sort
     * @param order
     * @return
     */
    IPage<CjRuinsInfoOld> getCjRuinsInfoList(@Param(value = "page") Page<CjRuinsInfoOld> page,
                                             @Param(value = "params") Map<String, Object> params,
                                             @Param(value = "sort") String sort,
                                             @Param(value = "order") String order);

    /**
     * 获取考古遗址列表
     *
     * @param type
     * @return
     */
    List<CjRuinsInfoOld> getCjRuinsInfoAll(Long type);

    /**
     * 获取考古遗址gps
     * @param category
     * @param agesList
     * @return
     */
    List<CjRuinsInfoOld> getRuinsDetail(@Param(value = "category") String category, @Param(value = "agesList") List<String> agesList);

    /**
     * 全文检索考古遗址列表
     *
     * @param keyword
     * @return
     */
    List<CjRuinsInfoOld> getCjRuinsInfo(@Param(value = "keyword") String keyword);

}
