package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
@Data
public class CjMark extends Model<CjMark> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 标注内容（存前台JSON）
     */
    private String content;

    /**
     * 类型（0点1线2面3文字）
     */
    private Integer type;

    /**
     * 颜色
     */
    private String color;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 修改时间
     */
    private String updateTime;

    /**
     * 创建时间
     */
    private String createTime;

    @TableField(exist = false)
    private String typeName;

    @JsonIgnore
    @TableField(exist = false)
    private String startTime;

    @JsonIgnore
    @TableField(exist = false)
    private String endTime;

}
