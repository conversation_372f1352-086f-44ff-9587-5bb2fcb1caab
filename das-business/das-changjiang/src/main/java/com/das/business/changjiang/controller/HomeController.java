package com.das.business.changjiang.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.das.annotation.DasController;
import com.das.business.changjiang.service.ICjRuinsService;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 首页接口
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@DasController("/home")
@Api(description = "首页")
public class HomeController {

    @Resource
    ICjRuinsService cjRuinsService;

    /**
     * 获取首页登录查询考古坐标数据
     *
     * @return
     */
    @RequestMapping(value = "/getHome", method = RequestMethod.GET)
    @ApiOperation(value = "获取首页登录查询考古坐标数据")
    public ResultMsg getHome() {
        QueryWrapper majorQue = new QueryWrapper();
        majorQue.select("major_id", "name","`desc`").eq("parent_id", "0");
        List<Map> majorArr = cjRuinsService.listMaps(majorQue);
        majorArr.forEach(item->{
            item.put("gps",item.get("desc"));
            item.remove("desc");
        });
        return new ResultMsg(majorArr);
    }


}
