package com.das.business.changjiang.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.business.changjiang.entity.CjProblemFeedback;
import com.das.business.changjiang.mapper.CjProblemFeedbackMapper;
import com.das.business.changjiang.service.ICjProblemFeedbackService;
import com.das.system.entity.PageEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * <p>
 * 问题反馈表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@Service
@Transactional
@Slf4j
public class CjProblemFeedbackServiceImpl extends ServiceImpl<CjProblemFeedbackMapper, CjProblemFeedback> implements ICjProblemFeedbackService {

    @Resource
    private CjProblemFeedbackMapper cjProblemFeedbackMapper;

    @Override
    public IPage<CjProblemFeedback> getCjProblemFeedbackList(PageEntity pageEntity) {
        return cjProblemFeedbackMapper.getCjProblemFeedbackList(
                new Page<>(pageEntity.getCurrent(), pageEntity.getSize()),
                pageEntity.getParams(),
                pageEntity.getSort(),
                pageEntity.getOrder()
        );
    }

    @Override
    public CjProblemFeedback getCjProblemFeedbackById(Long id) {
        return cjProblemFeedbackMapper.getCjProblemFeedbackById(id);
    }

    @Override
    public boolean saveCjProblemFeedback(CjProblemFeedback cjProblemFeedback) {
        try {
            if (cjProblemFeedback.getId() == null) {
                // 新增
                cjProblemFeedback.setCreateTime(LocalDateTime.now());
                cjProblemFeedback.setUpdateTime(LocalDateTime.now());
                if (cjProblemFeedback.getBackStatus() == null) {
                    cjProblemFeedback.setBackStatus(0); // 默认未回复
                }
                return this.save(cjProblemFeedback);
            } else {
                // 更新
                cjProblemFeedback.setUpdateTime(LocalDateTime.now());
                return this.updateById(cjProblemFeedback);
            }
        } catch (Exception e) {
            log.error("保存问题反馈失败", e);
            return false;
        }
    }

    @Override
    public boolean deleteCjProblemFeedback(Long id) {
        try {
            return this.removeById(id);
        } catch (Exception e) {
            log.error("删除问题反馈失败", e);
            return false;
        }
    }

    @Override
    public boolean batchDeleteCjProblemFeedback(Long[] ids) {
        try {
            return this.removeByIds(Arrays.asList(ids));
        } catch (Exception e) {
            log.error("批量删除问题反馈失败", e);
            return false;
        }
    }

    @Override
    public boolean replyProblemFeedback(Long id, String replyContent) {
        try {
            if (!StringUtils.hasText(replyContent)) {
                log.error("回复内容不能为空");
                return false;
            }
            
            CjProblemFeedback problemFeedback = new CjProblemFeedback();
            problemFeedback.setId(id);
            problemFeedback.setReplyContent(replyContent);
            problemFeedback.setBackStatus(1); // 设置为已回复
            problemFeedback.setUpdateTime(LocalDateTime.now());
            return this.updateById(problemFeedback);
        } catch (Exception e) {
            log.error("回复问题反馈失败", e);
            return false;
        }
    }

    @Override
    public boolean updateBackStatus(Long id, Integer backStatus) {
        try {
            CjProblemFeedback problemFeedback = new CjProblemFeedback();
            problemFeedback.setId(id);
            problemFeedback.setBackStatus(backStatus);
            problemFeedback.setUpdateTime(LocalDateTime.now());
            return this.updateById(problemFeedback);
        } catch (Exception e) {
            log.error("更新问题反馈状态失败", e);
            return false;
        }
    }
}
