package com.das.business.changjiang.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.entity.CjProblemFeedback;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * 问题反馈表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
public interface CjProblemFeedbackMapper extends BaseMapper<CjProblemFeedback> {

    /**
     * 分页查询问题反馈列表
     *
     * @param page   分页参数
     * @param params 查询参数
     * @param sort   排序字段
     * @param order  排序方式
     * @return 分页结果
     */
    IPage<CjProblemFeedback> getCjProblemFeedbackList(@Param(value = "page") Page<CjProblemFeedback> page,
                                                      @Param(value = "params") Map<String, Object> params,
                                                      @Param(value = "sort") String sort, 
                                                      @Param(value = "order") String order);

    /**
     * 根据ID查询问题反馈详情
     *
     * @param id 问题反馈ID
     * @return 问题反馈详情
     */
    CjProblemFeedback getCjProblemFeedbackById(@Param(value = "id") Long id);
}
