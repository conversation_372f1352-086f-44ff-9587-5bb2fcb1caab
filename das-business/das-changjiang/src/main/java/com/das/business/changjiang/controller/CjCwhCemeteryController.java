package com.das.business.changjiang.controller;


import cn.hutool.core.net.multipart.UploadFileHeader;
import com.das.annotation.DasController;
import com.das.business.changjiang.entity.CjCwhCemetery;
import com.das.business.changjiang.service.ICjCwhCemeteryService;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;

/**
 * <p>
 * 墓葬信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-02
 */
@Api(value = "墓葬信息表", description = "墓葬信息表")
@DasController("/cjCwhCemetery")
public class CjCwhCemeteryController {

    @Resource
    ICjCwhCemeteryService cemeteryService;

    @ApiOperation(value = "分页查询")
    @RequestMapping(value = "/findByPage", method = RequestMethod.POST)
    public ResultMsg findByPage(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(cemeteryService.findByPage(pageEntity));
    }

    @ApiOperation(value = "根据ID查询详情")
    @RequestMapping(value = "/getById", method = RequestMethod.POST)
    public ResultMsg getById(@RequestBody CjCwhCemetery cwhCemetery) {
        return new ResultMsg(cemeteryService.getById(cwhCemetery.getId()));
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "/save")
    public ResultMsg save(@RequestBody CjCwhCemetery cwhCemetery) {
        return new ResultMsg(cemeteryService.save(cwhCemetery));
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "/updateById")
    public ResultMsg updateById(@RequestBody CjCwhCemetery cwhCemetery) {
        return new ResultMsg(cemeteryService.updateById(cwhCemetery));
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "/removeByIds")
    public ResultMsg removeByIds(@RequestBody List<Long> ids) {
        return new ResultMsg(cemeteryService.removeByIds(ids));
    }

    @ApiOperation(value = "导入")
    @PostMapping(value = "/importExcel")
    public ResultMsg importExcel(MultipartFile file) {
        return new ResultMsg(cemeteryService.importExcel(file));
    }

}
