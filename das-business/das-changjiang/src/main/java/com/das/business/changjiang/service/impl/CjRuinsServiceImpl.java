package com.das.business.changjiang.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.entity.CjRuins;
import com.das.business.changjiang.entity.CjRuinsDetail;
import com.das.business.changjiang.mapper.CjRuinsMapper;
import com.das.business.changjiang.service.ICjRuinsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.system.entity.SysUser;
import com.das.utils.common.CangPConstant;
import com.das.utils.common.SystemUtil;
import com.das.utils.response.PageEntity;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.spring.web.json.Json;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Service
@Transactional
@Slf4j
public class CjRuinsServiceImpl extends ServiceImpl<CjRuinsMapper, CjRuins> implements ICjRuinsService {

    @Resource
    private CjRuinsMapper cjRuinsMapper;

    @Resource
    private CjRuinsDetailServiceImpl cjRuinsDetailService;

    @Override
    public List<CjRuins> findRuins(CjRuins cjRuins) {
        if(cjRuins.getParentId() == null){
            cjRuins.setLeafNode(1);
        }
        return cjRuinsMapper.findRuins(cjRuins);
    }

    @Override
    public boolean saveRuins(CjRuins cjRuins, MultipartFile file) {
        boolean flag = cjRuins.getId() != null;
        val wrapper = new QueryWrapper<CjRuins>();
        wrapper.select("id, major_id, minor_id, name, `desc`, excavate_age, ruins_age, parent_id, leaf_node").eq("id",cjRuins.getMajorId());
        CjRuins ruins = list(wrapper).get(0);
        cjRuins.setParentId(ruins.getId());
        cjRuins.setLeafNode(cjRuins.getType() == 1?1:0);
        if(saveOrUpdate(cjRuins)){
            if( file != null && !file.isEmpty()){
                if(flag){
                    //如果是修改，先删除之前的数据再插入
                    val q = new QueryWrapper<CjRuins>();
                    q.eq("parent_id",cjRuins.getId()).or().eq("id",cjRuins.getId());
                    List<Long> ids = list(q).stream().map(CjRuins::getId).collect(Collectors.toList());
                    if(ids.size() > 0){
                        val wrap = new QueryWrapper<CjRuinsDetail>();
                        wrap.in("parent_id",ids);
                        cjRuinsDetailService.remove(wrap);
                        ids.remove(cjRuins.getId());
                        if(ids.size() > 0){
                            removeByIds(ids);
                        }
                    }
                }
                String json = readFileToJson(file);
                if(json != null){
                    //如果为线则不插入json中的明细点
                    if(cjRuins.getType() == 0){
                        json = saveJson(cjRuins, json);
                    }
                    if(json == null){
                        return false;
                    }
                    CjRuinsDetail detail = new CjRuinsDetail();
                    saveDetail(cjRuins, json, detail);
                }
            }
        }
        return true;
    }

    @Override
    public boolean delRuins(Long id) {
        val q = new QueryWrapper<CjRuinsDetail>();
        q.eq("parent_id",id);
        cjRuinsDetailService.remove(q);
        return removeById(id);
    }

    @Override
    public IPage<CjRuins> getRuinsList(PageEntity pageEntity) {
        return cjRuinsMapper.getRuinsList(new Page<>(pageEntity.getCurrent(),pageEntity.getSize()),
                pageEntity.getParams(),pageEntity.getSort(),pageEntity.getOrder());
    }

    @Override
    public CjRuins getRuinsById(Long id) {
        return cjRuinsMapper.getRuinsById(id);
    }

    @Override
    public JSONObject getRuinsTotal(CjRuins cjRuins) {
        //1：遗址点 2：考古报告 3：文物
        switch (cjRuins.getType()){
            case 1 : return getRuinsInfo(cjRuins);
            case 2 : return getReportInfo(cjRuins);
            case 3 : return getCollectInfo(cjRuins);
            default: return null;
        }
    }

    private JSONObject getRuinsInfo(CjRuins cjRuins) {
        List list = cjRuinsMapper.getRuinsTotal(cjRuins);
        String[] Name = new String[list.size()];
        String[] valueName = CangPConstant.RuinsType;
        Integer[] value1 = new Integer[list.size()];
        Integer[] value2 = new Integer[list.size()];
        Integer[] value3 = new Integer[list.size()];
        Integer[] value4 = new Integer[list.size()];
        for (int i = 0; i < list.size(); i++) {
            Map map = (Map) list.get(i);
            Name[i] = map.get("name").toString();
            value1[i] = Integer.parseInt(map.get("moveRelic").toString());
            value2[i] = Integer.parseInt(map.get("unmoveRelic").toString());
            value3[i] = Integer.parseInt(map.get("history").toString());
            value4[i] = Integer.parseInt(map.get("road").toString());
        }
        ArrayList lst = new ArrayList();
        lst.add(value1);
        lst.add(value2);
        lst.add(value3);
        lst.add(value4);
        Map map = new HashMap();
        map.put("key", Name);
        map.put("value", lst);
        map.put("valueName", valueName);
        JSONObject jsonObject = new JSONObject(map);
        return jsonObject;
    }

    private JSONObject getReportInfo(CjRuins cjRuins) {
        List list = cjRuinsMapper.getCjReportTotal(cjRuins);
        String[] Name = new String[list.size()];
        String[] valueName = CangPConstant.ReportType;
        Integer[] value1 = new Integer[list.size()];
        Integer[] value2 = new Integer[list.size()];
        for (int i = 0; i < list.size(); i++) {
            Map map = (Map) list.get(i);
            Name[i] = map.get("name").toString();
            value1[i] = Integer.parseInt(map.get("file").toString());
            value2[i] = Integer.parseInt(map.get("picture").toString());
        }
        ArrayList lst = new ArrayList();
        lst.add(value1);
        lst.add(value2);
        Map map = new HashMap();
        map.put("key", Name);
        map.put("value", lst);
        map.put("valueName", valueName);
        JSONObject jsonObject = new JSONObject(map);
        return jsonObject;
    }

    private JSONObject getCollectInfo(CjRuins cjRuins) {
        List list = cjRuinsMapper.getCollectInfo(cjRuins);
        String[] Name = new String[list.size()];
        Integer[] value = new Integer[list.size()];
        for (int i = 0; i < list.size(); i++) {
            Map map = (Map) list.get(i);
            Name[i] = map.get("name").toString();
            value[i] = Integer.parseInt(map.get("num").toString());
        }
        Map map = new HashMap();
        map.put("key", Name);
        map.put("value", value);
        JSONObject jsonObject = new JSONObject(map);
        return jsonObject;
    }

    //保存节点到主表以及明细表中
    private String saveJson(CjRuins cjRuins, String json) {
        JSONObject jsonObject = new JSONObject(json);
        JSONArray jsonArray = (JSONArray) jsonObject.get("features");
        for(int i = 0;i<jsonArray.size();i++){
            Map map =(Map)((Map)jsonArray.get(i)).get("properties");
            String name = "";
            if(map.containsKey("mc")){
                name = map.get("mc").toString();
            }else if(map.containsKey("Name")){
                name = map.get("Name").toString();
            }else{
                removeById(cjRuins.getId());
                return null;
            }
            CjRuins cj = new CjRuins();
            cj.setMajorId(cjRuins.getMajorId());
            cj.setMinorId(cjRuins.getMinorId());
            cj.setName(name);
            cj.setDesc(cjRuins.getDesc());
            cj.setExcavateAge(cjRuins.getExcavateAge());
            cj.setRuinsAge(cjRuins.getRuinsAge());
            cj.setParentId(cjRuins.getId());
            cj.setLeafNode(1);
            if(saveOrUpdate(cj)){
                Map mapDetail =(Map)((Map)jsonArray.get(i)).get("geometry");
                String gpsStr =mapDetail.get("coordinates").toString();
                String gps = "";
                if(gpsStr.contains(",0]")){
                    gps = gpsStr.substring(1,gpsStr.indexOf(",0]"));
                }else{
                    gps = gpsStr.substring(1,gpsStr.length()-1);
                }
                CjRuinsDetail cjRuinsDetail = new CjRuinsDetail();
                cjRuinsDetail.setName(name);
                cjRuinsDetail.setDesc(name);
                cjRuinsDetail.setExcavateAge(cjRuins.getExcavateAge());
                cjRuinsDetail.setRuinsAge(cjRuins.getRuinsAge());
                cjRuinsDetail.setGps(gps);
                cjRuinsDetail.setType(cjRuins.getType());
                cjRuinsDetail.setColor("red");
                cjRuinsDetail.setParentId(cj.getId());
                cjRuinsDetailService.saveOrUpdate(cjRuinsDetail);
                map.put("Id",cjRuinsDetail.getId());
            }
        }
        return jsonObject.toString();
    }

    //保存明细主信息
    private void saveDetail(CjRuins cjRuins, String json, CjRuinsDetail detail) {
        detail.setLineSet(json);
        detail.setName(cjRuins.getName());
        detail.setDesc(cjRuins.getName());
        detail.setExcavateAge(cjRuins.getExcavateAge());
        detail.setRuinsAge(cjRuins.getRuinsAge());
        detail.setType(cjRuins.getType());
        detail.setColor("red");
        detail.setParentId(cjRuins.getId());
        cjRuinsDetailService.saveOrUpdate(detail);
    }

    //解析json数据
    private String readFileToJson(MultipartFile file) {
        BufferedReader reader = null;
        StringBuilder ans = new StringBuilder();
        try {
            Reader read = new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8);
            reader = new BufferedReader(read);
            String tmp;
            while ((tmp = reader.readLine()) != null){
                ans.append(tmp);
            }
            return ans.toString();
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            if(reader != null){
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }
}
