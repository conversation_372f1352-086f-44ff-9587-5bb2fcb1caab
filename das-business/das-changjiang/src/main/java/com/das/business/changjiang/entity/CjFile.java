package com.das.business.changjiang.entity;

import cn.hutool.log.Log;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
@Data
public class CjFile extends Model<CjFile> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 源文件名称
     */
    private String name;

    /**
     * 存储名称
     */
    private String saveName;

    /**
     * 加载地址
     */
    private String loadPath;

    /**
     * 关联资源ID
     */
    private Long resId;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 文件类型（0源文件1缩略文件）
     */
    private Integer fileType;

    /**
     * 缩略文件父节点
     */
    private Long parentId;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 文件类型
     */
    @TableField(exist = false)
    private String suffix;

}
