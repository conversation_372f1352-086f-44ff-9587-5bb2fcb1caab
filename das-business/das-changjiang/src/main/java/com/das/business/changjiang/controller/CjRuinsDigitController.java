package com.das.business.changjiang.controller;


import com.das.annotation.RequestSecurity;
import com.das.business.changjiang.entity.CjRuinsDigit;
import com.das.business.changjiang.service.ICjRuinsDigitService;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 考古遗址成果模块
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
@Api(value = "考古遗址成果模块", description = "考古遗址成果模块")
@RestController
@RequestMapping("/cjRuinsDigit")
public class CjRuinsDigitController {

    @Resource
    private ICjRuinsDigitService cjRuinsDigitService;

    /**
     * 分页查询
     */
    @ApiOperation(value = "分页查询考古遗址成果信息")
    @RequestSecurity(value = "/getCjRuinsDigitList", method = RequestMethod.POST)
    public ResultMsg getCjRuinsDigitList(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(cjRuinsDigitService.getCjRuinsDigitList(pageEntity));
    }

    /**
     * 分页查询（展示端）
     */
    @ApiOperation(value = "分页查询考古遗址成果信息")
    @RequestSecurity(value = "/getRuinsDigitTypeAndCount", method = RequestMethod.POST)
    public ResultMsg getRuinsDigit(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(cjRuinsDigitService.getCjRuinsDigitList(pageEntity));
    }

    /**
     * 新增or修改考古遗址成果
     */
    @ApiOperation(value = "新增or修改考古遗址成果")
    @RequestSecurity(value = "/saveCjRuinsDigit", method = RequestMethod.POST)
    public ResultMsg saveCjRuinsDigit(@RequestBody CjRuinsDigit cjRuinsDigit) {
        return new ResultMsg(cjRuinsDigitService.saveCjRuinsDigit(cjRuinsDigit));
    }

    /**
     * 删除考古遗址成果
     */
    @ApiOperation(value = "删除考古遗址成果信息")
    @RequestSecurity(value = "/delCjRuinsDigit", method = RequestMethod.POST)
    public ResultMsg delCjRuinsDigit(@RequestBody List<Long> ids) {
        return new ResultMsg(cjRuinsDigitService.delCjRuinsDigit(ids));
    }

    /**
     * 根据id查询考古遗址成果
     */
    @ApiOperation(value = "根据id查询考古遗址成果")
    @RequestSecurity(value = "/getCjRuinsDigitById", method = RequestMethod.POST)
    public ResultMsg getCjRuinsDigitById(@RequestParam Long id) {
        return new ResultMsg(cjRuinsDigitService.getCjRuinsDigitById(id));
    }

    /**
     * 根据id查询考古遗址成果分类
     */
    @ApiOperation(value = "根据id查询考古遗址成果分类")
    @RequestSecurity(value = "/getTypeAndCount", method = RequestMethod.POST)
    public ResultMsg getTypeAndCount(@RequestParam Long ruinsId) {
        return new ResultMsg(cjRuinsDigitService.getTypeAndCount(ruinsId));
    }

}
