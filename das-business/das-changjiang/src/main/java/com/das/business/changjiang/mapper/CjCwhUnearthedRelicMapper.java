package com.das.business.changjiang.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.entity.CjCwhUnearthedRelic;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * 出土文物信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-02
 */
public interface CjCwhUnearthedRelicMapper extends BaseMapper<CjCwhUnearthedRelic> {

    IPage<CjCwhUnearthedRelic> findByPage(@Param(value = "page") Page<CjCwhUnearthedRelic> page,
                                          @Param(value = "params") Map<String, Object> params,
                                          @Param(value = "sort") String sort,
                                          @Param(value = "order") String order);

}
