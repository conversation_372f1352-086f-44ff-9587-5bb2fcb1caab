package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 地名资料表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@Data
public class CjPlaceNameData extends Model<CjPlaceNameData> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 地名
     */
    private String name;

    /**
     * 地图类型
     */
    private Long mapType;

    /**
     * 地图分组大类
     */
    private Long mapGroupBig;

    /**
     * 地图分组小类
     */
    private Long mapGroupSmall;

    /**
     * 古地域类别，1遗址, 2墓葬, 3其他
     */
    private Integer ancientRegionCategory;

    /**
     * 古地域类别为3时填写
     */
    private String regionCategoryContent;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 县/区
     */
    private String district;

    /**
     * 地理位置
     */
    private String geographicLocation;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 年代,多个用逗号分开
     */
    private String age;

    /**
     * 建制沿革
     */
    private String administrativeHistory;

    /**
     * 简介
     */
    private String introduction;

    /**
     * 信息来源
     */
    private String infoSource;

    /**
     * 备注信息
     */
    private String remarks;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 发布状态：0未发布，1已发布
     */
    private Integer publishStatus;

    // 扩展字段，不存储到数据库
    @JsonIgnore
    @TableField(exist = false)
    private String startTime;

    @JsonIgnore
    @TableField(exist = false)
    private String endTime;

    @TableField(exist = false)
    private String publishStatusName;

    @TableField(exist = false)
    private String ancientRegionCategoryName;
}
