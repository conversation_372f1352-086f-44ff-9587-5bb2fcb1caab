package com.das.business.changjiang.controller;


import com.das.annotation.DasController;
import com.das.business.changjiang.entity.CjCwhLiterature;
import com.das.business.changjiang.service.ICjCwhLiteratureService;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 文献资料信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-02
 */
@Api(value = "文献资料信息表", description = "文献资料信息表")
@DasController("/cjCwhLiterature")
public class CjCwhLiteratureController {

    @Resource
    ICjCwhLiteratureService literatureService;

    @ApiOperation(value = "分页查询")
    @RequestMapping(value = "/findByPage", method = RequestMethod.POST)
    public ResultMsg findByPage(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(literatureService.findByPage(pageEntity));
    }

    @ApiOperation(value = "根据ID查询详情")
    @RequestMapping(value = "/getById", method = RequestMethod.POST)
    public ResultMsg getById(@RequestBody CjCwhLiterature cwhLiterature) {
        return new ResultMsg(literatureService.getById(cwhLiterature.getId()));
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "/save")
    public ResultMsg save(@RequestBody CjCwhLiterature cwhLiterature) {
        return new ResultMsg(literatureService.save(cwhLiterature));
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "/updateById")
    public ResultMsg updateById(@RequestBody CjCwhLiterature cwhLiterature) {
        return new ResultMsg(literatureService.updateById(cwhLiterature));
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "/removeByIds")
    public ResultMsg removeByIds(@RequestBody List<Long> ids) {
        return new ResultMsg(literatureService.removeByIds(ids));
    }

    @ApiOperation(value = "导入")
    @PostMapping(value = "/importExcel")
    public ResultMsg importExcel(MultipartFile file) {
        return new ResultMsg(literatureService.importExcel(file));
    }


}
