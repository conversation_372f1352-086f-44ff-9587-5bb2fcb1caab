package com.das.business.changjiang.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.entity.CjDictionaries;
import com.das.business.changjiang.entity.CjDictionariesFile;
import com.das.business.changjiang.entity.CjMark;
import com.das.business.changjiang.mapper.CjDictionariesFileMapper;
import com.das.business.changjiang.mapper.CjDictionariesMapper;
import com.das.business.changjiang.service.ICjDictionariesFileService;
import com.das.business.changjiang.service.ICjDictionariesService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.system.entity.SysUser;
import com.das.utils.common.FileUtil;
import com.das.utils.common.SystemUtil;
import com.das.utils.response.PageEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.ErrorManager;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-26
 */
@Service
public class CjDictionariesServiceImpl extends ServiceImpl<CjDictionariesMapper, CjDictionaries> implements ICjDictionariesService {

    @Resource
    CjDictionariesMapper dictionariesMapper;

    @Resource
    ICjDictionariesFileService dictionariesFileService;

    @Override
    public IPage<CjDictionaries> getCjDictionariesList(PageEntity pageEntity) {
        IPage<CjDictionaries> res = dictionariesMapper.getCjDictionariesList(new Page<>(pageEntity.getCurrent(), pageEntity.getSize()),
                pageEntity.getParams(), pageEntity.getSort(), pageEntity.getOrder());
        res.getRecords().stream().forEach(i -> {
            i.getFileList().stream().forEach(file -> {
                int fileType = file.getFileType();
                switch (fileType) {
                    case 0:
                        if (i.getImgList() == null) {
                            i.setImgList(new ArrayList<>());
                        }
                        i.getImgList().add(file);
                        break;
                    case 1:
                        if (i.getVideoList() == null) {
                            i.setVideoList(new ArrayList<>());
                        }
                        i.getVideoList().add(file);
                        break;
                    case 2:
                        if (i.getAtlasList() == null) {
                            i.setAtlasList(new ArrayList<>());
                        }
                        i.getAtlasList().add(file);
                        break;
                }
            });
        });
        res.getRecords().forEach(c->{
            c.setFileList(null);
        });
        return res;
    }

    @Override
    public List<Map> getCjDictionariesTree(String class1) {
        return dictionariesMapper.getCjDictionariesTree(class1);
    }

    @Override
    @Transactional
    public boolean saveOrUpdateDic(CjDictionaries dictionaries) {
        if (this.saveOrUpdate(dictionaries)) {
            if (dictionaries.getImgList().size() > 0) {
                dictionaries.getImgList().stream().forEach(img->{
                    img.setDicId(dictionaries.getId());
                });
                dictionariesFileService.saveOrUpdateBatch(dictionaries.getImgList());
            }
            if (dictionaries.getVideoList().size() > 0) {
                dictionaries.getVideoList().stream().forEach(img->{
                    img.setDicId(dictionaries.getId());
                });
                dictionariesFileService.saveOrUpdateBatch(dictionaries.getVideoList());
            }
            if (dictionaries.getAtlasList().size() > 0) {
                dictionaries.getAtlasList().stream().forEach(img->{
                    img.setDicId(dictionaries.getId());
                });
                dictionariesFileService.saveOrUpdateBatch(dictionaries.getAtlasList());
            }
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean removeDic(List<Long> ids){
        if(this.removeByIds(ids)){
            if(dictionariesFileService.delFile(ids)){
                ids.stream().forEach(id->{
                    CjDictionariesFile dicFile = dictionariesFileService.getById(id);
                    if(dicFile!=null){
                        FileUtil.deleteFile(dicFile.getLoadPath());
                    }
                });
            }
        }
        return true;
    }

}
