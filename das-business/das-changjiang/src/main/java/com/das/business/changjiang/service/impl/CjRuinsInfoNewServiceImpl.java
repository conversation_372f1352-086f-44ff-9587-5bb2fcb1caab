package com.das.business.changjiang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.das.business.changjiang.controller.vo.CjRuinsInfoDetailVO;
import com.das.business.changjiang.controller.vo.CjRuinsInfoTombsNameVO;
import com.das.business.changjiang.controller.vo.CjRuinsInfoMapVO;
import com.das.business.changjiang.entity.CjRuinsInfo;
import com.das.business.changjiang.entity.CjRuinsInfoNew;
import com.das.business.changjiang.entity.CjRuinsInfoTombs;
import com.das.business.changjiang.entity.CjRunisInfoFile;
import com.das.business.changjiang.mapper.CjRuinsInfoNewMapper;
import com.das.business.changjiang.service.CjRuinsInfoService;
import com.das.business.changjiang.service.ICjRuinsInfoNewService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.business.changjiang.service.ICjRuinsInfoTombsService;
import com.das.business.changjiang.service.ICjRunisInfoFileService;
import com.das.system.entity.SysCode;
import com.das.system.service.ISysCodeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 考古遗址基础信息表-20241225调整业务后新表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Service
@RequiredArgsConstructor
public class CjRuinsInfoNewServiceImpl extends ServiceImpl<CjRuinsInfoNewMapper, CjRuinsInfoNew> implements ICjRuinsInfoNewService {

    private final ISysCodeService sysCodeService;
    private final CjRuinsInfoNewMapper cjRuinsInfoNewMapper;
    private final ICjRunisInfoFileService cjRunisInfoFileService;
    private final ICjRuinsInfoTombsService cjRuinsInfoTombsService;
    private final CjRuinsInfoService cjRuinsInfoService;

    @Override
    public List<CjRuinsInfoMapVO> getRuinsDetail(Map<String, String> params) {
        String category = null;
        if (params.get("ids") != null) {
            category = "(" + params.get("ids").replace("34", "611").replace("35", "612").replace("36", "613") + ")";
        }
        List<String> argsList = null;
        if (params.get("ages") != null) {
            argsList = Arrays.stream(params.get("ages").split(",")).collect(Collectors.toList());
        }
        if (params.get("parentId") != null) {
            for (String parentId : params.get("parentId").split(",")) {
                SysCode sysCode = sysCodeService.getById(parentId);
                argsList.add(sysCode.getDescription());
            }
        }
        return cjRuinsInfoNewMapper.getRuinsDetail(category, argsList);
    }

    @Override
    public CjRuinsInfoDetailVO getRuinsDetailById(Long id) {
        CjRuinsInfo cjRuinsInfoNew = cjRuinsInfoService.getById(id);
        CjRuinsInfoDetailVO res = new CjRuinsInfoDetailVO();
        BeanUtils.copyProperties(cjRuinsInfoNew, res);

        //查询示意图片
        QueryWrapper<CjRunisInfoFile> fileQueryWrapper = new QueryWrapper<>();
        fileQueryWrapper.lambda().eq(CjRunisInfoFile::getType, 1);
        fileQueryWrapper.lambda().eq(CjRunisInfoFile::getRuinsId, res.getId());
        res.setImgList(cjRunisInfoFileService.list(fileQueryWrapper));

        //查询墓地列表
        QueryWrapper<CjRuinsInfoTombs> tombsQueryWrapper = new QueryWrapper<>();
        tombsQueryWrapper.lambda().eq(CjRuinsInfoTombs::getRuinsId, res.getId());
        tombsQueryWrapper.lambda().select(CjRuinsInfoTombs::getId, CjRuinsInfoTombs::getName);
        List<CjRuinsInfoTombs> tombsNameVOList = cjRuinsInfoTombsService.list(tombsQueryWrapper);
        List<CjRuinsInfoTombsNameVO> tombsList = new ArrayList<>();
        tombsNameVOList.forEach(item -> {
            tombsList.add(new CjRuinsInfoTombsNameVO(item.getId(), item.getName()));
        });
        res.setTombsList(tombsList);

        return res;
    }

}
