package com.das.business.changjiang.controller.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 
 * @Description
 * <AUTHOR>
 * @Date 2025-01-02
 */
@Data
public class CjRuinsInfoImportVO {

	@ExcelProperty("名称")
	private String name;

	@ExcelProperty("所属区、县")
	private String address;

	@ExcelProperty("经度")
	private String longitude;

	@ExcelProperty("纬度")
	private String latitude;

	@ExcelProperty("发掘墓葬数量")
	private Integer discoverTombs;

	@ExcelProperty("探明墓葬数量")
	private Integer proveTombs;

	@ExcelProperty("发掘时间")
	private String discoverTime;

	@ExcelProperty("时代")
	private String age;

	@ExcelProperty("发掘经过")
	private String discoverLog;

	@ExcelProperty("墓葬分布概况")
	private String tombsLayout;

	@ExcelProperty("地理、环境描述")
	private String environment;

	@ExcelProperty("周边遗迹描述")
	private String surroundingRuins;

	@ExcelProperty("报告、简报")
	private String report;

	@ExcelProperty("其他信息")
	private String otherInfo;

}
