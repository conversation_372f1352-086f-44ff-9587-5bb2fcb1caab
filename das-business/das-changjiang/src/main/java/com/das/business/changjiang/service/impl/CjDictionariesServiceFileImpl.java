package com.das.business.changjiang.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.business.changjiang.entity.CjDictionariesFile;
import com.das.business.changjiang.mapper.CjDictionariesFileMapper;
import com.das.business.changjiang.service.ICjDictionariesFileService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
@Service
public class CjDictionariesServiceFileImpl extends ServiceImpl<CjDictionariesFileMapper, CjDictionariesFile> implements ICjDictionariesFileService {

    @Resource
    CjDictionariesFileMapper dictionariesFileMapper;

    @Override
    public boolean delFile(List<Long> dicId) {
        dicId.stream().forEach(id->{
            dictionariesFileMapper.delFile(id);
        });
        return true;
    }
}
