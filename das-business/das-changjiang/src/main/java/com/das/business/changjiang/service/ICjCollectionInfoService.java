package com.das.business.changjiang.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.das.business.changjiang.entity.CjCollectionInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.system.entity.SysCode;
import com.das.system.entity.entitywrap.PageEntity;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
public interface ICjCollectionInfoService extends IService<CjCollectionInfo> {
    IPage<SysCode> getByPage(PageEntity pageEntity);

    boolean saveCjCollectionInfo(CjCollectionInfo cjCollectionInfo);

    boolean delCjCollectionInfo(Long id);

    List<Map> getGroupByData();

    CjCollectionInfo getInfoById(Long id);

    List<CjCollectionInfo> getInfoByRuinsId(Long id);
}
