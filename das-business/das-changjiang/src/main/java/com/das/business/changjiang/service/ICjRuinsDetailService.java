package com.das.business.changjiang.service;

import com.das.business.changjiang.entity.CjRuinsDetail;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.business.changjiang.entity.requestEntity.RuinsDetailAge;
import com.das.utils.response.ResultMsg;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 时空演变WebSocket查询接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
public interface ICjRuinsDetailService extends IService<CjRuinsDetail> {
    List<Map> getAgeRuinsDetail(int start, int end);

    ResultMsg getRuinsDetail(long id);
}
