package com.das.business.changjiang.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.business.changjiang.entity.CjHistoryMaps;
import com.das.business.changjiang.mapper.CjHistoryMapsMapper;
import com.das.business.changjiang.service.ICjHistoryMapsService;
import com.das.system.entity.PageEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * <p>
 * 历史地图表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@Service
@Transactional
@Slf4j
public class CjHistoryMapsServiceImpl extends ServiceImpl<CjHistoryMapsMapper, CjHistoryMaps> implements ICjHistoryMapsService {

    @Resource
    private CjHistoryMapsMapper cjHistoryMapsMapper;

    @Override
    public IPage<CjHistoryMaps> getCjHistoryMapsList(PageEntity pageEntity) {
        return cjHistoryMapsMapper.getCjHistoryMapsList(
                new Page<>(pageEntity.getCurrent(), pageEntity.getSize()),
                pageEntity.getParams(),
                pageEntity.getSort(),
                pageEntity.getOrder()
        );
    }

    @Override
    public CjHistoryMaps getCjHistoryMapsById(Long id) {
        return cjHistoryMapsMapper.getCjHistoryMapsById(id);
    }

    @Override
    public boolean saveCjHistoryMaps(CjHistoryMaps cjHistoryMaps) {
        try {
            if (cjHistoryMaps.getId() == null) {
                // 新增
                cjHistoryMaps.setCreateTime(LocalDateTime.now());
                cjHistoryMaps.setUpdateTime(LocalDateTime.now());
                if (cjHistoryMaps.getPublishStatus() == null) {
                    cjHistoryMaps.setPublishStatus(0); // 默认未发布
                }
                return this.save(cjHistoryMaps);
            } else {
                // 更新
                cjHistoryMaps.setUpdateTime(LocalDateTime.now());
                return this.updateById(cjHistoryMaps);
            }
        } catch (Exception e) {
            log.error("保存历史地图失败", e);
            return false;
        }
    }

    @Override
    public boolean deleteCjHistoryMaps(Long id) {
        try {
            return this.removeById(id);
        } catch (Exception e) {
            log.error("删除历史地图失败", e);
            return false;
        }
    }

    @Override
    public boolean batchDeleteCjHistoryMaps(Long[] ids) {
        try {
            return this.removeByIds(Arrays.asList(ids));
        } catch (Exception e) {
            log.error("批量删除历史地图失败", e);
            return false;
        }
    }

    @Override
    public boolean updatePublishStatus(Long id, Integer publishStatus) {
        try {
            CjHistoryMaps historyMaps = new CjHistoryMaps();
            historyMaps.setId(id);
            historyMaps.setPublishStatus(publishStatus);
            historyMaps.setUpdateTime(LocalDateTime.now());
            return this.updateById(historyMaps);
        } catch (Exception e) {
            log.error("更新历史地图发布状态失败", e);
            return false;
        }
    }
}
