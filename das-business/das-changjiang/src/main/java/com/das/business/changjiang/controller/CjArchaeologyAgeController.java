package com.das.business.changjiang.controller;


import com.das.annotation.DasController;
import com.das.business.changjiang.service.ICjArchaeologyAgeService;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@DasController("/cjArchaeologyAge")
@Api(description = "时间年代轴接口")
public class CjArchaeologyAgeController {

    @Resource
    ICjArchaeologyAgeService archaeologyAgeService;

    @ApiOperation(value = "查询历史年代")
    @RequestMapping(value = "/getArchaeologyAge", method = RequestMethod.GET)
    public ResultMsg getRuinsDetail() {
        return new ResultMsg(archaeologyAgeService.list());
    }

    @ApiOperation(value = "查询历史年代")
    @RequestMapping(value = "/getA", method = RequestMethod.GET)
    public ResultMsg getA() {
        return new ResultMsg(archaeologyAgeService.list());
    }

}
