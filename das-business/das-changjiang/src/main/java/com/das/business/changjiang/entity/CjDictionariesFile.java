package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;


/**
 * <p>
 * 辞典附件实体
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-05
 */
@Data
public class CjDictionariesFile extends Model<CjDictionariesFile> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 文件名称
     */
    private String name;

    /**
     * 保存名称
     */
    private String saveName;

    /**
     * 加载地址
     */
    private String loadPath;

    /**
     * 所属辞典ID
     */
    private long dicId;

    /**
     * 附件类型
     */
    private int fileType;

    /**
     * 附件大小
     */
    private double fileSize;


}
