package com.das.business.changjiang.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.entity.CjPlaceNameData;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * 地名资料表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
public interface CjPlaceNameDataMapper extends BaseMapper<CjPlaceNameData> {

    /**
     * 分页查询地名资料列表
     *
     * @param page   分页参数
     * @param params 查询参数
     * @param sort   排序字段
     * @param order  排序方式
     * @return 分页结果
     */
    IPage<CjPlaceNameData> getCjPlaceNameDataList(@Param(value = "page") Page<CjPlaceNameData> page,
                                                  @Param(value = "params") Map<String, Object> params,
                                                  @Param(value = "sort") String sort, 
                                                  @Param(value = "order") String order);

    /**
     * 根据ID查询地名资料详情
     *
     * @param id 地名资料ID
     * @return 地名资料详情
     */
    CjPlaceNameData getCjPlaceNameDataById(@Param(value = "id") Long id);
}
