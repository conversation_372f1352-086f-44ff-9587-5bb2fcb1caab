package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * 遗址发掘资料附件表
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CjRunisInfoFile extends Model<CjRunisInfoFile> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 遗址类型（1遗址2墓地）
     */
    private Integer type;

    /**
     * 遗址表id
     */
    private Long ruinsId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String updateTime;

    public CjRunisInfoFile(Integer type, Long ruinsId, String fileName, String fileType, String filePath){
        this.type = type;
        this.ruinsId = ruinsId;
        this.fileName = fileName;
        this.fileType = fileType;
        this.filePath = filePath;
    }

}
