package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 问题反馈表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@Data
public class CjProblemFeedback extends Model<CjProblemFeedback> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 反馈内容
     */
    private String sendContent;

    /**
     * 回复内容
     */
    private String replyContent;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 问题状态：0未回复，1已回复
     */
    private Integer backStatus;

    // 扩展字段，不存储到数据库
    @JsonIgnore
    @TableField(exist = false)
    private String startTime;

    @JsonIgnore
    @TableField(exist = false)
    private String endTime;

    @TableField(exist = false)
    private String backStatusName;
}
