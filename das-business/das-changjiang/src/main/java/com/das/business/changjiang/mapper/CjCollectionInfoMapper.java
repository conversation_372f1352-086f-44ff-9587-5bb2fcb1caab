package com.das.business.changjiang.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.entity.CjCollectionInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.das.system.entity.SysCode;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
public interface CjCollectionInfoMapper extends BaseMapper<CjCollectionInfo> {


    IPage<SysCode> getByPage(@Param(value = "page") Page<SysCode> page,
                             @Param(value = "params") Map<String, Object> params,
                             @Param(value = "sort") String sort, @Param(value = "order") String order);

    List<Map> getGroupByData();

    CjCollectionInfo getInfoById(@Param(value = "id") Long id);

    List<CjCollectionInfo> getInfoByRuinsId(@Param(value = "id") Long id);

    int batchUpdate(Map<String,Object> map);
}
