package com.das.business.changjiang.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.entity.CjRuinsDigit;
import com.das.business.changjiang.entity.responseEntity.TypeAndCount;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-5-23
 */
public interface CjRuinsDigitMapper extends BaseMapper<CjRuinsDigit> {

    /**
     * 分页查询
     */
    IPage<CjRuinsDigit> getCjRuinsDigitList(@Param(value = "page") Page<CjRuinsDigit> page,
                                            @Param(value = "params") Map<String, Object> params,
                                            @Param(value = "sort") String sort,
                                            @Param(value = "order") String order);

    /**
     * 根据id查询考古遗址成果分类
     * @param ruinsId
     * @return
     */
    List<TypeAndCount> getTypeAndCount(Long ruinsId);

    int batchUpdate(Map<String,Object> map);

    /**
     * 获取成果所有名称
     *
     * @param ruinsId
     * @return
     */
    List<String> getDigitNames(Long ruinsId);

    /**
     * 获取成果的类型名称
     *
     * @param ruinsId
     * @return
     */
    List<String> getDigitTypes(Long ruinsId);

}
