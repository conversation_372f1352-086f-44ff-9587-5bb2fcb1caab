package com.das.business.changjiang.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.das.business.changjiang.entity.CjRuins;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.utils.response.PageEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
public interface ICjRuinsService extends IService<CjRuins> {

    List<CjRuins> findRuins(CjRuins cjRuins);

    boolean saveRuins(CjRuins cjRuins, MultipartFile file);

    boolean delRuins(Long id);

    IPage<CjRuins> getRuinsList(PageEntity pageEntity);

    CjRuins getRuinsById(Long id);

    Object getRuinsTotal(CjRuins cjRuins);

}
