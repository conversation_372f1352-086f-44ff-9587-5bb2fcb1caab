package com.das.business.changjiang.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.entity.CjRuinsInfoTombs;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 墓地基本信息表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
public interface CjRuinsInfoTombsMapper extends BaseMapper<CjRuinsInfoTombs> {

    IPage<CjRuinsInfoTombs> findByPage(@Param(value = "page") Page<CjRuinsInfoTombs> page,
                                       @Param(value = "params") Map<String, Object> params,
                                       @Param(value = "sort") String sort,
                                       @Param(value = "order") String order);

}
