package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * <p>
 * 椁室大小
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Data
public class CjRuinsInfoTombsCoffin extends Model<CjRuinsInfoTombsCoffin> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 长
     */
    private String length;

    /**
     * 宽
     */
    private String wight;

    /**
     * 高
     */
    private String height;

    /**
     * 所属墓葬ID
     */
    private Long tombsId;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String updateTime;

}
