package com.das.business.changjiang.controller;


import com.das.annotation.RequestSecurity;
import com.das.business.changjiang.entity.CjRuinsInfoOld;
import com.das.business.changjiang.service.CjRuinsInfoOldService;
import com.das.business.changjiang.service.ICjRuinsInfoNewService;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 考古遗址模块
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
//@RestController
//@RequestMapping("/cjRuinsInfoOld")
public class CjRuinsInfoOldController {

//    @Resource
    private CjRuinsInfoOldService cjRuinsInfoService;
//    @Resource
    private ICjRuinsInfoNewService ruinsInfoNewService;

    /**
     * 分页查询
     */
    @RequestSecurity(value = "/getCjRuinsInfoList", method = RequestMethod.POST)
    public ResultMsg getCjRuinsInfoList(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(cjRuinsInfoService.getCjRuinsInfoList(pageEntity));
    }


    /**
     * 考古遗址导出
     */
    @RequestSecurity(value = "/ExportCjRuinsInfoList", method = RequestMethod.GET)
    public void ExportCjRuinsInfoList(String level, String category, String age, String name, String[] ids, HttpServletResponse response) {
        cjRuinsInfoService.ExportCjRuinsInfoList(level, category, age, name, ids, response);
    }

    /**
     * 新增or修改考古遗址
     */
    @RequestSecurity(value = "/saveCjRuinsInfo", method = RequestMethod.POST)
    public ResultMsg saveCjRuinsInfo(@RequestBody CjRuinsInfoOld cjRuinsInfo) {
        return new ResultMsg(cjRuinsInfoService.saveCjRuinsInfo(cjRuinsInfo));
    }

    /**
     * 删除考古遗址信息
     */
    @RequestSecurity(value = "/delCjRuinsInfo", method = RequestMethod.POST)
    public ResultMsg delCjRuinsInfo(@RequestBody List<Long> ids) {
        return new ResultMsg(cjRuinsInfoService.delCjRuinsInfo(ids));
    }

    /**
     * 根据id查询考古遗址信息
     */
    @ApiOperation(value = "根据id查询考古遗址信息")
    @RequestSecurity(value = "/getCjRuinsInfoByIdOld", method = RequestMethod.POST)
    public ResultMsg getCjRuinsInfoByIdOld(@RequestParam Long id) {
        return new ResultMsg(cjRuinsInfoService.getCjRuinsInfoById(id));
    }

    /**
     * 获取考古遗址列表
     */
    @RequestSecurity(value = "/getCjRuinsInfoAll", method = RequestMethod.POST)
    public ResultMsg getCjRuinsInfoAll(@RequestBody Map<String, Object> map) {
        if (map.get("keyword") != null) {
            return new ResultMsg(cjRuinsInfoService.getCjRuinsInfo(map.get("keyword").toString()));
        }
        if(map.get("type") != null) {
            return new ResultMsg(cjRuinsInfoService.getCjRuinsInfoAll(Long.parseLong(map.get("type").toString())));
        }
        return new ResultMsg(new ArrayList<CjRuinsInfoOld>(0));
    }

    /**
     * 根据树查询所有的集合数据
     */
    @RequestMapping(value = "/getRuinsDetailOld", method = RequestMethod.POST)
    public ResultMsg getRuinsDetailOld(@RequestBody Map<String, String> params) {
        return new ResultMsg(cjRuinsInfoService.getRuinsDetail(params));
    }


    /**
     * 根据树查询所有的集合数据-调用新版本数据结构接口
     */
    @RequestMapping(value = "/getRuinsDetail", method = RequestMethod.POST)
    public ResultMsg getRuinsDetailNew(@RequestBody Map<String, String> params) {
        return new ResultMsg(ruinsInfoNewService.getRuinsDetail(params));
    }

    /**
     * 根据id查询考古遗址信息
     */
    @ApiOperation(value = "根据id查询考古遗址信息")
    @RequestSecurity(value = "/getCjRuinsInfoById", method = RequestMethod.POST)
    public ResultMsg getCjRuinsInfoById(@RequestParam Long id) {
        return new ResultMsg(ruinsInfoNewService.getRuinsDetailById(id));
    }


}
