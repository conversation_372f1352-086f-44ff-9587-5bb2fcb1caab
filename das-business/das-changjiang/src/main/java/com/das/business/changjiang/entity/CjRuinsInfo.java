package com.das.business.changjiang.entity;

import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;

import com.das.business.changjiang.controller.vo.CjRuinsInfoFileVO;
import lombok.Data;
import org.apache.ibatis.annotations.Delete;

import java.util.Date;
import java.util.List;

/**
 * 
 * @Description
 * <AUTHOR>
 * @Date 2025-01-02
 */
@Data
@TableName(value ="cj_ruins_info")
public class CjRuinsInfo implements Serializable {
	private static final long serialVersionUID =  1609374105162980944L;

	/**
	 * 主键id
	 */
	@TableId(type = IdType.AUTO)
 	@TableField(value = "id")
	private Long id;

	/**
	 * 所属父类
	 */
 	@TableField(value = "parent_id")
	private Long parentId;

	/**
	 * 文物名称
	 */
 	@TableField(value = "name")
	private String name;

	/**
	 * 类别
	 */
 	@TableField(value = "category")
	private Long category;

	/**
	 * 市
	 */
 	@TableField(value = "city")
	private String city;

	/**
	 * 县
	 */
 	@TableField(value = "county")
	private String county;

	/**
	 * 地址
	 */
 	@TableField(value = "address")
	private String address;

	/**
	 * 经度
	 */
 	@TableField(value = "longitude")
	private String longitude;

	/**
	 * 纬度
	 */
 	@TableField(value = "latitude")
	private String latitude;

	/**
	 * 海拔高度
	 */
 	@TableField(value = "altitude")
	private String altitude;

	/**
	 * 发掘墓葬数量（座）
	 */
 	@TableField(value = "discover_tombs")
	private Integer discoverTombs;

	/**
	 * 探明墓葬数量（座）
	 */
 	@TableField(value = "prove_tombs")
	private Integer proveTombs;

	/**
	 * 发掘时间
	 */
 	@TableField(value = "discover_time")
	private String discoverTime;

	/**
	 * 时代
	 */
 	@TableField(value = "age")
	private String age;

	/**
	 * 发掘经过
	 */
 	@TableField(value = "discover_log")
	private String discoverLog;

	/**
	 * 墓葬分布情况
	 */
 	@TableField(value = "tombs_layout")
	private String tombsLayout;

	/**
	 * 地理、环境描述
	 */
 	@TableField(value = "environment")
	private String environment;

	/**
	 * 周边遗迹描述
	 */
 	@TableField(value = "surrounding_ruins")
	private String surroundingRuins;

	/**
	 * 报告、简报
	 */
 	@TableField(value = "report")
	private String report;

	/**
	 * 其他信息
	 */
 	@TableField(value = "other_info")
	private String otherInfo;

	/**
	 * 墓地平面布局图(存储压缩的小图、加载大图默认名称去掉_min后加载)
	 */
 	@TableField(value = "layout_plan_url")
	private String layoutPlanUrl;

	/**
	 * 创建时间
	 */
 	@TableField(value = "create_time")
	private String createTime;

	/**
	 * 修改时间
	 */
 	@TableField(value = "update_time")
	private String updateTime;

 	@TableLogic
	private String delFlag;

 	@TableField(exist = false)
 	private JSONArray imgList;

}
