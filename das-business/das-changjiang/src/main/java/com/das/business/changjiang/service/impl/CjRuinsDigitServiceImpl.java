package com.das.business.changjiang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.business.changjiang.entity.CjFile;
import com.das.business.changjiang.entity.CjRuinsDigit;
import com.das.business.changjiang.entity.responseEntity.TypeAndCount;
import com.das.business.changjiang.mapper.CjRuinsDigitMapper;
import com.das.business.changjiang.service.ICjRuinsDigitService;
import com.das.es.dao.EsEntityRepository;
import com.das.es.entity.EsEntity;
import com.das.utils.common.FileUtil;
import com.das.utils.common.ImgUtil;
import com.das.utils.common.StringHandle;
import com.das.utils.response.PageEntity;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-5-23
 */
@Service
@Transactional
@Slf4j
public class CjRuinsDigitServiceImpl extends ServiceImpl<CjRuinsDigitMapper, CjRuinsDigit> implements ICjRuinsDigitService {

    @Resource
    private CjRuinsDigitMapper cjRuinsDigitMapper;

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Resource
    private EsEntityRepository esEntityRepository;

    @Override
    public IPage<CjRuinsDigit> getCjRuinsDigitList(PageEntity pageEntity) {
        return cjRuinsDigitMapper.getCjRuinsDigitList(
                new Page<CjRuinsDigit>(pageEntity.getCurrent(), pageEntity.getSize()),
                pageEntity.getParams(),
                pageEntity.getSort(),
                pageEntity.getOrder());
    }

    @Override
    public boolean saveCjRuinsDigit(CjRuinsDigit cjRuinsDigit) {
        // 是否需要获取缩略图
        boolean flag = false;
        if (cjRuinsDigit.getId() == null) {
            flag = true;
        } else {
            CjRuinsDigit ruinsDigit = this.getById(cjRuinsDigit.getId());
            if (!cjRuinsDigit.getCover().equals(ruinsDigit.getCover()) || ruinsDigit.getThumbnail() == null) {
                flag = true;
            }
        }
        if (flag) {
            String cover = cjRuinsDigit.getCover();
            cjRuinsDigit.setThumbnail(ImgUtil.getThumbnail(cover));
        }

        // 是否需要重新解压
        if (cjRuinsDigit.getCjFiles().size() > 0 && cjRuinsDigit.getCjFiles().get(0).getFileSize() != null) {
            CjFile cjFile = cjRuinsDigit.getCjFiles().get(0);
            if (cjFile.getLoadPath().endsWith(".zip") || cjFile.getLoadPath().endsWith(".ZIP")) {
                cjRuinsDigit.setStatus(0);
                cjRuinsDigit.setLoadPath(cjFile.getLoadPath());
                cjRuinsDigit.setModel("");
            }
        }

        boolean b = this.saveOrUpdate(cjRuinsDigit);
        if (b) {
            // 添加到es中
            String digitNames =
                    String.join(",", cjRuinsDigitMapper.getDigitNames(cjRuinsDigit.getRuinsId()));
            String digitTypes =
                    String.join(",", cjRuinsDigitMapper.getDigitTypes(cjRuinsDigit.getRuinsId()));
            EsEntity esEntity = new EsEntity();
            esEntity.setTid(cjRuinsDigit.getRuinsId())
                    .setDigitNames(digitNames)
                    .setDigitTypes(digitTypes)
                    .setResourceId(cjRuinsDigit.getRuinsId());
            //this.saveEs(esEntity);
        }
        return b;
    }

    @Override
    public boolean delCjRuinsDigit(List<Long> ids) {
        Long ruinsId = this.getById(ids.get(0)).getRuinsId();
        List<CjRuinsDigit> cjRuinsDigits = this.list(new QueryWrapper<CjRuinsDigit>().in("id", ids));
        boolean flag = this.removeByIds(ids);
        if (flag) {
            cjRuinsDigits.forEach(cjRuinsDigit -> {
                String cover = cjRuinsDigit.getCover();
                if (StringHandle.isNotEmpty(cover)) FileUtil.deleteFile(cover);
                String loadPath = cjRuinsDigit.getLoadPath();
                if (StringHandle.isNotEmpty(loadPath)) FileUtil.deleteFile(loadPath);
            });
            // 修改es中数据
            ids.forEach(id -> {
                String digitNames = String.join(",", cjRuinsDigitMapper.getDigitNames(ruinsId));
                String digitTypes = String.join(",", cjRuinsDigitMapper.getDigitTypes(ruinsId));
                EsEntity esEntity = new EsEntity();
                esEntity.setTid(ruinsId)
                        .setDigitNames(digitNames)
                        .setDigitTypes(digitTypes)
                        .setResourceId(ruinsId);
                //this.saveEs(esEntity);
            });
        }
        return flag;
    }

    @Override
    public CjRuinsDigit getCjRuinsDigitById(Long id) {
        CjRuinsDigit cjRuinsDigit = this.getById(id);
        String loadPath = cjRuinsDigit.getLoadPath();
        if (StringHandle.isNotEmpty(loadPath)) {
            CjFile cjFile = new CjFile();
            cjFile.setName(loadPath.substring(loadPath.lastIndexOf("/") + 1));
            ArrayList<CjFile> cjFiles = new ArrayList<>();
            cjFiles.add(cjFile);
            cjRuinsDigit.setCjFiles(cjFiles);
        }
        return cjRuinsDigit;
    }

    @Override
    public List<TypeAndCount> getTypeAndCount(Long ruinsId) {
        return cjRuinsDigitMapper.getTypeAndCount(ruinsId);
    }

    /**
     * 插入es
     *
     * @param entity
     */
    public void saveEs(EsEntity entity) {
        //查询es中是否存在
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilders.boolQuery()
                        .must(QueryBuilders.matchQuery("tid", entity.getTid()))
                )
                .build();
        SearchHits<EsEntity> searchHits = elasticsearchRestTemplate.search(query, EsEntity.class);
        if (searchHits.getTotalHits() > 0) {
            for (SearchHit<EsEntity> searchHit : searchHits) {
                EsEntity esEntity = searchHit.getContent();
                Document document = Document.create();
                document.put("digitNames", entity.getDigitNames());
                document.put("digitTypes", entity.getDigitTypes());
                elasticsearchRestTemplate.update(UpdateQuery.builder(esEntity.getId())
                        .withDocument(document).build(), IndexCoordinates.of("cj_ruins_info"));
            }
        } else {
            EsEntity es = new EsEntity();
            es.setTid(entity.getTid())
                    .setDigitNames(entity.getDigitNames())
                    .setDigitTypes(entity.getDigitTypes())
                    .setResourceId(entity.getResourceId());
            log.info("插入es成功");
            esEntityRepository.save(es);
        }
    }

}
