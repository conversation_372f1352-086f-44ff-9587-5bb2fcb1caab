package com.das.business.changjiang.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.das.business.changjiang.entity.CjCollectionInfo;
import com.das.business.changjiang.entity.CjRuinsDigit;
import com.das.business.changjiang.mapper.CjCollectionInfoMapper;
import com.das.business.changjiang.mapper.CjRuinsDigitMapper;
import com.das.utils.common.ZipTo3dModelUtil;
import com.das.yml.FtpConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 功能描述: 定时解压模型
 *
 * <AUTHOR>
 * @time
 * @see
 **/
@Component
@Slf4j
@EnableScheduling
public class ZipToGet3dSchedule {

    @Resource
    private CjRuinsDigitMapper cjRuinsDigitMapper;

    @Resource
    private CjCollectionInfoMapper cjCollectionInfoMapper;

    @Autowired
    private FtpConfig ftpConfig;

    /**
     * 每2分钟检查项目成果中是否有未解压的模型，如果有，逐个解压转换
     */
    @Scheduled(cron = "0 0/2 * * * ?")
    public void unzipModel() {
        log.debug("启动解压定时任务！！！");

        QueryWrapper<CjRuinsDigit> wrapper = Wrappers.query();
        //类型是模型
        wrapper.eq("type", 604);
        //未转化状态
        wrapper.eq("status", 0);
        List<CjRuinsDigit> cjRuinsDigitList = cjRuinsDigitMapper.selectList(wrapper);

        if (cjRuinsDigitList != null && cjRuinsDigitList.size() != 0) {
            //批量更新未转换状态为转换中
            List<String> ids = new ArrayList<>();
            for (CjRuinsDigit cjRuinsDigit : cjRuinsDigitList) {
                ids.add(cjRuinsDigit.getId().toString());
            }
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("ids", "(" + String.join(",", ids) + ")");

            cjRuinsDigitMapper.batchUpdate(paramMap);

            for (CjRuinsDigit cjRuinsDigit : cjRuinsDigitList) {
                String loadPath = cjRuinsDigit.getLoadPath();
                if (StringUtils.isNotBlank(loadPath)) {
                    loadPath.replace(ftpConfig.getFtpUrl(), ftpConfig.getFtpPath());
                }
                //获取返回的路径
                String path = ZipTo3dModelUtil.zipToModel(loadPath);
                CjRuinsDigit digitResult = new CjRuinsDigit();
                digitResult.setId(cjRuinsDigit.getId());
                if (StringUtils.isNotBlank(path)) {
                    digitResult.setStatus(2);
                } else {
                    digitResult.setStatus(3);
                }
                digitResult.setModel(path.replace(ftpConfig.getFtpPath(), ftpConfig.getFtpUrl()));
                cjRuinsDigitMapper.updateById(digitResult);

            }
        }
    }

    @Scheduled(cron = "0 0/2 * * * ?")
    public void unzipCollectionModel() {
        log.debug("启动解压定时任务！！！");

        QueryWrapper<CjCollectionInfo> wrapper = Wrappers.query();
        //未转化状态
        wrapper.eq("status", 0);
        List<CjCollectionInfo> cjCollectionInfoList = cjCollectionInfoMapper.selectList(wrapper);

        if (cjCollectionInfoList != null && cjCollectionInfoList.size() != 0) {
            //批量更新未转换状态为转换中
            List<String> ids = new ArrayList<>();
            for (CjCollectionInfo cjCollectionInfo : cjCollectionInfoList) {
                ids.add(cjCollectionInfo.getId().toString());
            }
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("ids", "(" + String.join(",", ids) + ")");

            cjCollectionInfoMapper.batchUpdate(paramMap);

            for (CjCollectionInfo cjCollectionInfo : cjCollectionInfoList) {
                String loadPath = cjCollectionInfo.getThreeUrl();
                if (StringUtils.isNotBlank(loadPath)) {
                    loadPath.replace(ftpConfig.getFtpUrl(), ftpConfig.getFtpPath());
                }
                //获取返回的路径
                String path = ZipTo3dModelUtil.zipToCollectionModel(loadPath);
                CjCollectionInfo collectionResult = new CjCollectionInfo();
                collectionResult.setId(cjCollectionInfo.getId());
                if (StringUtils.isNotBlank(path)) {
                    collectionResult.setStatus(2);
                } else {
                    collectionResult.setStatus(3);
                }
                collectionResult.setModel(path.replace(ftpConfig.getFtpPath(), ftpConfig.getFtpUrl()));
                cjCollectionInfoMapper.updateById(collectionResult);
            }
        }
    }

}
