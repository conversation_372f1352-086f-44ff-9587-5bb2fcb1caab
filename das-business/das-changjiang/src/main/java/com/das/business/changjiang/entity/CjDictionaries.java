package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 辞典实体
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-26
 */
@Data
public class CjDictionaries extends Model<CjDictionaries> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 分类1
     */
    private String class1;

    /**
     * 分类2
     */
    private String class2;

    /**
     * 分类3
     */
    private String class3;

    /**
     * 注释
     */
    private String annotation;

    /**
     * 相关链接
     */
    private String annotation_link;

    /**
     * 图片
     */
    @TableField(exist = false)
    private List<CjDictionariesFile> imgList;

    /**
     * 视频
     */
    @TableField(exist = false)
    private List<CjDictionariesFile> videoList;

    /**
     * 图谱
     */
    @TableField(exist = false)
    private List<CjDictionariesFile> atlasList;

    /**
     * 附件列表
     */
    @TableField(exist = false)
    private List<CjDictionariesFile> fileList;


}
