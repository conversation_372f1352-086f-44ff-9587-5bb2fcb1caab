package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * <p>
 * 墓地基本信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Data
public class CjRuinsInfoTombs extends Model<CjRuinsInfoTombs> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属遗址
     */
    private Long ruinsId;

    /**
     * 墓葬名称
     */
    private String name;

    /**
     * 年代
     */
    private String dynasty;

    /**
     * 盗扰情况
     */
    private String stealInfo;

    /**
     * 封土-有/无
     */
    private String fiefHave;

    /**
     * 封土-形制
     */
    private String fiefShape;

    /**
     * 封土-底径（米）
     */
    private String fiefBottomDiameter;

    /**
     * 封土-高度（米）
     */
    private String fiefHeight;

    /**
     * 墓坑-形制
     */
    private String holeShape;

    /**
     * 墓坑-方向
     */
    private String holeDirection;

    /**
     * 墓坑-墓口长（米）
     */
    private String holeTopLong;

    /**
     * 墓坑-墓口宽（米）
     */
    private String holeTopWidth;

    /**
     * 墓坑-墓底长（米）
     */
    private String holeBottomLength;

    /**
     * 墓坑-墓底宽（米）
     */
    private String holeBottomWidth;

    /**
     * 墓坑-墓深（米）
     */
    private String holeBottomHeight;

    /**
     * 墓坑-台阶数量
     */
    private String holeStep;

    /**
     * 墓坑-墓道有/无
     */
    private String holePassageHave;

    /**
     * 墓坑-墓道数量
     */
    private String holePassageNumber;

    /**
     * 墓坑-坡长（米）
     */
    private String holePassageTotalLength;

    /**
     * 墓坑-口长（米）
     */
    private String holePassageTopLength;

    /**
     * 墓坑-口长端（米）
     */
    private String holePassageTopLengthLength;

    /**
     * 墓坑-口短端（米）
     */
    private String holePassageTopShortLength;

    /**
     * 墓坑-底端（米）
     */
    private String holePassageBottomLength;

    /**
     * 墓坑-坡度
     */
    private String holePassageAngle;

    /**
     * 填土描述
     */
    private String fillSoil;

    /**
     * 其他
     */
    private String other;

    /**
     * 棺椁重数
     */
    private String coffinNumber;

    /**
     * 分室数量
     */
    private String divideNumber;

    /**
     * 葬式
     */
    private String way;

    /**
     * 墓主性别
     */
    private String sex;

    /**
     * 墓主年龄
     */
    private String age;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String updateTime;


}
