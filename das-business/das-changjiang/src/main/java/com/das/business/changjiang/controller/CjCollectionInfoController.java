package com.das.business.changjiang.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.das.annotation.DasController;
import com.das.annotation.RequestSecurity;
import com.das.business.changjiang.entity.CjCollectionInfo;
import com.das.business.changjiang.service.ICjCollectionInfoService;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.service.ISysCodeService;
import com.das.utils.common.CangPConstant;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;


import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@DasController("/cjCollectionInfo")
@Api(description = "文物信息接口")
public class CjCollectionInfoController {

    @Resource
    ICjCollectionInfoService cjCollectionInfoService;

    @Resource
    ISysCodeService sysCodeService;


    @ApiOperation(value = "文物查询初始化")
    @RequestMapping(value = "/init", method = RequestMethod.GET)
    public ResultMsg init() {
        JSONObject jsonObject = new JSONObject();
        //文物类别
        QueryWrapper lb = new QueryWrapper();
        lb.select("id", "description").eq("field", CangPConstant.TYPE_FIELD);
        lb.eq("is_last_child", CangPConstant.IS_LAST_CHILD_1);
        List<Map> typeList = sysCodeService.listMaps(lb);
        List<Map> groupData = cjCollectionInfoService.getGroupByData();
        typeList.forEach(item->{
            String desc = item.get("description").toString();
            desc = desc+"("+getTypeCount(Long.parseLong(item.get("id").toString()), groupData)+")";
            item.put("description", desc);
        });
        jsonObject.put("TYPE_FIELD", typeList);//文物类别

        //文物级别
        QueryWrapper jb = new QueryWrapper();
        jb.select("id", "description").eq("field", CangPConstant.LEVEL_FIELD);
        jb.eq("is_last_child", CangPConstant.IS_LAST_CHILD_1);
        jsonObject.put("LEVEL_FIELD", sysCodeService.listMaps(jb));

        //质地
        QueryWrapper zd = new QueryWrapper();
        zd.select("id", "description").in("field", CangPConstant.TEXTURE_ORGANIC_FIELD, CangPConstant.TEXTURE_INORGANIC_FIELD);
        zd.eq("enabled",1);
        zd.eq("is_last_child",1);
        zd.orderByAsc("sort_no");
        jsonObject.put("TEXTURE_ORGANIC_FIELD", sysCodeService.listMaps(zd));

        //年代
        QueryWrapper year = new QueryWrapper();
        year.select("id", "description").in("parent_id", CangPConstant.MCR_AGE_LISHI_OPTION);
        year.eq("enabled",1);
        jsonObject.put("YEAR_FIELD", sysCodeService.listMaps(year));
        return new ResultMsg(jsonObject);
    }


    @ApiOperation(value = "查询所有文物")
    @RequestMapping(value = "/getCollectionInfo", method = RequestMethod.POST)
    public ResultMsg getRuinsDetail(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(cjCollectionInfoService.getByPage(pageEntity));
    }

    @ApiOperation(value = "查询所有文物")
    @RequestMapping(value = "/getById/{id}", method = RequestMethod.GET)
    public ResultMsg getById(@PathVariable("id") long id) {
        CjCollectionInfo cjCollectionInfo = cjCollectionInfoService.getInfoById(id);
        return new ResultMsg(cjCollectionInfo);
    }

    @ApiOperation(value = "新增或修改文物信息")
    @RequestSecurity(value = "/saveCjCollectionInfo",method = RequestMethod.POST)
    public ResultMsg saveCjCollectionInfo(@RequestBody CjCollectionInfo cjCollectionInfo){
        return new ResultMsg(cjCollectionInfoService.saveCjCollectionInfo(cjCollectionInfo));
    }

    @ApiOperation(value = "删除文物信息")
    @RequestSecurity(value = "/delCjCollectionInfo",method = RequestMethod.POST)
    public ResultMsg delCjCollectionInfo(@RequestParam Long id){
        return new ResultMsg(cjCollectionInfoService.delCjCollectionInfo(id));
    }


    public int getTypeCount(long typeId, List<Map> groupData){
        for(Map item:groupData){
            if(Long.parseLong(item.get("type").toString())==typeId){
                return Integer.parseInt(item.get("count").toString());
            }
        }
        return 0;
    }
}
