package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 出土文物信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-02
 */
@Data
public class CjCwhUnearthedRelic extends Model<CjCwhUnearthedRelic> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 图片
     */
    private String cover;

    /**
     * 器类
     */
    private String classify;

    /**
     * 器类名称
     */
    @TableField(exist = false)
    private String classifyName;

    /**
     * 材质
     */
    private String materialQuality;

    /**
     * 材质名称
     */
    @TableField(exist = false)
    private String materialQualityName;

    /**
     * 出土地点
     */
    private String beUnearthed;

    /**
     * 出土单位
     */
    private String unearthedUnit;

    /**
     * 主体纹饰
     */
    private String mainDecoration;

    /**
     * 年代
     */
    private String age;

    /**
     *
     * 年代名称
     */
    @TableField(exist = false)
    private String ageName;

    /**
     * 收藏单位
     */
    private String collectionUnit;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

}
