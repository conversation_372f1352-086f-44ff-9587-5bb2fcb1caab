package com.das.business.changjiang.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.business.changjiang.entity.CjDigitalMaps;
import com.das.business.changjiang.mapper.CjDigitalMapsMapper;
import com.das.business.changjiang.service.ICjDigitalMapsService;
import com.das.system.entity.PageEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * <p>
 * 数字地图表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@Service
@Transactional
@Slf4j
public class CjDigitalMapsServiceImpl extends ServiceImpl<CjDigitalMapsMapper, CjDigitalMaps> implements ICjDigitalMapsService {

    @Resource
    private CjDigitalMapsMapper cjDigitalMapsMapper;

    @Override
    public IPage<CjDigitalMaps> getCjDigitalMapsList(PageEntity pageEntity) {
        return cjDigitalMapsMapper.getCjDigitalMapsList(
                new Page<>(pageEntity.getCurrent(), pageEntity.getSize()),
                pageEntity.getParams(),
                pageEntity.getSort(),
                pageEntity.getOrder()
        );
    }

    @Override
    public CjDigitalMaps getCjDigitalMapsById(Long id) {
        return cjDigitalMapsMapper.getCjDigitalMapsById(id);
    }

    @Override
    public boolean saveCjDigitalMaps(CjDigitalMaps cjDigitalMaps) {
        try {
            if (cjDigitalMaps.getId() == null) {
                // 新增
                cjDigitalMaps.setCreateTime(LocalDateTime.now());
                cjDigitalMaps.setUpdateTime(LocalDateTime.now());
                if (cjDigitalMaps.getPublishStatus() == null) {
                    cjDigitalMaps.setPublishStatus(0); // 默认未发布
                }
                return this.save(cjDigitalMaps);
            } else {
                // 更新
                cjDigitalMaps.setUpdateTime(LocalDateTime.now());
                return this.updateById(cjDigitalMaps);
            }
        } catch (Exception e) {
            log.error("保存数字地图失败", e);
            return false;
        }
    }

    @Override
    public boolean deleteCjDigitalMaps(Long id) {
        try {
            return this.removeById(id);
        } catch (Exception e) {
            log.error("删除数字地图失败", e);
            return false;
        }
    }

    @Override
    public boolean batchDeleteCjDigitalMaps(Long[] ids) {
        try {
            return this.removeByIds(Arrays.asList(ids));
        } catch (Exception e) {
            log.error("批量删除数字地图失败", e);
            return false;
        }
    }

    @Override
    public boolean updatePublishStatus(Long id, Integer publishStatus) {
        try {
            CjDigitalMaps digitalMaps = new CjDigitalMaps();
            digitalMaps.setId(id);
            digitalMaps.setPublishStatus(publishStatus);
            digitalMaps.setUpdateTime(LocalDateTime.now());
            return this.updateById(digitalMaps);
        } catch (Exception e) {
            log.error("更新数字地图发布状态失败", e);
            return false;
        }
    }
}
