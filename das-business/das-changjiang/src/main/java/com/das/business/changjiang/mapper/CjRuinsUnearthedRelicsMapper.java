package com.das.business.changjiang.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.entity.CjRuinsUnearthedRelics;
import com.das.business.changjiang.entity.responseEntity.TypeAndCount;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-5-23
 */
public interface CjRuinsUnearthedRelicsMapper extends BaseMapper<CjRuinsUnearthedRelics> {

    /**
     * 分页查询
     */
    IPage<CjRuinsUnearthedRelics> getCjRuinsRelicsList(@Param(value = "page") Page<CjRuinsUnearthedRelics> page,
                                                       @Param(value = "params") Map<String, Object> params,
                                                       @Param(value = "sort") String sort,
                                                       @Param(value = "order") String order);


    /**
     * 根据id查询考古遗址出土文物分类
     *
     * @param ruinsId
     * @return
     */
    List<TypeAndCount> getTypeAndCount(Long ruinsId);


    /**
     * 获取遗址所有文物名称
     *
     * @param ruinsId
     * @return
     */
    List<String> getRelicNames(Long ruinsId);

}
