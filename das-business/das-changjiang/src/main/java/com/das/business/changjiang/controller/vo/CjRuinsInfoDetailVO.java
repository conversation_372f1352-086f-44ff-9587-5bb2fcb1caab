package com.das.business.changjiang.controller.vo;

import com.das.business.changjiang.entity.CjRuinsInfoNew;
import com.das.business.changjiang.entity.CjRunisInfoFile;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 考古遗址基础信息表
 *
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CjRuinsInfoDetailVO extends CjRuinsInfoNew {

    /**
     * 示意图片
     */
    private List<CjRunisInfoFile> imgList;

    /**
     * 墓地列表
     */
    private List<CjRuinsInfoTombsNameVO> tombsList;

}