package com.das.business.changjiang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.business.changjiang.entity.CjFile;
import com.das.business.changjiang.entity.CjRuinsUnearthedMaterial;
import com.das.business.changjiang.entity.CjUnearthedMaterialFile;
import com.das.business.changjiang.entity.responseEntity.TypeAndCount;
import com.das.business.changjiang.mapper.CjRuinsUnearthedMaterialMapper;
import com.das.business.changjiang.mapper.CjUnearthedMaterialFileMapper;
import com.das.business.changjiang.service.ICjRuinsUnearthedMaterialService;
import com.das.business.changjiang.service.ICjUnearthedMaterialFileService;
import com.das.es.dao.EsEntityRepository;
import com.das.es.entity.CollectionInfo;
import com.das.es.entity.EsEntity;
import com.das.utils.common.ImgUtil;
import com.das.utils.response.PageEntity;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-5-23
 */
@Service
@Transactional
@Slf4j
public class CjRuinsUnearthedMaterialServiceImpl extends ServiceImpl<CjRuinsUnearthedMaterialMapper, CjRuinsUnearthedMaterial> implements ICjRuinsUnearthedMaterialService {

    @Resource
    private CjRuinsUnearthedMaterialMapper cjRuinsMaterialMapper;

    @Resource
    private ICjUnearthedMaterialFileService materialFileService;

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Resource
    private EsEntityRepository esEntityRepository;

    @Resource
    private CjUnearthedMaterialFileMapper materialFileMapper;

    @Override
    public IPage<CjRuinsUnearthedMaterial> getCjRuinsMaterialList(PageEntity pageEntity) {
        return cjRuinsMaterialMapper.getCjRuinsMaterialList(
                new Page<CjRuinsUnearthedMaterial>(pageEntity.getCurrent(), pageEntity.getSize()),
                pageEntity.getParams(),
                pageEntity.getSort(),
                pageEntity.getOrder());
    }

    @Override
    public boolean saveCjRuinsMaterial(CjRuinsUnearthedMaterial cjRuinsMaterial) {
        // 是否需要获取缩略图
        boolean whether = false;
        if (cjRuinsMaterial.getId() == null) {
            whether = true;
        } else {
            CjRuinsUnearthedMaterial unearthedMaterial = this.getById(cjRuinsMaterial.getId());
            if (!cjRuinsMaterial.getCover().equals(unearthedMaterial.getCover()) || cjRuinsMaterial.getThumbnail() == null) {
                whether = true;
            }
        }
        if (whether) {
            String cover = cjRuinsMaterial.getCover();
            cjRuinsMaterial.setThumbnail(ImgUtil.getThumbnail(cover));
        }

        boolean flag = this.saveOrUpdate(cjRuinsMaterial);
        if (flag) {
            // 更新附件
            val req = new QueryWrapper<CjUnearthedMaterialFile>()
                    .eq("material_id", cjRuinsMaterial.getId());
            materialFileService.remove(req);

            for (CjFile cjFile : cjRuinsMaterial.getCjFiles()) {
                CjUnearthedMaterialFile materialFile = new CjUnearthedMaterialFile();
                materialFile.setMaterialId(cjRuinsMaterial.getId());
                materialFile.setFileName(cjFile.getName());
                materialFile.setFileType(cjFile.getSuffix());
                materialFile.setFilePath(cjFile.getLoadPath());
                materialFileService.save(materialFile);
            }

            // 修改es中数据
            String materialNames =
                    String.join(",", cjRuinsMaterialMapper.getMaterialNames(cjRuinsMaterial.getRuinsId()));
            String materialTypes =
                    String.join(",", cjRuinsMaterialMapper.getMaterialTypes(cjRuinsMaterial.getRuinsId()));
            String materialFileNames =
                    String.join(",", materialFileMapper.getMaterialFileNames(cjRuinsMaterial.getRuinsId()));
            EsEntity esEntity = new EsEntity();
            esEntity.setTid(cjRuinsMaterial.getRuinsId())
                    .setMaterialNames(materialNames)
                    .setMaterialTypes(materialTypes)
                    .setMaterialFileNames(materialFileNames)
                    .setResourceId(cjRuinsMaterial.getRuinsId());
            //this.saveEs(esEntity);
        }
        return flag;
    }

    @Override
    public boolean delCjRuinsMaterial(List<Long> ids) {
        Long ruinsId = this.getById(ids.get(0)).getRuinsId();
        boolean flag = this.removeByIds(ids);
        if (flag) {
            val req = new QueryWrapper<CjUnearthedMaterialFile>()
                    .in("material_id", ids);
            materialFileService.remove(req);

            ids.forEach(id->{
                // 修改es中数据
                String materialNames = String.join(",", cjRuinsMaterialMapper.getMaterialNames(ruinsId));
                String materialTypes = String.join(",", cjRuinsMaterialMapper.getMaterialTypes(ruinsId));
                String materialFileNames = String.join(",", materialFileMapper.getMaterialFileNames(ruinsId));
                EsEntity esEntity = new EsEntity();
                esEntity.setTid(ruinsId)
                        .setMaterialNames(materialNames)
                        .setMaterialTypes(materialTypes)
                        .setMaterialFileNames(materialFileNames)
                        .setResourceId(ruinsId);
                //this.saveEs(esEntity);
            });
        }
        return flag;
    }

    @Override
    public CjRuinsUnearthedMaterial getCjRuinsMaterialById(Long id) {
        ArrayList<CjFile> CjFileList = new ArrayList<CjFile>();
        CjRuinsUnearthedMaterial unearthedMaterial = this.getById(id);
        val req = new QueryWrapper<CjUnearthedMaterialFile>()
                .eq("material_id", id);
        for (CjUnearthedMaterialFile materialFile : materialFileService.list(req)) {
            CjFile cjFile = new CjFile();
            cjFile.setId(materialFile.getId());
            cjFile.setName(materialFile.getFileName());
            cjFile.setSuffix(materialFile.getFileType());
            cjFile.setLoadPath(materialFile.getFilePath());
            CjFileList.add(cjFile);
        }
        unearthedMaterial.setCjFiles(CjFileList);
        return unearthedMaterial;
    }

    @Override
    public List<TypeAndCount> getTypeAndCount(Long ruinsId) {
        return cjRuinsMaterialMapper.getTypeAndCount(ruinsId);
    }

    /**
     * 插入es
     *
     * @param entity
     */
    public void saveEs(EsEntity entity) {
        //查询es中是否存在
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilders.boolQuery()
                        .must(QueryBuilders.matchQuery("tid", entity.getTid()))
                )
                .build();
        SearchHits<EsEntity> searchHits = elasticsearchRestTemplate.search(query, EsEntity.class);
        if (searchHits.getTotalHits() > 0) {
            for (SearchHit<EsEntity> searchHit : searchHits) {
                EsEntity esEntity = searchHit.getContent();
                Document document = Document.create();
                document.put("materialNames", entity.getMaterialNames());
                document.put("materialTypes", entity.getMaterialTypes());
                document.put("materialFileNames", entity.getMaterialFileNames());
                elasticsearchRestTemplate.update(UpdateQuery.builder(esEntity.getId())
                        .withDocument(document).build(), IndexCoordinates.of("cj_ruins_info"));
            }
        } else {
            EsEntity es = new EsEntity();
            es.setTid(entity.getTid())
                    .setMaterialNames(entity.getMaterialNames())
                    .setMaterialTypes(entity.getMaterialTypes())
                    .setMaterialFileNames(entity.getMaterialFileNames())
                    .setResourceId(entity.getResourceId());
            log.info("插入es成功");
            esEntityRepository.save(es);
        }
    }

}
