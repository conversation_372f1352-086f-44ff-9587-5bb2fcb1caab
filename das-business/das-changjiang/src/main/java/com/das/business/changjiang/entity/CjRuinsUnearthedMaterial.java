package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.das.business.changjiang.entity.responseEntity.TypeAndCount;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 遗址发掘资料实体
 * </p>
 *
 * <AUTHOR>
 * @since 2022-5-23
 */ 
@Data
public class CjRuinsUnearthedMaterial implements Serializable {

    // 主键ID
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(name = "id", value = "主键ID", required = true)
    private Long id;

    @ApiModelProperty(name = "ruinsId", value = "遗址id", required = false)
    private Long ruinsId;

    @ApiModelProperty(name = "name", value = "发掘资料名称", required = false)
    private String name;

    @ApiModelProperty(name = "type", value = "发掘资料类型", required = false)
    private Long type;

    @ApiModelProperty(name = "cover", value = "封面图地址", required = false)
    private String cover;

    @ApiModelProperty(name = "thumbnail", value = "封面缩略图", required = false)
    private String thumbnail;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间", required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(name = "updateTime", value = "修改时间", required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 发掘资料类型名称
     */
    @TableField(exist = false)
    private String typeName;

    /**
     * 发掘资料附件
     */
    @TableField(exist = false)
    private List<CjUnearthedMaterialFile> materialFileList;

    /**
     * 新增发掘资料附件
     */
    @TableField(exist = false)
    private List<CjUnearthedMaterialFile> materialFiles;

    @TableField(exist = false)
    private List<CjFile> cjFiles;

    @TableField(exist = false)
    private List<TypeAndCount> typeAndCounts;

}
