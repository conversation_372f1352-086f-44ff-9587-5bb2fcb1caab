package com.das.business.changjiang.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.business.changjiang.entity.CjCwhCemetery;
import com.das.business.changjiang.mapper.CjCwhCemeteryMapper;
import com.das.business.changjiang.service.ICjCwhCemeteryService;
import com.das.system.entity.SysCode;
import com.das.utils.common.DateUtil;
import com.das.utils.common.StringHandle;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultCode;
import com.das.utils.response.ResultMsg;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 墓葬信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-02
 */
@Service
public class CjCwhCemeteryServiceImpl extends ServiceImpl<CjCwhCemeteryMapper, CjCwhCemetery> implements ICjCwhCemeteryService {

    @Resource
    CjCwhCemeteryMapper cemeteryMapper;

    @Override
    public IPage<CjCwhCemetery> findByPage(PageEntity pageEntity) {
        return cemeteryMapper.findByPage(new Page<>(pageEntity.getCurrent(), pageEntity.getSize()),
                pageEntity.getParams(), pageEntity.getSort(), pageEntity.getOrder());
    }

    public boolean importExcel(MultipartFile file) {
        List<CjCwhCemetery> list = new ArrayList<>();
        ArrayList<ResultMsg> resultMsgs = new ArrayList<>();
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(inputStream);
            // 循环工作表Sheet
            HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(0);
            // 循环行Row
            for (int rowNum = 1; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
                CjCwhCemetery cemetery = new CjCwhCemetery();
                HSSFRow hssfRow = hssfSheet.getRow(rowNum);
                if (hssfRow == null) {
                    continue;
                }
                // 名称
                HSSFCell xh0 = hssfRow.getCell(0);
                if (xh0 != null) {
                    xh0.setCellType(CellType.STRING);
                    String stringCellValue = xh0.getStringCellValue();
                    if (StringUtils.isEmpty(stringCellValue)) {
                        continue;
                    }
                    cemetery.setName(stringCellValue);
                }
                HSSFCell xh1 = hssfRow.getCell(1);
                if (xh1 != null) {
                    xh1.setCellType(CellType.STRING);
                    String stringCellValue = xh1.getStringCellValue();
                    cemetery.setAge(stringCellValue);
                }
                HSSFCell xh2 = hssfRow.getCell(2);
                if (xh2 != null) {
                    xh2.setCellType(CellType.STRING);
                    String stringCellValue = xh2.getStringCellValue();
                    if (StringUtils.isNotBlank(stringCellValue)) {
                        cemetery.setDirection(Integer.valueOf(stringCellValue));
                    }
                }
                HSSFCell xh3 = hssfRow.getCell(3);
                if (xh3 != null) {
                    xh3.setCellType(CellType.STRING);
                    String stringCellValue = xh3.getStringCellValue();
                    if (StringUtils.isNotBlank(stringCellValue)) {
                        cemetery.setArea(Double.valueOf(stringCellValue));
                    }
                }
                HSSFCell xh4 = hssfRow.getCell(4);
                if (xh4 != null) {
                    xh4.setCellType(CellType.STRING);
                    String stringCellValue = xh4.getStringCellValue();
                    if (StringUtils.isNotBlank(stringCellValue)) {
                        cemetery.setCoffinChongchu(Integer.valueOf(stringCellValue));
                    }
                }
                HSSFCell xh5 = hssfRow.getCell(5);
                if (xh5 != null) {
                    xh5.setCellType(CellType.STRING);
                    String stringCellValue = xh5.getStringCellValue();
                    if (StringUtils.isNotBlank(stringCellValue)) {
                        cemetery.setCoffinNumber(Integer.valueOf(stringCellValue));
                    }
                }
                HSSFCell xh6 = hssfRow.getCell(6);
                if (xh6 != null) {
                    xh6.setCellType(CellType.STRING);
                    String stringCellValue = xh6.getStringCellValue();
                    if (StringUtils.isNotBlank(stringCellValue)) {
                        cemetery.setSteps(Integer.valueOf(stringCellValue));
                    }
                }
                HSSFCell xh7 = hssfRow.getCell(7);
                if (xh7 != null) {
                    xh7.setCellType(CellType.STRING);
                    String stringCellValue = xh7.getStringCellValue();
                    if ("男".equals(stringCellValue)) {
                        cemetery.setSex(1);
                    }
                    if ("女".equals(stringCellValue)) {
                        cemetery.setSex(2);
                    }
                    if ("未知".equals(stringCellValue)) {
                        cemetery.setSex(0);
                    }
                }
                HSSFCell xh8 = hssfRow.getCell(8);
                if (xh8 != null) {
                    xh8.setCellType(CellType.STRING);
                    String stringCellValue = xh8.getStringCellValue();
                    cemetery.setMusicalInstrument(stringCellValue);
                }
                HSSFCell xh9 = hssfRow.getCell(9);
                if (xh9 != null) {
                    xh9.setCellType(CellType.STRING);
                    String stringCellValue = xh9.getStringCellValue();
                    cemetery.setPottery(stringCellValue);
                }
                HSSFCell xh10 = hssfRow.getCell(10);
                if (xh10 != null) {
                    xh10.setCellType(CellType.STRING);
                    String stringCellValue = xh10.getStringCellValue();
                    cemetery.setUsePottery(stringCellValue);
                }
                HSSFCell xh11 = hssfRow.getCell(11);
                if (xh11 != null) {
                    xh11.setCellType(CellType.STRING);
                    String stringCellValue = xh11.getStringCellValue();
                    cemetery.setChariotHorseImplement(stringCellValue);
                }
                HSSFCell xh12 = hssfRow.getCell(12);
                if (xh12 != null) {
                    xh12.setCellType(CellType.STRING);
                    String stringCellValue = xh12.getStringCellValue();
                    cemetery.setWeaponry(stringCellValue);
                }
                HSSFCell xh13 = hssfRow.getCell(13);
                if (xh13 != null) {
                    xh13.setCellType(CellType.STRING);
                    String stringCellValue = xh13.getStringCellValue();
                    cemetery.setLacquerWoodWare(stringCellValue);
                }
                HSSFCell xh14 = hssfRow.getCell(14);
                if (xh14 != null) {
                    xh14.setCellType(CellType.STRING);
                    String stringCellValue = xh14.getStringCellValue();
                    cemetery.setJade(stringCellValue);
                }
                HSSFCell xh15 = hssfRow.getCell(15);
                if (xh15 != null) {
                    xh15.setCellType(CellType.STRING);
                    String stringCellValue = xh15.getStringCellValue();
                    cemetery.setOther(stringCellValue);
                }
                HSSFCell xh16 = hssfRow.getCell(16);
                if (xh16 != null) {
                    xh16.setCellType(CellType.STRING);
                    String stringCellValue = xh16.getStringCellValue();
                    cemetery.setRemark(stringCellValue);
                }
                list.add(cemetery);
            }
            return this.saveBatch(list);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                }
            }
        }
        return false;
    }


}
