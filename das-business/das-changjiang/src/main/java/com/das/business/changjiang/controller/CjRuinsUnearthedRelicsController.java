package com.das.business.changjiang.controller;


import com.das.annotation.RequestSecurity;
import com.das.business.changjiang.entity.CjRuinsUnearthedRelics;
import com.das.business.changjiang.service.ICjRuinsUnearthedRelicsService;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 考古遗址出土文物模块
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
@Api(value = "考古遗址出土文物模块", description = "考古遗址出土文物模块")
@RestController
@RequestMapping("/cjRuinsRelics")
public class CjRuinsUnearthedRelicsController {

    @Resource
    private ICjRuinsUnearthedRelicsService cjRuinsRelicsService;

    /**
     * 分页查询
     */
    @ApiOperation(value = "分页查询考古遗址出土文物信息")
    @RequestSecurity(value = "/getCjRuinsRelicsList", method = RequestMethod.POST)
    public ResultMsg getCjRuinsRelicsList(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(cjRuinsRelicsService.getCjRuinsRelicsList(pageEntity));
    }

    /**
     * 新增or修改考古遗址出土文物
     */
    @ApiOperation(value = "新增or修改考古遗址出土文物")
    @RequestSecurity(value = "/saveCjRuinsRelics", method = RequestMethod.POST)
    public ResultMsg saveCjRuinsRelics(@RequestBody CjRuinsUnearthedRelics cjRuinsRelics) {
        if (cjRuinsRelics.getId() == null) {
            return new ResultMsg(cjRuinsRelicsService.saveCjRuinsRelics(cjRuinsRelics));
        } else {
            return new ResultMsg(cjRuinsRelicsService.updateCjRuinsRelics(cjRuinsRelics));
        }
    }

    /**
     * 删除考古遗址出土文物
     */
    @ApiOperation(value = "删除考古遗址出土文物信息")
    @RequestSecurity(value = "/delCjRuinsRelics", method = RequestMethod.POST)
    public ResultMsg delCjRuinsRelics(@RequestBody List<Long> ids) {
        return new ResultMsg(cjRuinsRelicsService.delCjRuinsRelics(ids));
    }

    /**
     * 根据id查询考古遗址出土文物
     */
    @ApiOperation(value = "根据id查询考古遗址出土文物")
    @RequestSecurity(value = "/getCjRuinsRelicsById", method = RequestMethod.POST)
    public ResultMsg getCjRuinsRelicsById(@RequestParam Long id) {
        return new ResultMsg(cjRuinsRelicsService.getCjRuinsRelicsById(id));
    }

    /**
     * 根据id查询考古遗址出土文物分类
     */
    @ApiOperation(value = "根据id查询考古遗址出土文物分类")
    @RequestSecurity(value = "/getTypeAndCount", method = RequestMethod.POST)
    public ResultMsg getTypeAndCount(@RequestParam Long ruinsId) {
        return new ResultMsg(cjRuinsRelicsService.getTypeAndCount(ruinsId));
    }

}
