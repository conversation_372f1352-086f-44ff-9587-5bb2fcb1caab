package com.das.business.changjiang.service;

import com.das.business.changjiang.controller.vo.CjRuinsInfoDetailVO;
import com.das.business.changjiang.controller.vo.CjRuinsInfoMapVO;
import com.das.business.changjiang.entity.CjRuinsInfoNew;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 考古遗址基础信息表-20241225调整业务后新表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public interface ICjRuinsInfoNewService extends IService<CjRuinsInfoNew> {

    /**
     * 查询地图分布数据
     * @param params
     * @return
     */
    List<CjRuinsInfoMapVO> getRuinsDetail(Map<String, String> params);

    /**
     * 查询墓地详情
     * @param id
     * @return
     */
    CjRuinsInfoDetailVO getRuinsDetailById(Long id);

}
