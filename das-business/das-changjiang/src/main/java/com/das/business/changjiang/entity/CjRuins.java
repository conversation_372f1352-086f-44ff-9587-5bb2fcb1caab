package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

/**
 * 考古遗址实体
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Data
public class CjRuins extends Model<CjRuins> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private long majorId;
    private long minorId;
    private String name;
    @TableField(value = "`desc`")
    private String desc;
    private String excavateAge;
    private String ruinsAge;
    private Long parentId;
    private long leafNode;
    private String createTime;

    @TableField(exist = false)
    private List<CjRuins> children;

    @TableField(exist = false)
    private int type;

    @TableField(exist = false)
    private String startTime;

    @TableField(exist = false)
    private String endTime;

    @TableField(exist = false)
    private String majorIdName;

    @TableField(exist = false)
    private String minorIdName;

    //@TableField(exist = false)
    //private List<Double> gpsArr;

}
