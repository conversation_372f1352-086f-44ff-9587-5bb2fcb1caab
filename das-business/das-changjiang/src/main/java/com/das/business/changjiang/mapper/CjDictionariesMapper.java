package com.das.business.changjiang.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.entity.CjDictionaries;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.das.system.entity.SysCode;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-26
 */
public interface CjDictionariesMapper extends BaseMapper<CjDictionaries> {

    IPage<CjDictionaries> getCjDictionariesList(@Param(value = "page") Page<SysCode> page,
                                                @Param(value = "params") Map<String, Object> params,
                                                @Param(value = "sort") String sort, @Param(value = "order") String order);

    List<Map> getCjDictionariesTree(@Param(value = "class1")String class1);

}
