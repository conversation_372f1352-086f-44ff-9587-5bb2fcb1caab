package com.das.business.changjiang.controller;


import com.das.annotation.RequestSecurity;
import com.das.business.changjiang.entity.CjCollect;
import com.das.business.changjiang.entity.CjMark;
import com.das.business.changjiang.service.impl.CjCollectServiceImpl;
import com.das.business.changjiang.service.impl.CjMarkServiceImpl;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import org.springframework.stereotype.Controller;

import javax.annotation.Resource;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
@Api(value = "长江GIS收藏接口", description = "长江GIS收藏接口")
@RestController
@RequestMapping("/cjCollect")
public class CjCollectController {

    @Resource
    private CjCollectServiceImpl cjCollectService;

    /**
     * 分页查询
     */
    @ApiOperation(value = "分页查询标记信息")
    @RequestSecurity(value = "/getCjCollectList",method = RequestMethod.POST)
    public ResultMsg getCjCollect(@RequestBody PageEntity pageEntity){
//        cjCollectService.save()
        return new ResultMsg(cjCollectService.getCjCollectList(pageEntity));
    }

    /**
     * 新增or修改标注
     */
    @ApiOperation(value = "收藏信息")
    @RequestSecurity(value = "/saveCjCollect",method = RequestMethod.POST)
    public ResultMsg saveCjCollect(@RequestBody CjCollect collect){
        return new ResultMsg(cjCollectService.saveCjCollect(collect));
    }

    /**
     * 删除标注信息
     */
    @ApiOperation(value = "取消收藏信息")
    @RequestSecurity(value = "/delCjCollect",method = RequestMethod.POST)
    public ResultMsg delCjCollect(@RequestParam Long id){
        return new ResultMsg(cjCollectService.delCjCollect(id));
    }

    /**
     * 根据id查询标注信息
     */
    @ApiOperation(value = "根据id查询标注信息")
    @RequestSecurity(value = "/getCjCollectById",method = RequestMethod.POST)
    public ResultMsg getCjCollectById(@RequestParam Long id){
        return new ResultMsg(cjCollectService.getCjCollectById(id));
    }
}
