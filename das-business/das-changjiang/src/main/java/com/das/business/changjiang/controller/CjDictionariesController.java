package com.das.business.changjiang.controller;


import com.das.annotation.RequestSecurity;
import com.das.business.changjiang.entity.CjDictionaries;
import com.das.business.changjiang.service.ICjDictionariesService;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-26
 */
@Api(value = "辞典接口")
@RestController
@RequestMapping("/cjDictionaries")
public class CjDictionariesController {

    @Resource
    ICjDictionariesService dictionariesService;

    /**
     * 分页查询辞典
     */
    @ApiOperation(value = "分页查询辞典")
    @RequestSecurity(value = "/getCjDictionariesList",method = RequestMethod.POST)
    public ResultMsg getCjDictionariesList(@RequestBody PageEntity pageEntity){
        return new ResultMsg(dictionariesService.getCjDictionariesList(pageEntity));
    }

    /**
     * 查询分类树
     */
    @ApiOperation(value = "查询分类树")
    @RequestSecurity(value = "/getCjDictionariesTree", method = RequestMethod.POST)
    public ResultMsg getCjDictionariesTree(String class1) {
        return new ResultMsg(dictionariesService.getCjDictionariesTree(class1));
    }

    /**
     * 新增或修改辞典
     */
    @ApiOperation(value = "新增或修改辞典")
    @RequestSecurity(value = "/saveOrUpdate", method = RequestMethod.POST)
    public ResultMsg saveOrUpdate(@RequestBody CjDictionaries dictionaries) {
        return new ResultMsg(dictionariesService.saveOrUpdateDic(dictionaries));
    }

    /**
     * 删除辞典
     */
    @ApiOperation(value = "删除辞典")
    @RequestSecurity(value = "/removeByIds", method = RequestMethod.POST)
    public ResultMsg getCjDictionariesTree2(@RequestBody List<Long> ids) {
        return new ResultMsg(dictionariesService.removeDic(ids));
    }


}
