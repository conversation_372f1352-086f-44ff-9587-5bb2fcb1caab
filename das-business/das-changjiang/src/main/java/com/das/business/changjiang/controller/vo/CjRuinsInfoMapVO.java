package com.das.business.changjiang.controller.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

/**
 * 考古遗址基础信息表
 *
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CjRuinsInfoMapVO implements Serializable {
    /**
     * 主键id
     */
    private Long id;
    private String name;
    private String discoverTime;
    private String age;
    private String gps;
    private String report;
}