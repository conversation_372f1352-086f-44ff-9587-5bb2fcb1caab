package com.das.business.changjiang.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.controller.vo.CjRuinsInfoTombsFollowNameVO;
import com.das.business.changjiang.controller.vo.CjRuinsInfoTombsVO;
import com.das.business.changjiang.entity.CjRuinsInfoTombs;
import com.das.business.changjiang.entity.CjRuinsInfoTombsCoffin;
import com.das.business.changjiang.entity.CjRuinsInfoTombsFollow;
import com.das.business.changjiang.entity.CjRunisInfoFile;
import com.das.business.changjiang.mapper.CjRuinsInfoTombsMapper;
import com.das.business.changjiang.service.ICjRuinsInfoTombsCoffinService;
import com.das.business.changjiang.service.ICjRuinsInfoTombsFollowService;
import com.das.business.changjiang.service.ICjRuinsInfoTombsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.business.changjiang.service.ICjRunisInfoFileService;
import com.das.utils.response.PageEntity;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.swing.text.html.Option;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 墓地基本信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Service
@RequiredArgsConstructor
public class CjRuinsInfoTombsServiceImpl extends ServiceImpl<CjRuinsInfoTombsMapper, CjRuinsInfoTombs> implements ICjRuinsInfoTombsService {

    private final ICjRuinsInfoTombsFollowService ruinsInfoTombsFollowService;
    private final ICjRuinsInfoTombsCoffinService ruinsInfoTombsCoffinService;
    private final ICjRunisInfoFileService cjRunisInfoFileService;
    private final CjRuinsInfoTombsMapper cjRuinsInfoTombsMapper;

    @Override
    public IPage<CjRuinsInfoTombs> findByPage(PageEntity pageEntity) {
        return cjRuinsInfoTombsMapper.findByPage(new Page<>(pageEntity.getCurrent(), pageEntity.getSize()),
                pageEntity.getParams(), pageEntity.getSort(), pageEntity.getOrder());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(CjRuinsInfoTombsVO cjRuinsInfoTombsVO) {
        if (this.save(cjRuinsInfoTombsVO)) {
            //保存椁室大小
            Optional.ofNullable(cjRuinsInfoTombsVO.getCoffinList())
                    .orElseGet(ArrayList::new)
                    .forEach(item -> {
                        item.setTombsId(cjRuinsInfoTombsVO.getId());
                    });
            if (ObjectUtil.isNotNull(cjRuinsInfoTombsVO.getCoffinList())) {
                ruinsInfoTombsCoffinService.saveBatch(cjRuinsInfoTombsVO.getCoffinList());
            }

            //保存示意图片
            Optional.ofNullable(cjRuinsInfoTombsVO.getImgList())
                    .orElseGet(ArrayList::new)
                    .forEach(item -> {
                        item.setType(2);
                        item.setRuinsId(cjRuinsInfoTombsVO.getId());
                    });
            if (ObjectUtil.isNotNull(cjRuinsInfoTombsVO.getImgList())) {
                cjRunisInfoFileService.saveBatch(cjRuinsInfoTombsVO.getImgList());
            }

            //保存随葬遗物列表
            List<CjRuinsInfoTombsFollow> followList = new ArrayList<>();
            Optional.ofNullable(cjRuinsInfoTombsVO.getFollow1List())
                    .orElseGet(ArrayList::new)
                    .forEach(item -> {
                        followList.add(new CjRuinsInfoTombsFollow(1, item.getName(), item.getNumber(), cjRuinsInfoTombsVO.getId()));
                    });
            Optional.ofNullable(cjRuinsInfoTombsVO.getFollow2List())
                    .orElseGet(ArrayList::new)
                    .forEach(item -> {
                        followList.add(new CjRuinsInfoTombsFollow(2, item.getName(), item.getNumber(), cjRuinsInfoTombsVO.getId()));
                    });
            Optional.ofNullable(cjRuinsInfoTombsVO.getFollow3List())
                    .orElseGet(ArrayList::new)
                    .forEach(item -> {
                        followList.add(new CjRuinsInfoTombsFollow(3, item.getName(), item.getNumber(), cjRuinsInfoTombsVO.getId()));
                    });
            Optional.ofNullable(cjRuinsInfoTombsVO.getFollow4List())
                    .orElseGet(ArrayList::new)
                    .forEach(item -> {
                        followList.add(new CjRuinsInfoTombsFollow(4, item.getName(), item.getNumber(), cjRuinsInfoTombsVO.getId()));
                    });
            Optional.ofNullable(cjRuinsInfoTombsVO.getFollow5List())
                    .orElseGet(ArrayList::new)
                    .forEach(item -> {
                        followList.add(new CjRuinsInfoTombsFollow(5, item.getName(), item.getNumber(), cjRuinsInfoTombsVO.getId()));
                    });
            Optional.ofNullable(cjRuinsInfoTombsVO.getFollow6List())
                    .orElseGet(ArrayList::new)
                    .forEach(item -> {
                        followList.add(new CjRuinsInfoTombsFollow(6, item.getName(), item.getNumber(), cjRuinsInfoTombsVO.getId()));
                    });
            Optional.ofNullable(cjRuinsInfoTombsVO.getFollow7List())
                    .orElseGet(ArrayList::new)
                    .forEach(item -> {
                        followList.add(new CjRuinsInfoTombsFollow(7, item.getName(), item.getNumber(), cjRuinsInfoTombsVO.getId()));
                    });
            if (ObjectUtil.isNotNull(followList.isEmpty())) {
                ruinsInfoTombsFollowService.saveBatch(followList);
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean update(CjRuinsInfoTombsVO cjRuinsInfoTombsVO) {
        if (this.updateById(cjRuinsInfoTombsVO)) {
            //保存椁室大小
            Optional.ofNullable(cjRuinsInfoTombsVO.getCoffinList())
                    .orElseGet(ArrayList::new)
                    .forEach(item -> {
                        item.setTombsId(cjRuinsInfoTombsVO.getId());
                    });
            if (ObjectUtil.isNotNull(cjRuinsInfoTombsVO.getCoffinList())) {
                ruinsInfoTombsCoffinService.remove(new QueryWrapper<CjRuinsInfoTombsCoffin>().eq("tombs_id", cjRuinsInfoTombsVO.getId()));
                ruinsInfoTombsCoffinService.saveBatch(cjRuinsInfoTombsVO.getCoffinList());
            }

            //保存示意图片
            Optional.ofNullable(cjRuinsInfoTombsVO.getImgList())
                    .orElseGet(ArrayList::new)
                    .forEach(item -> {
                        item.setType(2);
                        item.setRuinsId(cjRuinsInfoTombsVO.getId());
                    });
            if (ObjectUtil.isNotNull(cjRuinsInfoTombsVO.getImgList())) {
                cjRunisInfoFileService.remove(new QueryWrapper<CjRunisInfoFile>().eq("ruins_id", cjRuinsInfoTombsVO.getId()));
                cjRunisInfoFileService.saveBatch(cjRuinsInfoTombsVO.getImgList());
            }

            //保存随葬遗物列表
            List<CjRuinsInfoTombsFollow> followList = new ArrayList<>();
            Optional.ofNullable(cjRuinsInfoTombsVO.getFollow1List())
                    .orElseGet(ArrayList::new)
                    .forEach(item -> {
                        followList.add(new CjRuinsInfoTombsFollow(1, item.getName(), item.getNumber(), cjRuinsInfoTombsVO.getId()));
                    });
            Optional.ofNullable(cjRuinsInfoTombsVO.getFollow2List())
                    .orElseGet(ArrayList::new)
                    .forEach(item -> {
                        followList.add(new CjRuinsInfoTombsFollow(2, item.getName(), item.getNumber(), cjRuinsInfoTombsVO.getId()));
                    });
            Optional.ofNullable(cjRuinsInfoTombsVO.getFollow3List())
                    .orElseGet(ArrayList::new)
                    .forEach(item -> {
                        followList.add(new CjRuinsInfoTombsFollow(3, item.getName(), item.getNumber(), cjRuinsInfoTombsVO.getId()));
                    });
            Optional.ofNullable(cjRuinsInfoTombsVO.getFollow4List())
                    .orElseGet(ArrayList::new)
                    .forEach(item -> {
                        followList.add(new CjRuinsInfoTombsFollow(4, item.getName(), item.getNumber(), cjRuinsInfoTombsVO.getId()));
                    });
            Optional.ofNullable(cjRuinsInfoTombsVO.getFollow5List())
                    .orElseGet(ArrayList::new)
                    .forEach(item -> {
                        followList.add(new CjRuinsInfoTombsFollow(5, item.getName(), item.getNumber(), cjRuinsInfoTombsVO.getId()));
                    });
            Optional.ofNullable(cjRuinsInfoTombsVO.getFollow6List())
                    .orElseGet(ArrayList::new)
                    .forEach(item -> {
                        followList.add(new CjRuinsInfoTombsFollow(6, item.getName(), item.getNumber(), cjRuinsInfoTombsVO.getId()));
                    });
            Optional.ofNullable(cjRuinsInfoTombsVO.getFollow7List())
                    .orElseGet(ArrayList::new)
                    .forEach(item -> {
                        followList.add(new CjRuinsInfoTombsFollow(7, item.getName(), item.getNumber(), cjRuinsInfoTombsVO.getId()));
                    });
            if (ObjectUtil.isNotNull(followList.isEmpty())) {
                ruinsInfoTombsFollowService.remove(new QueryWrapper<CjRuinsInfoTombsFollow>().eq("tombs_id", cjRuinsInfoTombsVO.getId()));
                ruinsInfoTombsFollowService.saveBatch(followList);
            }
            return true;
        }
        return false;
    }


    @Override
    public CjRuinsInfoTombsVO getDetailById(Long id) {
        CjRuinsInfoTombs cjRuinsInfoTombs = this.getById(id);
        CjRuinsInfoTombsVO res = new CjRuinsInfoTombsVO();
        if (cjRuinsInfoTombs == null) {
            return new CjRuinsInfoTombsVO();
        }
        BeanUtils.copyProperties(cjRuinsInfoTombs, res);
        //查询椁室大小
        QueryWrapper<CjRuinsInfoTombsCoffin> queryWrapperCoffin = new QueryWrapper<>();
        queryWrapperCoffin.lambda().eq(CjRuinsInfoTombsCoffin::getTombsId, res.getId());
        res.setCoffinList(ruinsInfoTombsCoffinService.list(queryWrapperCoffin));

        //查询示意图片
        QueryWrapper<CjRunisInfoFile> fileQueryWrapper = new QueryWrapper<>();
        fileQueryWrapper.lambda().eq(CjRunisInfoFile::getType, 2);
        fileQueryWrapper.lambda().eq(CjRunisInfoFile::getRuinsId, res.getId());
        res.setImgList(cjRunisInfoFileService.list(fileQueryWrapper));

        //查询随葬遗物列表并分组
        QueryWrapper<CjRuinsInfoTombsFollow> queryWrapperFollow = new QueryWrapper<>();
        queryWrapperFollow.lambda().eq(CjRuinsInfoTombsFollow::getTombsId, res.getId());
        List<CjRuinsInfoTombsFollow> followList = ruinsInfoTombsFollowService.list(queryWrapperFollow);
        if (!followList.isEmpty()) {
            List<CjRuinsInfoTombsFollowNameVO> followNameVOList = new ArrayList<>(followList.size());
            followList.forEach(item -> {
                followNameVOList.add(new CjRuinsInfoTombsFollowNameVO(String.valueOf(item.getId()), item.getName(), item.getType(), item.getNumber()));
            });
            res.setFollow1List(followNameVOList.stream().filter(item -> item.getType().equals(1)).collect(Collectors.toList()));
            res.setFollow2List(followNameVOList.stream().filter(item -> item.getType().equals(2)).collect(Collectors.toList()));
            res.setFollow3List(followNameVOList.stream().filter(item -> item.getType().equals(3)).collect(Collectors.toList()));
            res.setFollow4List(followNameVOList.stream().filter(item -> item.getType().equals(4)).collect(Collectors.toList()));
            res.setFollow5List(followNameVOList.stream().filter(item -> item.getType().equals(5)).collect(Collectors.toList()));
            res.setFollow6List(followNameVOList.stream().filter(item -> item.getType().equals(6)).collect(Collectors.toList()));
            res.setFollow7List(followNameVOList.stream().filter(item -> item.getType().equals(7)).collect(Collectors.toList()));
        }
        return res;
    }

}
