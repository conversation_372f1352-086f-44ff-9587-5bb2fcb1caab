package com.das.business.changjiang.controller;

import com.das.utils.response.ResultCode;
import com.das.utils.response.ResultMsg;
import com.das.yml.FtpConfig;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.UUID;


/**
 * 上传文件
 *
 * <AUTHOR> @since 2019-08-18
 */
@RestController
@RequestMapping("/sysResource")
@Slf4j
@RequiredArgsConstructor
public class SysResourceController {

    private final FtpConfig ftpConfig;

    @ApiOperation(value = "上传附件", notes = "上传附件")
    @RequestMapping(value = "/uploadFile", method = RequestMethod.POST)
    public ResultMsg uploadFile(@RequestParam("file") MultipartFile file) {
        String url = "";
        if (file != null) {
            //取得当前上传文件的文件名称
            String fileName = getRealFileName(file);
            if (!StringUtils.isEmpty(fileName)) {
                //文件后缀名
                String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
                //文件唯一名
                String uuid = UUID.randomUUID().toString().replace("-", "");
                //存储文件地址
                String path = ftpConfig.getFtpPath() + "file/" + uuid + "." + suffix;
                //前端访问地址
                url = ftpConfig.getFtpUrl() + "file/" + uuid + "." + suffix;
                try {
                    //创建文件路径
                    File dest = new File(path);
                    File pathFile = new File(ftpConfig.getFtpPath() + "file/");
                    if(!pathFile.exists()){
                        pathFile.mkdir();
                    }
                    //将上传文件存储到该路径上
                    file.transferTo(dest);
                } catch (IOException e) {
                    log.error(e.getMessage(), "上传文件失败！");
                    return new ResultMsg(ResultCode.CODE_40002, false);
                }
            }
        }
        return new ResultMsg(url);
    }


    public String getRealFileName(MultipartFile file){
        // 获取上传的文件名称，并结合存放路径，构建新的文件名称
        String filename = file.getOriginalFilename();

        // 文件上传时，Chrome和IE/Edge对于originalFilename处理不同
        // Chrome 会获取到该文件的直接文件名称，IE/Edge会获取到文件上传时完整路径/文件名
        // Check for Unix-style path
        int unixSep = filename.lastIndexOf('/');
        // Check for Windows-style path
        int winSep = filename.lastIndexOf('\\');
        // Cut off at latest possible point
        int pos = (winSep > unixSep ? winSep : unixSep);
        if (pos != -1)  {
            filename = filename.substring(pos + 1);
        }
        return filename;
    }


}
