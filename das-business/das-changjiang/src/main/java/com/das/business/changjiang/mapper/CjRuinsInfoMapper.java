package com.das.business.changjiang.mapper;

import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.Map;
import java.util.List;

import com.das.business.changjiang.entity.CjRuinsInfo;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025-01-02
 */
@Mapper
public interface CjRuinsInfoMapper extends BaseMapper<CjRuinsInfo> {

    IPage<CjRuinsInfo> findByPage(@Param(value = "page") Page<CjRuinsInfo> page,
                                  @Param(value = "params") Map<String, Object> params,
                                  @Param(value = "sort") String sort,
                                  @Param(value = "order") String order);

    List<Object> execCustomSql(@Param("execCustomSql") String execCustomSql, Map<String, Object> param);

}
