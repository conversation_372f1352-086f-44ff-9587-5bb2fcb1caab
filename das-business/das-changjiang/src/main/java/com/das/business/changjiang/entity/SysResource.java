package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 *
 * <AUTHOR> @since 2019-08-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysResource extends Model<SysResource> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键标识
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 文件名
     */
    private String filename;

    /**
     * 后缀名（包括点）
     */
    private String suffixname;

    /**
     * 文件类型（1图片 2文本 3其他）
     */
    private String type;

    /**
     * 文件备注（介绍）
     */
    private String comment;

    /**
     * 上传时间
     */
    private LocalDateTime uploadtime;

    public SysResource() {
    }

    public SysResource(String filename, String suffixname, String type) {
        this.filename = filename;
        this.suffixname = suffixname;
        this.type = type;
    }

    public String getPath() {
        return this.filename + '.' + this.suffixname;
    }
}
