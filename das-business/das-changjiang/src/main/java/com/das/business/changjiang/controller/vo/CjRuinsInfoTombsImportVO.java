package com.das.business.changjiang.controller.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 墓葬基本信息表导入
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@Data
public class CjRuinsInfoTombsImportVO implements Serializable {

    @ExcelProperty("墓葬名称")
    private String name;

    @ExcelProperty("年代")
    private String dynasty;

    @ExcelProperty("盗扰情况")
    private String stealInfo;

    @ExcelProperty("封土-有/无")
    private String fiefHave;

    @ExcelProperty("封土-形制")
    private String fiefShape;

    @ExcelProperty("封土-底径（米）")
    private String fiefBottomDiameter;

    @ExcelProperty("封土-高度（米）")
    private String fiefHeight;

    @ExcelProperty("墓坑-形制")
    private String holeShape;

    @ExcelProperty("墓坑-方向")
    private String holeDirection;

    @ExcelProperty("墓坑-墓口长（米）")
    private String holeTopLong;

    @ExcelProperty("墓坑-墓口宽（米）")
    private String holeTopWidth;

    @ExcelProperty("墓坑-墓底长（米）")
    private String holeBottomLength;

    @ExcelProperty("墓坑-墓底宽（米）")
    private String holeBottomWidth;

    @ExcelProperty("墓坑-墓深（米）")
    private String holeBottomHeight;

    @ExcelProperty("墓坑-台阶数量")
    private String holeStep;

    @ExcelProperty("墓坑-墓道有/无")
    private String holePassageHave;

    @ExcelProperty("墓坑-墓道数量")
    private String holePassageNumber;

    @ExcelProperty("墓坑-坡长（米）")
    private String holePassageTotalLength;

    @ExcelProperty("墓坑-口长（米）")
    private String holePassageTopLength;

    @ExcelProperty("墓坑-口长端（米）")
    private String holePassageTopLengthLength;

    @ExcelProperty("墓坑-口短端（米）")
    private String holePassageTopShortLength;

    @ExcelProperty("墓坑-底端（米）")
    private String holePassageBottomLength;

    @ExcelProperty("墓坑-坡度")
    private String holePassageAngle;

    @ExcelProperty("填土描述")
    private String fillSoil;

    @ExcelProperty("其他")
    private String other;

    @ExcelProperty("棺椁重数")
    private String coffinNumber;

    @ExcelProperty("分室数量")
    private String divideNumber;

    @ExcelProperty("葬式")
    private String way;

    @ExcelProperty("墓主性别")
    private String sex;

    @ExcelProperty("墓主年龄")
    private String age;

    @ExcelProperty("备注")
    private String remark;

}
