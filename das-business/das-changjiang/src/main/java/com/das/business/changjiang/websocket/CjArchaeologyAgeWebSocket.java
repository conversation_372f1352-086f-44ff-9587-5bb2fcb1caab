package com.das.business.changjiang.websocket;


import com.alibaba.fastjson.JSONObject;
import com.das.business.changjiang.service.ICjRuinsDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;

/**
 * 时空演变查询接口
 */
@ServerEndpoint(value = "/ruinsDetail")
@Component
public class CjArchaeologyAgeWebSocket {

    private Session session;

    private static ICjRuinsDetailService ruinsDetailService;

    @Autowired
    public void setRuinsDetailService(ICjRuinsDetailService ruinsDetailService){
        this.ruinsDetailService = ruinsDetailService;
    }

    @OnOpen
    public void onOpen(Session session){
        this.session = session;
    }

    @OnClose
    public void onClose(Session session){
        System.out.println("关闭链接");
    }

    @OnMessage
    public void onMessage(String msg, Session session) throws IOException {
        String[] param = msg.split(",");
        session.getBasicRemote().sendText(JSONObject.toJSONString(ruinsDetailService.getAgeRuinsDetail(Integer.parseInt(param[0]), Integer.parseInt(param[1]))));
    }

    @OnError
    public void onError(Session session, Throwable error){
        System.out.println("error");
    }

}
