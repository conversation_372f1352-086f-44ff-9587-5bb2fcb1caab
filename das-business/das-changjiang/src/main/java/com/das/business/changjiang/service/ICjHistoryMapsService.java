package com.das.business.changjiang.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.business.changjiang.entity.CjHistoryMaps;
import com.das.utils.response.PageEntity;

/**
 * <p>
 * 历史地图表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
public interface ICjHistoryMapsService extends IService<CjHistoryMaps> {

    /**
     * 分页查询历史地图列表
     *
     * @param pageEntity 分页查询参数
     * @return 分页结果
     */
    IPage<CjHistoryMaps> getCjHistoryMapsList(PageEntity pageEntity);

    /**
     * 根据ID查询历史地图详情
     *
     * @param id 历史地图ID
     * @return 历史地图详情
     */
    CjHistoryMaps getCjHistoryMapsById(Long id);

    /**
     * 保存或更新历史地图
     *
     * @param cjHistoryMaps 历史地图对象
     * @return 操作结果
     */
    boolean saveCjHistoryMaps(CjHistoryMaps cjHistoryMaps);

    /**
     * 删除历史地图
     *
     * @param id 历史地图ID
     * @return 操作结果
     */
    boolean deleteCjHistoryMaps(Long id);

    /**
     * 批量删除历史地图
     *
     * @param ids 历史地图ID数组
     * @return 操作结果
     */
    boolean batchDeleteCjHistoryMaps(Long[] ids);

    /**
     * 更新发布状态
     *
     * @param id            历史地图ID
     * @param publishStatus 发布状态
     * @return 操作结果
     */
    boolean updatePublishStatus(Long id, Integer publishStatus);
}
