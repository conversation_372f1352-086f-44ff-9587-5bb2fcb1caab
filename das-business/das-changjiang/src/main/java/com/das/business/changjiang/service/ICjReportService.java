package com.das.business.changjiang.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.das.business.changjiang.entity.CjReport;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.utils.response.PageEntity;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
public interface ICjReportService extends IService<CjReport> {

    IPage<CjReport> getCjReportList(PageEntity pageEntity);

     List<CjReport> getCjReportTop();

    boolean saveCjReport(CjReport cjReport);

    boolean delCjReport(Long  id);

    CjReport getCjReportById(Long id);

    boolean publishInfo(CjReport cjReport);

    List<CjReport> getCjReportByRuinsId(Long id);
}
