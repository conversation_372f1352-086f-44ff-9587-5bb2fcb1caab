package com.das.business.changjiang.controller.vo.vo;

import lombok.Data;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

/**
 * 
 * @Description
 * <AUTHOR>
 * @Date 2025-01-14
 */
@Data
public class CjRuinsInfoTombsVO {

	@ApiModelProperty(value = "主键", required = false)
	private Long id;

	@ApiModelProperty(value = "所属遗址", required = false)
	private Long ruinsId;

	@ApiModelProperty(value = "墓葬名称", required = false)
	private String name;

	@ApiModelProperty(value = "年代", required = false)
	private String dynasty;

	@ApiModelProperty(value = "盗扰情况", required = false)
	private String stealInfo;

	@ApiModelProperty(value = "封土-有/无", required = false)
	private String fiefHave;

	@ApiModelProperty(value = "封土-形制", required = false)
	private String fiefShape;

	@ApiModelProperty(value = "封土-底径（米）", required = false)
	private String fiefBottomDiameter;

	@ApiModelProperty(value = "封土-高度（米）", required = false)
	private String fiefHeight;

	@ApiModelProperty(value = "墓坑-形制", required = false)
	private String holeShape;

	@ApiModelProperty(value = "墓坑-方向", required = false)
	private String holeDirection;

	@ApiModelProperty(value = "墓坑-墓口长（米）", required = false)
	private String holeTopLong;

	@ApiModelProperty(value = "墓坑-墓口宽（米）", required = false)
	private String holeTopWidth;

	@ApiModelProperty(value = "墓坑-墓底长（米）", required = false)
	private String holeBottomLength;

	@ApiModelProperty(value = "墓坑-墓底宽（米）", required = false)
	private String holeBottomWidth;

	@ApiModelProperty(value = "墓坑-墓深（米）", required = false)
	private String holeBottomHeight;

	@ApiModelProperty(value = "墓坑-台阶数量", required = false)
	private String holeStep;

	@ApiModelProperty(value = "墓坑-墓道有/无", required = false)
	private String holePassageHave;

	@ApiModelProperty(value = "墓坑-墓道数量", required = false)
	private String holePassageNumber;

	@ApiModelProperty(value = "墓坑-坡长（米）", required = false)
	private String holePassageTotalLength;

	@ApiModelProperty(value = "墓坑-口长（米）", required = false)
	private String holePassageTopLength;

	@ApiModelProperty(value = "墓坑-口长端（米）", required = false)
	private String holePassageTopLengthLength;

	@ApiModelProperty(value = "墓坑-口短端（米）", required = false)
	private String holePassageTopShortLength;

	@ApiModelProperty(value = "墓坑-底端（米）", required = false)
	private String holePassageBottomLength;

	@ApiModelProperty(value = "墓坑-坡度", required = false)
	private String holePassageAngle;

	@ApiModelProperty(value = "填土描述", required = false)
	private String fillSoil;

	@ApiModelProperty(value = "其他", required = false)
	private String other;

	@ApiModelProperty(value = "棺椁重数", required = false)
	private String coffinNumber;

	@ApiModelProperty(value = "分室数量", required = false)
	private String divideNumber;

	@ApiModelProperty(value = "葬式", required = false)
	private String way;

	@ApiModelProperty(value = "墓主性别", required = false)
	private String sex;

	@ApiModelProperty(value = "墓主年龄", required = false)
	private String age;

	@ApiModelProperty(value = "备注", required = false)
	private String remark;

	@ApiModelProperty(value = "创建时间", required = false)
	private String createTime;

	@ApiModelProperty(value = "修改时间", required = false)
	private String updateTime;

}
