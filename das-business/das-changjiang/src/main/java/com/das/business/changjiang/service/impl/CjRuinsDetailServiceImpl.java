package com.das.business.changjiang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.das.business.changjiang.entity.CjCollectionInfo;
import com.das.business.changjiang.entity.CjReport;
import com.das.business.changjiang.entity.CjRuinsDetail;
import com.das.business.changjiang.entity.requestEntity.RuinsDetailAge;
import com.das.business.changjiang.mapper.CjRuinsDetailMapper;
import com.das.business.changjiang.service.ICjRuinsDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.utils.response.ResultMsg;
import lombok.var;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 时空演变WebSocket查询接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Service
public class CjRuinsDetailServiceImpl extends ServiceImpl<CjRuinsDetailMapper, CjRuinsDetail> implements ICjRuinsDetailService {

    @Resource
    CjRuinsDetailMapper cjRuinsDetailMapper;

    @Resource
    CjCollectionInfoServiceImpl cjCollectionInfoService;

    @Resource
    CjReportServiceImpl cjReportService;

    @Override
    public List<Map> getAgeRuinsDetail(int start, int end) {
        return cjRuinsDetailMapper.getAgeRuinsDetail(start, end);
    }

    @Override
    public ResultMsg getRuinsDetail(long id) {
        var queryWrapper = new QueryWrapper<CjRuinsDetail>();
        queryWrapper.select("id", "name", "`desc`", "excavate_age", "ruins_age", "gps", "type", "line_set", "color").eq("id", id);
        Map<String, Object> res = this.getMap(queryWrapper);

        //查询文物
        List<CjCollectionInfo> cjCollectionInfo = cjCollectionInfoService.getInfoByRuinsId(id);
        res.put("collection", (cjCollectionInfo==null?new CjCollectionInfo():cjCollectionInfo));

        //查询报告
        res.put("report", cjReportService.getCjReportByRuinsId(id));
        return new ResultMsg(res);
    }


}
