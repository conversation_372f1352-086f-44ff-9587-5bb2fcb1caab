package com.das.business.changjiang.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.business.changjiang.entity.CjDigitalMaps;
import com.das.system.entity.PageEntity;

/**
 * <p>
 * 数字地图表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
public interface ICjDigitalMapsService extends IService<CjDigitalMaps> {

    /**
     * 分页查询数字地图列表
     *
     * @param pageEntity 分页查询参数
     * @return 分页结果
     */
    IPage<CjDigitalMaps> getCjDigitalMapsList(PageEntity pageEntity);

    /**
     * 根据ID查询数字地图详情
     *
     * @param id 数字地图ID
     * @return 数字地图详情
     */
    CjDigitalMaps getCjDigitalMapsById(Long id);

    /**
     * 保存或更新数字地图
     *
     * @param cjDigitalMaps 数字地图对象
     * @return 操作结果
     */
    boolean saveCjDigitalMaps(CjDigitalMaps cjDigitalMaps);

    /**
     * 删除数字地图
     *
     * @param id 数字地图ID
     * @return 操作结果
     */
    boolean deleteCjDigitalMaps(Long id);

    /**
     * 批量删除数字地图
     *
     * @param ids 数字地图ID数组
     * @return 操作结果
     */
    boolean batchDeleteCjDigitalMaps(Long[] ids);

    /**
     * 更新发布状态
     *
     * @param id            数字地图ID
     * @param publishStatus 发布状态
     * @return 操作结果
     */
    boolean updatePublishStatus(Long id, Integer publishStatus);
}
