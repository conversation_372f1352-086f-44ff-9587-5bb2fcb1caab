package com.das.business.changjiang.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.entity.CjRuinsUnearthedMaterial;
import com.das.business.changjiang.entity.responseEntity.TypeAndCount;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-5-23
 */
public interface CjRuinsUnearthedMaterialMapper extends BaseMapper<CjRuinsUnearthedMaterial> {

    /**
     * 分页查询
     */
    IPage<CjRuinsUnearthedMaterial> getCjRuinsMaterialList(@Param(value = "page") Page<CjRuinsUnearthedMaterial> page,
                                                           @Param(value = "params") Map<String, Object> params,
                                                           @Param(value = "sort") String sort,
                                                           @Param(value = "order") String order);

    /**
     * 根据id查询考古遗址发掘资料分类
     *
     * @param ruinsId
     * @return
     */
    List<TypeAndCount> getTypeAndCount(Long ruinsId);

    /**
     * 获取挖掘资料所有名称
     *
     * @param ruinsId
     * @return
     */
    List<String> getMaterialNames(Long ruinsId);

    /**
     * 获取挖掘资料类型所有名称
     *
     * @param ruinsId
     * @return
     */
    List<String> getMaterialTypes(Long ruinsId);

}
