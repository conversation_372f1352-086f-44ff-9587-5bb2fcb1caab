package com.das.business.changjiang.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.das.annotation.RequestSecurity;
import com.das.business.changjiang.controller.vo.CjRuinsInfoImportVO;
import com.das.business.changjiang.entity.CjRuinsInfo;
import com.das.business.changjiang.service.ICjRuinsInfoNewService;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.das.business.changjiang.service.CjRuinsInfoService;
import com.das.business.changjiang.controller.vo.CjRuinsInfoVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 遗址管理控制器
 *
 * @Description
 * <AUTHOR>
 * @Date 2025-01-02
 */
@RestController()
@RequestMapping("/cjRuinsInfo")
@RequiredArgsConstructor
@Slf4j
public class CjRuinsInfoController {

    private final CjRuinsInfoService cjRuinsInfoService;
    private final ICjRuinsInfoNewService ruinsInfoNewService;

    /**
     * 分页查询
     */
    @RequestSecurity(value = "/getCjRuinsInfoList", method = RequestMethod.POST)
    public ResultMsg getCjRuinsInfoList(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(cjRuinsInfoService.findByPage(pageEntity));
    }

    /**
     * 根据ID查询详情
     *
     * @param id
     * @return
     */
    @PostMapping(value = "/getById")
    public ResultMsg getById(@RequestBody Long id) {
        return new ResultMsg(cjRuinsInfoService.getById(id));
    }

    /**
     * 新增
     *
     * @param cjRuinsInfoVO
     * @return
     */
    @PostMapping(value = "/save")
    public ResultMsg save(@RequestBody CjRuinsInfoVO cjRuinsInfoVO) {
        return new ResultMsg(cjRuinsInfoService.add(cjRuinsInfoVO));
    }

    /**
     * 修改
     *
     * @param cjRuinsInfoVO
     * @return
     */
    @PostMapping(value = "/updateById")
    public ResultMsg updateById(@RequestBody CjRuinsInfoVO cjRuinsInfoVO) {
        return new ResultMsg(cjRuinsInfoService.updateById(cjRuinsInfoVO));
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @PostMapping(value = "/removeByIds")
    public ResultMsg removeByIds(@RequestBody List<Long> ids) {
        return new ResultMsg(cjRuinsInfoService.removeByIds(ids));
    }

    /**
     * 根据树查询所有的集合数据-调用新版本数据结构接口
     */
    @RequestMapping(value = "/getRuinsDetail", method = RequestMethod.POST)
    public ResultMsg getRuinsDetailNew(@RequestBody Map<String, String> params) {
        return new ResultMsg(ruinsInfoNewService.getRuinsDetail(params));
    }

    /**
     * 根据id查询考古遗址信息
     */
    @RequestSecurity(value = "/getCjRuinsInfoById", method = RequestMethod.POST)
    public ResultMsg getCjRuinsInfoById(@RequestParam Long id) {
        return new ResultMsg(ruinsInfoNewService.getRuinsDetailById(id));
    }

    /**
     * 后台管理-导入
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/import")
    public ResultMsg importExcel(MultipartFile file) {
        try (InputStream in = file.getInputStream()) {
            final List<CjRuinsInfo> buffer = new ArrayList<>();
            EasyExcel.read(in, CjRuinsInfoImportVO.class,
                            new AnalysisEventListener<CjRuinsInfoImportVO>() {
                                @Override
                                public void invoke(CjRuinsInfoImportVO d, AnalysisContext context) {
                                    CjRuinsInfo e = new CjRuinsInfo();
                                    e.setName(d.getName());
                                    e.setAddress(d.getAddress());
                                    e.setLongitude(d.getLongitude());
                                    e.setLatitude(d.getLatitude());
                                    e.setDiscoverTombs(d.getDiscoverTombs());
                                    e.setProveTombs(d.getProveTombs());
                                    e.setDiscoverTime(d.getDiscoverTime());
                                    e.setAge(d.getAge());
                                    e.setDiscoverLog(d.getDiscoverLog());
                                    e.setTombsLayout(d.getTombsLayout());
                                    e.setEnvironment(d.getEnvironment());
                                    e.setSurroundingRuins(d.getSurroundingRuins());
                                    e.setReport(d.getReport());
                                    e.setOtherInfo(d.getOtherInfo());
                                    buffer.add(e);
                                }

                                @Override
                                public void doAfterAllAnalysed(AnalysisContext context) {
                                }
                            })
                    .sheet()          // 默认第一张表；如需指定 sheet 名/序号可改 .sheet("Sheet1") / .sheet(0)
                    .headRowNumber(1) // 表头行数，按你的模板调整
                    .doRead();
            cjRuinsInfoService.saveBatch(buffer);
            return new ResultMsg(true);
        } catch (Exception e) {
            log.error("导入失败", e);
            return new ResultMsg(false);
        }
    }


    /**
     * 后台管理-导出
     *
     * @param name
     * @param response
     * @throws IOException
     */
    @GetMapping("/export")
    public void exportData(@RequestParam(required = false) String name,
                           HttpServletResponse response) throws IOException {
        // 1) 查询数据（示例：按名称模糊）
        PageEntity pageEntity = new PageEntity();
        pageEntity.setCurrent(1);
        pageEntity.setSize(1000000);
        Map<String, Object> param = new HashMap<>();
        param.put("name", name);
        pageEntity.setParams(param);
        IPage<CjRuinsInfo> page = cjRuinsInfoService.findByPage(pageEntity);
        List<CjRuinsInfo> list = page.getRecords();

        // 2) 实体 -> VO（含 @ExcelProperty 的类）
        List<CjRuinsInfoImportVO> rows = list.stream().map(e -> {
            CjRuinsInfoImportVO vo = new CjRuinsInfoImportVO();
            vo.setName(e.getName());
            vo.setAddress(e.getAddress());
            vo.setLongitude(e.getLongitude());
            vo.setLatitude(e.getLatitude());
            vo.setDiscoverTombs(e.getDiscoverTombs());
            vo.setProveTombs(e.getProveTombs());
            vo.setDiscoverTime(e.getDiscoverTime());
            vo.setAge(e.getAge());
            vo.setDiscoverLog(e.getDiscoverLog());
            vo.setTombsLayout(e.getTombsLayout());
            vo.setEnvironment(e.getEnvironment());
            vo.setSurroundingRuins(e.getSurroundingRuins());
            vo.setReport(e.getReport());
            vo.setOtherInfo(e.getOtherInfo());
            return vo;
        }).collect(Collectors.toList());

        // 3) 响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("遗址管理导出", StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 4) 写出
        EasyExcel.write(response.getOutputStream(), CjRuinsInfoImportVO.class)
                .sheet("遗址管理")
                .doWrite(rows);
    }
}
