package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 数字地图表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@Data
public class CjDigitalMaps extends Model<CjDigitalMaps> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 地图名称
     */
    private String mapName;

    /**
     * 地图类型及分组,多个用英文逗号隔开
     */
    private String mapTypeGroup;

    /**
     * 地图文件路径
     */
    private String mapFile;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 地图文字说明
     */
    private String description;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 发布状态：0未发布，1已发布
     */
    private Integer publishStatus;

    // 扩展字段，不存储到数据库
    @JsonIgnore
    @TableField(exist = false)
    private String startTime;

    @JsonIgnore
    @TableField(exist = false)
    private String endTime;

    @TableField(exist = false)
    private String publishStatusName;
}
