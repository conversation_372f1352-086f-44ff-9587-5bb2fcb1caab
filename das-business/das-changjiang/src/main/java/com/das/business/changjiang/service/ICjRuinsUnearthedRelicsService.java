package com.das.business.changjiang.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.business.changjiang.entity.CjRuinsUnearthedRelics;
import com.das.business.changjiang.entity.responseEntity.TypeAndCount;
import com.das.utils.response.PageEntity;

import java.util.List;

/**
 * <p>
 * 服务类接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-5-23
 */
public interface ICjRuinsUnearthedRelicsService extends IService<CjRuinsUnearthedRelics> {

    /**
     * 分页查询
     */
    IPage<CjRuinsUnearthedRelics> getCjRuinsRelicsList(PageEntity pageEntity);

    /**
     * 新增考古遗址发掘资料
     */
    boolean saveCjRuinsRelics(CjRuinsUnearthedRelics cjRuinsRelics);

    /**
     * 修改考古遗址发掘资料
     */
    boolean updateCjRuinsRelics(CjRuinsUnearthedRelics cjRuinsRelics);

    /**
     * 删除考古遗址发掘资料
     */
    boolean delCjRuinsRelics(List<Long> ids);

    /**
     * 根据id查询考古遗址发掘资料
     */
    CjRuinsUnearthedRelics getCjRuinsRelicsById(Long id);

    /**
     * 根据id查询考古遗址出土文物分类
     *
     * @param ruinsId
     * @return
     */
    List<TypeAndCount> getTypeAndCount(Long ruinsId);

}
