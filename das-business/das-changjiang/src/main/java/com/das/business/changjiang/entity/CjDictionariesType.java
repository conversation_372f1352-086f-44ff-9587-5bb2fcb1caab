package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 辞典类型实体
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-05
 */
@Data
public class CjDictionariesType extends Model<CjDictionariesType> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 标题
     */
    private String name;

    /**
     * 父类
     */
    private String parentId;


    /**
     * 是否为父节点（0否1是）
     */
    private Integer isLeaf;

    /**
     * 是否展开（0否1是）
     */
    private Integer isShow;

    /**
     * 子节点
     */
    @TableField(exist = false)
    private List<CjDictionariesType> children;


}
