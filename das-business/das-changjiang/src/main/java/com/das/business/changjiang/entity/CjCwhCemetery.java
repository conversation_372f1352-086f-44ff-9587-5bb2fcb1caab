package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 墓葬信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-02
 */
@Data
public class CjCwhCemetery extends Model<CjCwhCemetery> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 墓葬名称
     */
    private String name;

    /**
     * 年代
     */
    private String age;

    /**
     * 转换后的年代
     */
    @TableField(exist = false)
    private String ageName;

    /**
     * 方向
     */
    private Integer direction;

    /**
     * 面积
     */
    private Double area;

    /**
     * 棺椁重数
     */
    private Integer coffinChongchu;

    /**
     * 棺椁数量
     */
    private Integer coffinNumber;

    /**
     * 台阶
     */
    private Integer steps;

    /**
     * 0未知 1男 2女
     */
    private Integer sex;

    /**
     * 铜礼乐器
     */
    private String musicalInstrument;

    /**
     * 仿铜陶器
     */
    private String pottery;

    /**
     * 日用陶器
     */
    private String usePottery;

    /**
     * 车马器
     */
    private String chariotHorseImplement;

    /**
     * 兵器
     */
    private String weaponry;

    /**
     * 漆木器
     */
    private String lacquerWoodWare;

    /**
     * 玉器
     */
    private String jade;

    /**
     * 其他
     */
    private String other;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

}
