package com.das.business.changjiang.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.entity.CjDigitalMaps;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * 数字地图表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
public interface CjDigitalMapsMapper extends BaseMapper<CjDigitalMaps> {

    /**
     * 分页查询数字地图列表
     *
     * @param page   分页参数
     * @param params 查询参数
     * @param sort   排序字段
     * @param order  排序方式
     * @return 分页结果
     */
    IPage<CjDigitalMaps> getCjDigitalMapsList(@Param(value = "page") Page<CjDigitalMaps> page,
                                              @Param(value = "params") Map<String, Object> params,
                                              @Param(value = "sort") String sort, 
                                              @Param(value = "order") String order);

    /**
     * 根据ID查询数字地图详情
     *
     * @param id 数字地图ID
     * @return 数字地图详情
     */
    CjDigitalMaps getCjDigitalMapsById(@Param(value = "id") Long id);
}
