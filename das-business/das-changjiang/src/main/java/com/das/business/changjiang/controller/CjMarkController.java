package com.das.business.changjiang.controller;


import com.das.annotation.RequestSecurity;
import com.das.business.changjiang.entity.CjMark;
import com.das.business.changjiang.service.impl.CjMarkServiceImpl;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
@Api(value = "长江GIS标注接口", description = "长江GIS标注接口")
@RestController
@RequestMapping("/cjMark")
public class CjMarkController {

    @Resource
    private CjMarkServiceImpl cjMarkService;
    /**
     * 分页查询
     */
    @ApiOperation(value = "分页查询标记信息")
    @RequestSecurity(value = "/getCjMarkList",method = RequestMethod.POST)
    public ResultMsg getCjMarkList(@RequestBody PageEntity pageEntity){
        return new ResultMsg(cjMarkService.getCjMarkList(pageEntity));
    }

    /**
     * 新增or修改标注
     */
    @ApiOperation(value = "新增或修改标注")
    @RequestSecurity(value = "/saveCjMark",method = RequestMethod.POST)
    public ResultMsg saveCjMark(@RequestBody CjMark cjMark){
        return new ResultMsg(cjMarkService.saveCjMark(cjMark));
    }

    /**
     * 删除标注信息
     */
    @ApiOperation(value = "删除标注信息")
    @RequestSecurity(value = "/delCjMark",method = RequestMethod.POST)
    public ResultMsg delCjMark(@RequestParam Long id){
        return new ResultMsg(cjMarkService.delCjMark(id));
    }

    /**
     * 根据id查询标注信息
     */
    @ApiOperation(value = "根据id查询标注信息")
    @RequestSecurity(value = "/getCjMarkById",method = RequestMethod.POST)
    public ResultMsg getCjMarkById(@RequestParam Long id){
        return new ResultMsg(cjMarkService.getCjMarkById(id));
    }

}
