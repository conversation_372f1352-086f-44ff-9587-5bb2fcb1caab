package com.das.business.changjiang.controller;

import com.das.annotation.RequestSecurity;
import com.das.business.changjiang.entity.CjProblemFeedback;
import com.das.business.changjiang.service.ICjProblemFeedbackService;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 问题反馈
 */
@RestController
@RequestMapping("/cjProblemFeedback")
public class CjProblemFeedbackController {

    @Resource
    private ICjProblemFeedbackService cjProblemFeedbackService;

    /**
     * 分页查询问题反馈列表
     */
    @RequestSecurity(value = "/getCjProblemFeedbackList", method = RequestMethod.POST)
    public ResultMsg getCjProblemFeedbackList(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(cjProblemFeedbackService.getCjProblemFeedbackList(pageEntity));
    }

    /**
     * 根据ID查询问题反馈详情
     */
    @RequestSecurity(value = "/getCjProblemFeedbackById", method = RequestMethod.GET)
    public ResultMsg getCjProblemFeedbackById(@RequestParam Long id) {
        return new ResultMsg(cjProblemFeedbackService.getCjProblemFeedbackById(id));
    }

    /**
     * 新增或修改问题反馈
     */
    @RequestSecurity(value = "/saveCjProblemFeedback", method = RequestMethod.POST)
    public ResultMsg saveCjProblemFeedback(@RequestBody CjProblemFeedback cjProblemFeedback) {
        return new ResultMsg(cjProblemFeedbackService.saveCjProblemFeedback(cjProblemFeedback));
    }

    /**
     * 删除问题反馈
     */
    @RequestSecurity(value = "/deleteCjProblemFeedback", method = RequestMethod.DELETE)
    public ResultMsg deleteCjProblemFeedback(@RequestParam Long id) {
        return new ResultMsg(cjProblemFeedbackService.deleteCjProblemFeedback(id));
    }

    /**
     * 批量删除问题反馈
     */
    @RequestSecurity(value = "/batchDeleteCjProblemFeedback", method = RequestMethod.DELETE)
    public ResultMsg batchDeleteCjProblemFeedback(@RequestBody Long[] ids) {
        return new ResultMsg(cjProblemFeedbackService.batchDeleteCjProblemFeedback(ids));
    }

    /**
     * 回复问题反馈
     */
    @RequestSecurity(value = "/replyProblemFeedback", method = RequestMethod.PUT)
    public ResultMsg replyProblemFeedback(@RequestParam Long id, @RequestParam String replyContent) {
        return new ResultMsg(cjProblemFeedbackService.replyProblemFeedback(id, replyContent));
    }

    /**
     * 更新回复状态
     */
    @RequestSecurity(value = "/updateBackStatus", method = RequestMethod.PUT)
    public ResultMsg updateBackStatus(@RequestParam Long id, @RequestParam Integer backStatus) {
        return new ResultMsg(cjProblemFeedbackService.updateBackStatus(id, backStatus));
    }
}
