package com.das.business.changjiang.controller;

import com.das.business.changjiang.entity.CjProblemFeedback;
import com.das.business.changjiang.service.ICjProblemFeedbackService;
import com.das.common.annotation.DasController;
import com.das.common.annotation.RequestSecurity;
import com.das.system.entity.PageEntity;
import com.das.system.entity.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 问题反馈表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@Api(value = "问题反馈接口", description = "问题反馈接口")
@DasController("/cjProblemFeedback")
public class CjProblemFeedbackController {

    @Resource
    private ICjProblemFeedbackService cjProblemFeedbackService;

    /**
     * 分页查询问题反馈列表
     */
    @ApiOperation(value = "分页查询问题反馈列表")
    @RequestSecurity(value = "/getCjProblemFeedbackList", method = RequestMethod.POST)
    public ResultMsg getCjProblemFeedbackList(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(cjProblemFeedbackService.getCjProblemFeedbackList(pageEntity));
    }

    /**
     * 根据ID查询问题反馈详情
     */
    @ApiOperation(value = "根据ID查询问题反馈详情")
    @RequestSecurity(value = "/getCjProblemFeedbackById", method = RequestMethod.GET)
    public ResultMsg getCjProblemFeedbackById(@RequestParam Long id) {
        return new ResultMsg(cjProblemFeedbackService.getCjProblemFeedbackById(id));
    }

    /**
     * 新增或修改问题反馈
     */
    @ApiOperation(value = "新增或修改问题反馈")
    @RequestSecurity(value = "/saveCjProblemFeedback", method = RequestMethod.POST)
    public ResultMsg saveCjProblemFeedback(@RequestBody CjProblemFeedback cjProblemFeedback) {
        boolean result = cjProblemFeedbackService.saveCjProblemFeedback(cjProblemFeedback);
        if (result) {
            return new ResultMsg("操作成功");
        } else {
            return new ResultMsg(500, "操作失败");
        }
    }

    /**
     * 删除问题反馈
     */
    @ApiOperation(value = "删除问题反馈")
    @RequestSecurity(value = "/deleteCjProblemFeedback", method = RequestMethod.DELETE)
    public ResultMsg deleteCjProblemFeedback(@RequestParam Long id) {
        boolean result = cjProblemFeedbackService.deleteCjProblemFeedback(id);
        if (result) {
            return new ResultMsg("删除成功");
        } else {
            return new ResultMsg(500, "删除失败");
        }
    }

    /**
     * 批量删除问题反馈
     */
    @ApiOperation(value = "批量删除问题反馈")
    @RequestSecurity(value = "/batchDeleteCjProblemFeedback", method = RequestMethod.DELETE)
    public ResultMsg batchDeleteCjProblemFeedback(@RequestBody Long[] ids) {
        boolean result = cjProblemFeedbackService.batchDeleteCjProblemFeedback(ids);
        if (result) {
            return new ResultMsg("批量删除成功");
        } else {
            return new ResultMsg(500, "批量删除失败");
        }
    }

    /**
     * 回复问题反馈
     */
    @ApiOperation(value = "回复问题反馈")
    @RequestSecurity(value = "/replyProblemFeedback", method = RequestMethod.PUT)
    public ResultMsg replyProblemFeedback(@RequestParam Long id, @RequestParam String replyContent) {
        boolean result = cjProblemFeedbackService.replyProblemFeedback(id, replyContent);
        if (result) {
            return new ResultMsg("回复成功");
        } else {
            return new ResultMsg(500, "回复失败");
        }
    }

    /**
     * 更新回复状态
     */
    @ApiOperation(value = "更新回复状态")
    @RequestSecurity(value = "/updateBackStatus", method = RequestMethod.PUT)
    public ResultMsg updateBackStatus(@RequestParam Long id, @RequestParam Integer backStatus) {
        boolean result = cjProblemFeedbackService.updateBackStatus(id, backStatus);
        if (result) {
            return new ResultMsg("状态更新成功");
        } else {
            return new ResultMsg(500, "状态更新失败");
        }
    }
}
