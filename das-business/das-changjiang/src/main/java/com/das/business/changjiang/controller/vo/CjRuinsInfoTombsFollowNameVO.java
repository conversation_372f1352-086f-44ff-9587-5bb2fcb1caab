package com.das.business.changjiang.controller.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 考古遗址基础信息表
 *
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@AllArgsConstructor
@NoArgsConstructor
public class CjRuinsInfoTombsFollowNameVO implements Serializable {

    private String id;
    private String name;
    private Integer type;
    private Integer number;

}