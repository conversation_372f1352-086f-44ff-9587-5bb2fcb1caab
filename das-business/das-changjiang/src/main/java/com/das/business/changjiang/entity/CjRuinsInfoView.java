package com.das.business.changjiang.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @Title: 考古遗址基础信息表
 * @Description:
 * @date 2022/01/04 10:27
 */
@Data
@ApiModel(value = "考古遗址基础信息表")
public class CjRuinsInfoView {

    @ApiModelProperty(value = "文物名称")
    @ExcelProperty(value = {"文物名称"}, index = 0)
    private String name;

    @ApiModelProperty(value = "类别")
    @ExcelProperty(value = {"类别"}, index = 1)
    private String category;

    @ApiModelProperty(value = "市")
    @ExcelProperty(value = {"市"}, index = 2)
    private String city;

    @ApiModelProperty(value = "县")
    @ExcelProperty(value = {"县"}, index = 3)
    private String county;

    @ApiModelProperty(value = "地址")
    @ExcelProperty(value = {"地址"}, index = 4)
    private String address;

    @ApiModelProperty(value = "经度")
    @ExcelProperty(value = {"经度"}, index = 5)
    private double longitude;

    @ApiModelProperty(value = "纬度")
    @ExcelProperty(value = {"纬度"}, index = 6)
    private double latitude;

    @ApiModelProperty(value = "海拔高度")
    @ExcelProperty(value = {"海拔高度"}, index = 7)
    private double altitude;

    @ApiModelProperty(value = "级别")
    @ExcelProperty(value = {"级别"}, index = 8)
    private String level;

    @ApiModelProperty(value = "面积")
    @ExcelProperty(value = {"面积"}, index = 9)
    private double acreage;

    @ApiModelProperty(value = "年代")
    @ExcelProperty(value = {"年代"}, index = 10)
    private String age;

    @ApiModelProperty(value = "使用者")
    @ExcelProperty(value = {"使用者"}, index = 11)
    private String owner;

    @ApiModelProperty(value = "隶属")
    @ExcelProperty(value = {"隶属"}, index = 12)
    private String subjection;

    @ApiModelProperty(value = "单体描述")
    @ExcelProperty(value = {"单体描述"}, index = 13)
    private String monomerDescription;

    @ApiModelProperty(value = "简介")
    @ExcelProperty(value = {"简介"}, index = 14)
    private String description;

    @ApiModelProperty(value = "保存状况")
    @ExcelProperty(value = {"保存状况"}, index = 15)
    private String preserveStatus;

    @ApiModelProperty(value = "状态描述")
    @ExcelProperty(value = {"状态描述"}, index = 16)
    private String statusDescription;

    @ApiModelProperty(value = "自然损毁原因")
    @ExcelProperty(value = {"自然损毁原因"}, index = 17)
    private String naturalReason;

    @ApiModelProperty(value = "人为损毁原因")
    @ExcelProperty(value = {"人为损毁原因"}, index = 18)
    private String artificialReason;

    @ApiModelProperty(value = "损毁原因描述")
    @ExcelProperty(value = {"损毁原因描述"}, index = 19)
    private String reasonDescription;

    @ApiModelProperty(value = "环境")
    @ExcelProperty(value = {"环境"}, index = 20)
    private String environment;

    @ApiModelProperty(value = "人文")
    @ExcelProperty(value = {"人文"}, index = 21)
    private String humanity;
}
