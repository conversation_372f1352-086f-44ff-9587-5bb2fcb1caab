package com.das.business.changjiang.mapper;

import com.das.business.changjiang.controller.vo.CjRuinsInfoMapVO;
import com.das.business.changjiang.entity.CjRuinsInfoNew;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 考古遗址基础信息表-20241225调整业务后新表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public interface CjRuinsInfoNewMapper extends BaseMapper<CjRuinsInfoNew> {

    /**
     * 获取考古遗址gps
     *
     * @param category
     * @param agesList
     * @return
     */
    List<CjRuinsInfoMapVO> getRuinsDetail(@Param(value = "category") String category, @Param(value = "agesList") List<String> agesList);

}
