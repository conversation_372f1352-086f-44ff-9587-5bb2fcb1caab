package com.das.business.changjiang.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.business.changjiang.controller.vo.CjRuinsInfoVO;
import com.das.utils.response.PageEntity;
import com.das.business.changjiang.entity.CjRuinsInfo;

/**
 * 
 * @Description
 * <AUTHOR>
 * @Date 2025-01-02
 */
public interface CjRuinsInfoService extends IService<CjRuinsInfo> {

	IPage<CjRuinsInfo> findByPage(PageEntity pageEntity);

	boolean add(CjRuinsInfoVO cjRuinsInfoVO);

	boolean updateById(CjRuinsInfoVO cjRuinsInfoVO);

}
