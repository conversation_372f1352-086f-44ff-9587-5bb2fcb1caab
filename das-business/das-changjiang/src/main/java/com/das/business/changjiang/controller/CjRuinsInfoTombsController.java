package com.das.business.changjiang.controller;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.das.annotation.RequestSecurity;
import com.das.business.changjiang.controller.vo.CjRuinsInfoImportVO;
import com.das.business.changjiang.controller.vo.CjRuinsInfoTombsImportVO;
import com.das.business.changjiang.controller.vo.CjRuinsInfoTombsVO;
import com.das.business.changjiang.controller.vo.CjRuinsInfoVO;
import com.das.business.changjiang.entity.CjRuinsInfo;
import com.das.business.changjiang.entity.CjRuinsInfoTombs;
import com.das.business.changjiang.service.ICjRuinsInfoTombsService;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 墓葬管理
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@RestController
@RequestMapping("/cjRuinsInfoTombs")
@Slf4j
public class CjRuinsInfoTombsController {

    @Resource
    ICjRuinsInfoTombsService ruinsInfoTombsService;

    /**
     * 前台页面-根据id查询墓地基本信息
     */
    @RequestSecurity(value = "/getCjRuinsInfoTombsDetailById", method = RequestMethod.POST)
    public ResultMsg getCjRuinsInfoById(@RequestParam Long id) {
        return new ResultMsg(ruinsInfoTombsService.getDetailById(id));
    }


    /**
     * 后台管理-分页查询
     *
     * @param pageEntity
     * @return
     */
    @PostMapping(value = "/findByPage")
    public ResultMsg findByPage(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(ruinsInfoTombsService.findByPage(pageEntity));
    }

    /**
     * 后台管理-根据ID查询详情
     *
     * @param id
     * @return
     */
    @PostMapping(value = "/getById")
    public ResultMsg getById(@RequestBody Long id) {
        return new ResultMsg(ruinsInfoTombsService.getById(id));
    }

    /**
     * 后台管理-新增
     *
     * @param cjRuinsInfoTombsVO
     * @return
     */
    @PostMapping(value = "/save")
    public ResultMsg save(@RequestBody CjRuinsInfoTombsVO cjRuinsInfoTombsVO) {
        return new ResultMsg(ruinsInfoTombsService.add(cjRuinsInfoTombsVO));
    }

    /**
     * 后台管理-修改
     *
     * @param cjRuinsInfoTombsVO
     * @return
     */
    @PostMapping(value = "/updateById")
    public ResultMsg updateById(@RequestBody CjRuinsInfoTombsVO cjRuinsInfoTombsVO) {
        return new ResultMsg(ruinsInfoTombsService.update(cjRuinsInfoTombsVO));
    }

    /**
     * 后台管理-删除
     *
     * @param ids
     * @return
     */
    @PostMapping(value = "/removeByIds")
    public ResultMsg removeByIds(@RequestBody List<Long> ids) {
        return new ResultMsg(ruinsInfoTombsService.removeByIds(ids));
    }


    /**
     * 后台管理-导入
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/import")
    public ResultMsg importExcel(MultipartFile file, @RequestParam("ruinsId") Long ruinsId) {
        log.info("开始导入墓葬管理数据...");
        if (ruinsId == null || ruinsId <= 0) {
            return new ResultMsg("请选择所属遗址！");
        }
        try (InputStream in = file.getInputStream()) {
            final List<CjRuinsInfoTombs> buffer = new ArrayList<>();
            EasyExcel.read(in, CjRuinsInfoTombsImportVO.class,
                            new AnalysisEventListener<CjRuinsInfoTombsImportVO>() {
                                @Override
                                public void invoke(CjRuinsInfoTombsImportVO d, AnalysisContext context) {
                                    CjRuinsInfoTombs e = new CjRuinsInfoTombs();
                                    e.setRuinsId(ruinsId);
                                    e.setName(d.getName());
                                    e.setDynasty(d.getDynasty());
                                    e.setStealInfo(d.getStealInfo());
                                    e.setFiefHave(d.getFiefHave());
                                    e.setFiefShape(d.getFiefShape());
                                    e.setFiefBottomDiameter(d.getFiefBottomDiameter());
                                    e.setFiefHeight(d.getFiefHeight());
                                    e.setHoleShape(d.getHoleShape());
                                    e.setHoleDirection(d.getHoleDirection());
                                    e.setHoleTopLong(d.getHoleTopLong());
                                    e.setHoleTopWidth(d.getHoleTopWidth());
                                    e.setHoleBottomLength(d.getHoleBottomLength());
                                    e.setHoleBottomWidth(d.getHoleBottomWidth());
                                    e.setHoleBottomHeight(d.getHoleBottomHeight());
                                    e.setHoleStep(d.getHoleStep());
                                    e.setHolePassageHave(d.getHolePassageHave());
                                    e.setHolePassageNumber(d.getHolePassageNumber());
                                    e.setHolePassageTotalLength(d.getHolePassageTotalLength());
                                    e.setHolePassageTopLength(d.getHolePassageTopLength());
                                    e.setHolePassageTopLengthLength(d.getHolePassageTopLengthLength());
                                    e.setHolePassageTopShortLength(d.getHolePassageTopShortLength());
                                    e.setHolePassageBottomLength(d.getHolePassageBottomLength());
                                    e.setHolePassageAngle(d.getHolePassageAngle());
                                    e.setFillSoil(d.getFillSoil());
                                    e.setOther(d.getOther());
                                    e.setCoffinNumber(d.getCoffinNumber());
                                    e.setDivideNumber(d.getDivideNumber());
                                    e.setWay(d.getWay());
                                    e.setSex(d.getSex());
                                    e.setAge(d.getAge());
                                    e.setRemark(d.getRemark());
                                    buffer.add(e);
                                }

                                @Override
                                public void doAfterAllAnalysed(AnalysisContext context) {
                                }
                            })
                    .sheet()          // 默认第一张表；如需指定 sheet 名/序号可改 .sheet("Sheet1") / .sheet(0)
                    .headRowNumber(1) // 表头行数，按你的模板调整
                    .doRead();
            ruinsInfoTombsService.saveBatch(buffer);
            return new ResultMsg(true);
        } catch (Exception e) {
            log.error("导入失败", e);
            return new ResultMsg(false);
        }
    }


    /**
     * 后台管理-导出
     *
     * @param name
     * @param response
     * @throws IOException
     */
    @GetMapping("/export")
    public void exportData(@RequestParam(value = "name", required = false) String name,
                           @RequestParam(value = "ruinsId", required = true) Long ruinsId,
                           HttpServletResponse response) throws IOException {
        log.info("开始导出墓葬管理数据...");
        if (ruinsId == null || ruinsId <= 0) {
            return;
        }
        // 1) 查询数据（示例：按名称模糊）
        PageEntity pageEntity = new PageEntity();
        pageEntity.setCurrent(1);
        pageEntity.setSize(1000000);
        Map<String, Object> param = new HashMap<>();
        param.put("name", name);
        param.put("ruinsId", ruinsId);
        pageEntity.setParams(param);
        IPage<CjRuinsInfoTombs> page = ruinsInfoTombsService.findByPage(pageEntity);
        List<CjRuinsInfoTombs> list = page.getRecords();
        // 2) 实体 -> VO（含 @ExcelProperty 的类）
        List<CjRuinsInfoTombsImportVO> rows = list.stream().map(d -> {
            CjRuinsInfoTombsImportVO e = new CjRuinsInfoTombsImportVO();
            e.setName(d.getName());
            e.setDynasty(d.getDynasty());
            e.setStealInfo(d.getStealInfo());
            e.setFiefHave(d.getFiefHave());
            e.setFiefShape(d.getFiefShape());
            e.setFiefBottomDiameter(d.getFiefBottomDiameter());
            e.setFiefHeight(d.getFiefHeight());
            e.setHoleShape(d.getHoleShape());
            e.setHoleDirection(d.getHoleDirection());
            e.setHoleTopLong(d.getHoleTopLong());
            e.setHoleTopWidth(d.getHoleTopWidth());
            e.setHoleBottomLength(d.getHoleBottomLength());
            e.setHoleBottomWidth(d.getHoleBottomWidth());
            e.setHoleBottomHeight(d.getHoleBottomHeight());
            e.setHoleStep(d.getHoleStep());
            e.setHolePassageHave(d.getHolePassageHave());
            e.setHolePassageNumber(d.getHolePassageNumber());
            e.setHolePassageTotalLength(d.getHolePassageTotalLength());
            e.setHolePassageTopLength(d.getHolePassageTopLength());
            e.setHolePassageTopLengthLength(d.getHolePassageTopLengthLength());
            e.setHolePassageTopShortLength(d.getHolePassageTopShortLength());
            e.setHolePassageBottomLength(d.getHolePassageBottomLength());
            e.setHolePassageAngle(d.getHolePassageAngle());
            e.setFillSoil(d.getFillSoil());
            e.setOther(d.getOther());
            e.setCoffinNumber(d.getCoffinNumber());
            e.setDivideNumber(d.getDivideNumber());
            e.setWay(d.getWay());
            e.setSex(d.getSex());
            e.setAge(d.getAge());
            e.setRemark(d.getRemark());
            return e;
        }).collect(Collectors.toList());

        // 3) 响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("遗址管理墓葬导出", StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 4) 写出
        EasyExcel.write(response.getOutputStream(), CjRuinsInfoTombsImportVO.class)
                .sheet("遗址管理墓葬数据导出")
                .doWrite(rows);
    }

    private CjRuinsInfoTombs convertCjRuinsInfoTombs(CjRuinsInfoTombsVO cjRuinsInfoTombsVO) {
        CjRuinsInfoTombs cjRuinsInfoTombs = new CjRuinsInfoTombs();
        cjRuinsInfoTombs.setId(cjRuinsInfoTombsVO.getId());
        cjRuinsInfoTombs.setRuinsId(cjRuinsInfoTombsVO.getRuinsId());
        cjRuinsInfoTombs.setName(cjRuinsInfoTombsVO.getName());
        cjRuinsInfoTombs.setDynasty(cjRuinsInfoTombsVO.getDynasty());
        cjRuinsInfoTombs.setStealInfo(cjRuinsInfoTombsVO.getStealInfo());
        cjRuinsInfoTombs.setFiefHave(cjRuinsInfoTombsVO.getFiefHave());
        cjRuinsInfoTombs.setFiefShape(cjRuinsInfoTombsVO.getFiefShape());
        cjRuinsInfoTombs.setFiefBottomDiameter(cjRuinsInfoTombsVO.getFiefBottomDiameter());
        cjRuinsInfoTombs.setFiefHeight(cjRuinsInfoTombsVO.getFiefHeight());
        cjRuinsInfoTombs.setHoleShape(cjRuinsInfoTombsVO.getHoleShape());
        cjRuinsInfoTombs.setHoleDirection(cjRuinsInfoTombsVO.getHoleDirection());
        cjRuinsInfoTombs.setHoleTopLong(cjRuinsInfoTombsVO.getHoleTopLong());
        cjRuinsInfoTombs.setHoleTopWidth(cjRuinsInfoTombsVO.getHoleTopWidth());
        cjRuinsInfoTombs.setHoleBottomLength(cjRuinsInfoTombsVO.getHoleBottomLength());
        cjRuinsInfoTombs.setHoleBottomWidth(cjRuinsInfoTombsVO.getHoleBottomWidth());
        cjRuinsInfoTombs.setHoleBottomHeight(cjRuinsInfoTombsVO.getHoleBottomHeight());
        cjRuinsInfoTombs.setHoleStep(cjRuinsInfoTombsVO.getHoleStep());
        cjRuinsInfoTombs.setHolePassageHave(cjRuinsInfoTombsVO.getHolePassageHave());
        cjRuinsInfoTombs.setHolePassageNumber(cjRuinsInfoTombsVO.getHolePassageNumber());
        cjRuinsInfoTombs.setHolePassageTotalLength(cjRuinsInfoTombsVO.getHolePassageTotalLength());
        cjRuinsInfoTombs.setHolePassageTopLength(cjRuinsInfoTombsVO.getHolePassageTopLength());
        cjRuinsInfoTombs.setHolePassageTopLengthLength(cjRuinsInfoTombsVO.getHolePassageTopLengthLength());
        cjRuinsInfoTombs.setHolePassageTopShortLength(cjRuinsInfoTombsVO.getHolePassageTopShortLength());
        cjRuinsInfoTombs.setHolePassageBottomLength(cjRuinsInfoTombsVO.getHolePassageBottomLength());
        cjRuinsInfoTombs.setHolePassageAngle(cjRuinsInfoTombsVO.getHolePassageAngle());
        cjRuinsInfoTombs.setFillSoil(cjRuinsInfoTombsVO.getFillSoil());
        cjRuinsInfoTombs.setOther(cjRuinsInfoTombsVO.getOther());
        cjRuinsInfoTombs.setCoffinNumber(cjRuinsInfoTombsVO.getCoffinNumber());
        cjRuinsInfoTombs.setDivideNumber(cjRuinsInfoTombsVO.getDivideNumber());
        cjRuinsInfoTombs.setWay(cjRuinsInfoTombsVO.getWay());
        cjRuinsInfoTombs.setSex(cjRuinsInfoTombsVO.getSex());
        cjRuinsInfoTombs.setAge(cjRuinsInfoTombsVO.getAge());
        cjRuinsInfoTombs.setRemark(cjRuinsInfoTombsVO.getRemark());
        return cjRuinsInfoTombs;
    }

}
