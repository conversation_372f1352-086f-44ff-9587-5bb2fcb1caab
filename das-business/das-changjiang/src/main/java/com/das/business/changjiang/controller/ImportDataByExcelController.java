package com.das.business.changjiang.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.das.business.changjiang.entity.CjRuinsInfoOld;
import com.das.business.changjiang.mapper.CjRuinsInfoOldMapper;
import com.das.es.entity.EsEntity;
import com.das.es.service.EsService;
import com.das.system.entity.SysCode;
import com.das.system.mapper.SysCodeMapper;
import com.das.utils.response.ResultCode;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.*;

/**
 * Description 根据excel模板导入数据
 *
 * <AUTHOR>
 * @since 2022/5/20
 */
@Slf4j
@RestController
@RequestMapping(value = "/ExcelImport")
@Api(tags = "根据excel模板导入数据")
public class ImportDataByExcelController {

//    @Resource
    private CjRuinsInfoOldMapper cjRuinsInfoMapper;

    @Resource
    private SysCodeMapper sysCodeMapper;

    @Resource
    private EsService esService;

    //2003- 版本的excel
    private final static String excel2003L =".xls";
    //2007+ 版本的excel
    private final static String excel2007U =".xlsx";

//    public List<CjRuinsInfo> esList;

    //类型
    public Map<Long,String> categoryMap2;
    //级别
    public Map<Long,String> levelMap2;
    //年代
    //public Map<Long,String> ageMap2;
    //保存状况
    public Map<Long,String> preserveStatusMap2;

    /**
     * @方法名称: ImportDataByExcel
     * @实现功能: 根据excel模板导入数据
     * @param    file
     **/
    @ApiOperation(value="导入遗址概况excel表",response = CjRuinsInfoOld.class)
    @PostMapping(value = "/importDataByExcel")
    public ResultMsg importDataByExcel(@RequestParam("file") MultipartFile file){
        String resultStr;
        try{
            resultStr = importData(file);
        }catch (Exception e){
            return new ResultMsg(ResultCode.CODE_40004,e.getMessage(),false);
        }
        return new ResultMsg(resultStr);
    }


    public String importData(MultipartFile file) throws Exception{
        List<CjRuinsInfoOld> cjRuinsInfoList = new ArrayList<>();

        //获取数据
        List<List<Object>> oList;
        try {
            oList = getListByExcel(file.getInputStream(), file.getOriginalFilename());
        }catch (Exception e){
            throw new Exception("Excel解析失败！");
        }

        //记录数据格式错误的行数
        List<String> errorList = new ArrayList<>();

        //获取导入的参数字典
        //类型
        Map<String,Long> categoryMap = new HashMap<>();
        categoryMap2 = new HashMap<>();
        //级别
        Map<String,Long> levelMap = new HashMap<>();
        levelMap2 = new HashMap<>();
        //年代
        //Map<String,Long> ageMap = new HashMap<>();
        //ageMap2 = new HashMap<>();
        //保存状况
        Map<String,Long> preserveStatusMap = new HashMap<>();
        preserveStatusMap2 = new HashMap<>();

        QueryWrapper<SysCode> wrapper = new QueryWrapper();
        List<SysCode> codeList = sysCodeMapper.selectList(wrapper);
        for(SysCode sysCode:codeList){
            if(sysCode.getParentId()==610){
                categoryMap.put(sysCode.getDescription(),sysCode.getId());
                categoryMap2.put(sysCode.getId(),sysCode.getDescription());
            }

            if(sysCode.getParentId()==614){
                levelMap.put(sysCode.getDescription(),sysCode.getId());
                levelMap2.put(sysCode.getId(),sysCode.getDescription());
            }

//            if(sysCode.getParentId()==457
//                    || sysCode.getParentId()==461
//                    || sysCode.getParentId()== 463
//                    || sysCode.getParentId()== 464
//                    || sysCode.getParentId()== 466
//                    || sysCode.getParentId()== 467
//                    || sysCode.getParentId()==492
//                    || sysCode.getParentId()== 493
//                    || sysCode.getParentId()==469
//                    || sysCode.getParentId()==470
//                    || sysCode.getParentId()==480
//                    || sysCode.getId()==457){
//                ageMap.put(sysCode.getDescription(),sysCode.getId());
//                ageMap2.put(sysCode.getId(),sysCode.getDescription());
//            }

            if(sysCode.getParentId()==77){
                preserveStatusMap.put(sysCode.getDescription(),sysCode.getId());
                preserveStatusMap2.put(sysCode.getId(),sysCode.getDescription());
            }
        }

        //封装数据
        //从第二行开始，第一行是表头
        for (int i = 0; i < oList.size(); i++) {
            //必填字段判断
            String errorStr = "";
            try {
                List<Object> list = oList.get(i);
                CjRuinsInfoOld cjRuinsInfo = new CjRuinsInfoOld();

                //根据下标获取每一行的每一条数据
                String name = String.valueOf(list.get(0));
                if(StringUtils.isNotBlank(name)){
                    cjRuinsInfo.setName(name);
                }else{
                    //errorStr += "遗址名称不能为空！";
                    continue;
                }

                String category = String.valueOf(list.get(1));
                if (StringUtils.isNotBlank(category)) {
                    if(categoryMap.get(category)!=null) {
                        cjRuinsInfo.setCategory(categoryMap.get(category));
                    }else{
                        errorStr += "类型不能解析！";
                    }
                }else{
                    errorStr += "类型不能为空！";
                }

                cjRuinsInfo.setCity(String.valueOf(list.get(2)));
                cjRuinsInfo.setCounty(String.valueOf(list.get(3)));

                cjRuinsInfo.setAddress(String.valueOf(list.get(4)));

                String longitude = String.valueOf(list.get(5));
                if(StringUtils.isNotBlank(longitude)){
                    try {
                        cjRuinsInfo.setLongitude(Double.parseDouble(longitude));
                    }catch (Exception e){
                        errorStr += "经度只能是数字类型！";
                    }
                }else{
                    errorStr += "经度不能为空！";
                }

                String latitude = String.valueOf(list.get(6));
                if(StringUtils.isNotBlank(latitude)){
                    try {
                        cjRuinsInfo.setLatitude(Double.parseDouble(latitude));
                    }catch (Exception e){
                        errorStr += "纬度只能是数字类型！";
                    }
                }else{
                    errorStr += "纬度不能为空！";
                }

                String altitude = String.valueOf(list.get(7));
                if(StringUtils.isNotBlank(altitude)) {
                    try {
                        cjRuinsInfo.setAltitude(Double.parseDouble(altitude));
                    }catch (Exception e){
                        errorStr += "海拔高度只能是数字类型！";
                    }
                }

                String level = String.valueOf(list.get(8));
                if (StringUtils.isNotBlank(level)) {
                    if(levelMap.get(level)!=null){
                        cjRuinsInfo.setLevel(levelMap.get(level));
                    }else{
                        errorStr += "级别不能解析！";
                    }
                }else{
                    errorStr += "级别不能为空！";
                }

                String acreage = String.valueOf(list.get(9));
                if(StringUtils.isNotBlank(acreage)){
                    try {
                        cjRuinsInfo.setAcreage(Double.parseDouble(acreage));
                    }catch (Exception e){
                        errorStr += "面积只能是数字类型！";
                    }
                }

                String age = String.valueOf(list.get(10));
                if (StringUtils.isNotBlank(age)) {
//                    if(ageMap.get(age)!=null) {
//                        cjRuinsInfo.setAge(ageMap.get(age));
//                    }else{
//                        errorStr += "年代不能解析！";
//                    }
                    cjRuinsInfo.setAge(age);
                }else{
                    errorStr += "年代不能为空！";
                }

                cjRuinsInfo.setOwner(String.valueOf(list.get(11)));
                cjRuinsInfo.setSubjection(String.valueOf(list.get(12)));
                cjRuinsInfo.setMonomerDescription(String.valueOf(list.get(13)));
                cjRuinsInfo.setDescription(String.valueOf(list.get(14)));

                String preserveStatus = String.valueOf(list.get(15));
                if (StringUtils.isNotBlank(preserveStatus)) {
                    if(preserveStatusMap.get(preserveStatus)!=null) {
                        cjRuinsInfo.setPreserveStatus(preserveStatusMap.get(preserveStatus));
                    }else {
                        errorStr += "保存状况不能解析！";
                    }
                }

                cjRuinsInfo.setStatusDescription(String.valueOf(list.get(16)));
                cjRuinsInfo.setNaturalReason(String.valueOf(list.get(17)));
                cjRuinsInfo.setArtificialReason(String.valueOf(list.get(18)));
                cjRuinsInfo.setReasonDescription(String.valueOf(list.get(19)));
                cjRuinsInfo.setEnvironment(String.valueOf(list.get(20)));
                cjRuinsInfo.setHumanity(String.valueOf(list.get(21)));
                cjRuinsInfo.setImportTime(new Date());
                cjRuinsInfoList.add(cjRuinsInfo);
                if(StringUtils.isNotBlank(errorStr)){
                    throw new Exception("该行有必填字段未填！");
                }
            }catch (Exception e){
                errorList.add((i+2)+":"+errorStr);
            }
        }
        if(errorList.size() > 0){
            throw new Exception("数据格式有误！错误的行序号为："+String.join(";",errorList));
        }

        try {
            cjRuinsInfoMapper.batchInsert(cjRuinsInfoList);
            ruinsSaveEs(cjRuinsInfoList);
//            esList = new ArrayList<>();
//            esList.addAll(cjRuinsInfoList);
//            new Thread(() -> ImportDataByExcelController.this.ruinsSaveEs(esList)).start();
        }catch (Exception e){
            throw new Exception("导入失败！",e);
        }

        return "导入成功！";
    }

    private void ruinsSaveEs(List<CjRuinsInfoOld> cjRuinsInfoList) {
        List<EsEntity> esEntities = new ArrayList<>();
        for (CjRuinsInfoOld cjRuinsInfo : cjRuinsInfoList) {
            EsEntity esEntity = new EsEntity();
            esEntity.setTid(cjRuinsInfo.getId())
                    .setRuinsName(cjRuinsInfo.getName())
                    .setCategory(categoryMap2.get(cjRuinsInfo.getCategory())==null?"":categoryMap2.get(cjRuinsInfo.getCategory()))
                    .setCity(cjRuinsInfo.getCity())
                    .setCounty(cjRuinsInfo.getCounty())
                    .setAddress(cjRuinsInfo.getAddress())
                    .setLongitude(cjRuinsInfo.getLongitude())
                    .setLatitude(cjRuinsInfo.getLatitude())
                    .setAltitude(cjRuinsInfo.getAltitude())
                    .setLevel(levelMap2.get(cjRuinsInfo.getLevel())==null?"":levelMap2.get(cjRuinsInfo.getLevel()))
                    .setAcreage(cjRuinsInfo.getAcreage())
//                    .setAge(ageMap2.get(cjRuinsInfo.getAge())==null?"":ageMap2.get(cjRuinsInfo.getAge()))
                    .setAge(cjRuinsInfo.getAge())
                    .setOwner(cjRuinsInfo.getOwner())
                    .setSubjection(cjRuinsInfo.getSubjection())
                    .setMonomerDescription(cjRuinsInfo.getMonomerDescription())
                    .setDescription(cjRuinsInfo.getDescription())
                    .setPreserveStatus(preserveStatusMap2.get(cjRuinsInfo.getPreserveStatus())==null?"":preserveStatusMap2.get(cjRuinsInfo.getPreserveStatus()))
                    .setStatusDescription(cjRuinsInfo.getStatusDescription())
                    .setNaturalReason(cjRuinsInfo.getNaturalReason())
                    .setArtificialReason(cjRuinsInfo.getArtificialReason())
                    .setReasonDescription(cjRuinsInfo.getReasonDescription())
                    .setEnvironment(cjRuinsInfo.getEnvironment())
                    .setHumanity(cjRuinsInfo.getHumanity())
                    .setResourceId(cjRuinsInfo.getId());
            esEntities.add(esEntity);
            //esService.saveEs(esEntity);
        }
        esService.batchSave(esEntities);
    }


    /**
     * 描述：获取IO流中的数据，组装成List<List<Object>>对象
     * @param in,fileName
     * @return
     * @throws Exception
     */
    public static List<List<Object>> getListByExcel(InputStream in, String fileName) throws Exception {
        List<List<Object>> list;
        //创建Excel工作薄
        Workbook work = getWorkbook(in,fileName);
        if(work == null){
            throw new Exception("创建Excel工作薄为空！");
        }
        Sheet sheet;
        Row row;
        Cell cell;
        list = new ArrayList<>();
        //遍历Excel中第一个sheet

        sheet = work.getSheetAt(0);

        //根据表头记录列数
        int cellNums = sheet.getRow(sheet.getFirstRowNum()).getLastCellNum();

        //遍历当前sheet中的所有行
        for (int j = sheet.getFirstRowNum(); j < sheet.getLastRowNum()+1; j++) {
            row = sheet.getRow(j);
            if(row==null || row.getFirstCellNum()==j){
                continue;
            }
            //遍历所有的列
            List<Object> li = new ArrayList<>();
            for (int y = 0; y < cellNums; y++) {
                cell = row.getCell(y);
                li.add(getCellValue(cell));
            }
            list.add(li);
        }

        return list;
    }
    /**
     * 描述：根据文件后缀，自适应上传文件的版本
     * @param inStr,fileName
     * @return
     * @throws Exception
     */
    public static Workbook getWorkbook(InputStream inStr, String fileName) throws Exception{
        Workbook wb ;
        String fileType = fileName.substring(fileName.lastIndexOf("."));
        if(excel2003L.equals(fileType)){
            wb = new HSSFWorkbook(inStr);  //2003-
        }else if(excel2007U.equals(fileType)){
            wb = new XSSFWorkbook(inStr);  //2007+
        }else{
            throw new Exception("解析的文件格式有误！");
        }
        return wb;
    }
    /**
     * 描述：对表格中数值进行格式化
     * @param cell
     * @return
     */
    public  static Object getCellValue(Cell cell){
        Object value = "";
        if (cell!=null){
            switch (cell.getCellTypeEnum()) {
                case STRING:
                    value = cell.getRichStringCellValue().getString();
                    break;
                case NUMERIC:
                    value = cell.getNumericCellValue();
                    break;
                case BOOLEAN:
                    value = cell.getBooleanCellValue();
                    break;
                case BLANK:
                    value = "";
                    break;
                default:
                    break;
            }
        }
        return value;
    }

}
