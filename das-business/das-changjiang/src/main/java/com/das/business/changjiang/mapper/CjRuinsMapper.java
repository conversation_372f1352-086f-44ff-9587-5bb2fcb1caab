package com.das.business.changjiang.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.entity.CjRuins;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.das.system.entity.SysCode;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Repository
public interface CjRuinsMapper extends BaseMapper<CjRuins> {

    List<CjRuins> findRuins(@Param("cjRuins") CjRuins cjRuins);

    IPage<CjRuins> getRuinsList(@Param(value = "page") Page<CjRuins> page,
                                @Param(value = "params") Map<String, Object> params,
                                @Param(value = "sort") String sort, @Param(value = "order") String order);

    List<String> getRuinsTotal(CjRuins cjRuins);

    List<String> getCjReportTotal(CjRuins cjRuins);

    List<String> getCollectInfo(CjRuins cjRuins);

    CjRuins getRuinsById(@Param(value = "id") Long id);
}
