package com.das.business.changjiang.controller.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.das.business.changjiang.entity.CjRunisInfoFile;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;

/**
 * 
 * @Description
 * <AUTHOR>
 * @Date 2025-01-02
 */
@Data
public class CjRuinsInfoVO {

	@ApiModelProperty(value = "主键id", required = false)
	private Long id;

	@ApiModelProperty(value = "所属父类", required = false)
	private Long parentId;

	@ExcelProperty("文物名称")
	@ApiModelProperty(value = "文物名称", required = false)
	private String name;

	@ApiModelProperty(value = "类别", required = false)
	private Long category;

	@ApiModelProperty(value = "市", required = false)
	private String city;

	@ApiModelProperty(value = "县", required = false)
	private String county;

	@ExcelProperty("地址")
	@ApiModelProperty(value = "地址", required = false)
	private String address;

	@ExcelProperty("经度")
	@ApiModelProperty(value = "经度", required = false)
	private String longitude;

	@ExcelProperty("纬度")
	@ApiModelProperty(value = "纬度", required = false)
	private String latitude;

	@ApiModelProperty(value = "海拔高度", required = false)
	private String altitude;

	@ExcelProperty("发掘墓葬数量")
	@ApiModelProperty(value = "发掘墓葬数量（座）", required = false)
	private Integer discoverTombs;

	@ExcelProperty("探明墓葬数量")
	@ApiModelProperty(value = "探明墓葬数量（座）", required = false)
	private Integer proveTombs;

	@ExcelProperty("发掘时间")
	@ApiModelProperty(value = "发掘时间", required = false)
	private String discoverTime;

	@ExcelProperty("时代")
	@ApiModelProperty(value = "时代", required = false)
	private String age;

	@ExcelProperty("发掘经过")
	@ApiModelProperty(value = "发掘经过", required = false)
	private String discoverLog;

	@ExcelProperty("墓葬分布情况")
	@ApiModelProperty(value = "墓葬分布情况", required = false)
	private String tombsLayout;

	@ExcelProperty("地理、环境描述")
	@ApiModelProperty(value = "地理、环境描述", required = false)
	private String environment;

	@ExcelProperty("周边遗迹描述")
	@ApiModelProperty(value = "周边遗迹描述", required = false)
	private String surroundingRuins;

	@ExcelProperty("报告、简报")
	@ApiModelProperty(value = "报告、简报", required = false)
	private String report;

	@ExcelProperty("其他信息")
	@ApiModelProperty(value = "其他信息", required = false)
	private String otherInfo;

	@ApiModelProperty(value = "墓地平面布局图(存储压缩的小图、加载大图默认名称去掉_min后加载)", required = false)
	private String layoutPlanUrl;

	@ApiModelProperty(value = "创建时间", required = false)
	private String createTime;

	@ApiModelProperty(value = "修改时间", required = false)
	private String updateTime;

	@ApiModelProperty(value = "示意图片", required = false)
	private List<CjRuinsInfoFileVO> imgList;

}
