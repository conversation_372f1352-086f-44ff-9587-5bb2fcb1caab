package com.das.business.changjiang.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.das.annotation.DasController;
import com.das.business.changjiang.entity.CjRuins;
import com.das.business.changjiang.entity.CjRuinsInfoTombs;
import com.das.business.changjiang.entity.requestEntity.RuinsRequest;
import com.das.business.changjiang.service.ICjRuinsService;
import com.das.business.changjiang.service.impl.*;
import com.das.system.entity.SysCode;
import com.das.system.entity.SysUser;
import com.das.system.service.ISysCodeService;
import com.das.utils.common.CangPConstant;
import com.das.utils.common.SystemUtil;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultCode;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.var;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 考古遗址
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@DasController("/ruins")
@Api(description = "考古遗址")
public class RuinsController {

    @Resource
    ICjRuinsService cjRuinsService;

    @Resource
    CjRuinsDetailServiceImpl cjRuinsDetailService;

    @Resource
    ISysCodeService sysCodeService;


    @ApiOperation(value = "考古遗址初始化查询大菜单")
    @RequestMapping(value = "/init", method = RequestMethod.GET)
    public ResultMsg init() {
        QueryWrapper majorQue = new QueryWrapper();
        majorQue.select("id","major_id", "name", "`desc`").eq("parent_id", "0");
        List<Map<String, Object>> majorArr = cjRuinsService.listMaps(majorQue);
        majorArr.forEach(item -> {
            String[] desc = item.get("desc").toString().split(",");
            item.put("gps", new ArrayList() {{
                add(new BigDecimal(desc[0]));
                add(new BigDecimal(desc[1]));
            }});
            item.remove("desc");
        });

        var minorQue = new QueryWrapper<SysCode>();
        minorQue.select("code", "description").eq("field", CangPConstant.MINORTYPE).orderByAsc("sort_no");
        List<Map<String, Object>> minorArr = sysCodeService.listMaps(minorQue);
        minorArr.forEach(item -> {
            item.put("minorId", item.get("code"));
            item.put("name", item.get("description"));
            item.remove("code");
            item.remove("description");
        });
        JSONObject res = new JSONObject();

        res.put("major", majorArr);
        res.put("minor", minorArr);
        return new ResultMsg(res);
    }


    @ApiOperation(value = "新增考古遗址初始化")
    @RequestMapping(value = "/addInit", method = RequestMethod.GET)
    public ResultMsg addInit() {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.select("major_id", "name").eq("parent_id", "0");
        List majorArr = cjRuinsService.listMaps(queryWrapper);

        var minorQue = new QueryWrapper<SysCode>();
        minorQue.select("code", "description").eq("field", CangPConstant.MINORTYPE).notIn("description", "文物").orderByAsc("sort_no");
        List<Map<String, Object>> minorArr = sysCodeService.listMaps(minorQue);
        minorArr.forEach(item -> {
            item.put("minorId", Integer.parseInt(item.get("code").toString()));
            item.put("name", item.get("description"));
            item.remove("code");
            item.remove("description");
        });
        JSONObject res = new JSONObject();
        res.put("major", majorArr);
        res.put("minor", minorArr);
        return new ResultMsg(res);
    }

    @ApiOperation(value = "根据大类查询树结构")
    @RequestMapping(value = "/getRuinsTree", method = RequestMethod.POST)
    public ResultMsg getByPage(@RequestBody CjRuins ruins) {
        return new ResultMsg(cjRuinsService.findRuins(ruins));
    }


    @ApiOperation(value = "根据树查询所有的点线面集合数据")
    @RequestMapping(value = "/getRuinsDetail", method = RequestMethod.POST)
    public ResultMsg getRuinsDetail(@RequestBody Map<String, String> params) {
        String[] ids = params.get("id").split(",");
//        String[] ids = "3,5,6,7,8,9,10,11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28,29".split(",");
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.select("line_set", "id", "gps", "name").in("parent_id", ids);
        return new ResultMsg(cjRuinsDetailService.listMaps(queryWrapper));
    }

    @ApiOperation(value = "查询点详细信息")
    @RequestMapping(value = "/getRuinsDetailById", method = RequestMethod.POST)
    public ResultMsg getRuinsDetailById(@RequestBody CjRuins cjRuins) {
        return cjRuinsDetailService.getRuinsDetail(cjRuins.getId());
    }


    @ApiOperation(value = "查询时间轴")
    @RequestMapping(value = "/getTimeAxis", method = RequestMethod.POST)
    public ResultMsg getTimeAxis(@RequestBody RuinsRequest ruinsRequest) {
        var ageQue = new QueryWrapper<SysCode>();
        String queField = CangPConstant.EXCAVATEAGE;
        if (ruinsRequest.getType() == CangPConstant.RUINSAGE_ID) {
            queField = CangPConstant.RUINSAGE;
        }
        ageQue.select("code", "description").eq("field", queField).orderByAsc("sort_no");
        List<Map<String, Object>> ageArr = sysCodeService.listMaps(ageQue);
        ageArr.forEach(item -> {
            item.put("ageAnge", item.get("code"));
            item.put("dynasty", item.get("description"));
            item.remove("code");
            item.remove("description");
        });
        return new ResultMsg(ageArr);
    }

    @ApiOperation(value = "新增遗址点信息")
    @RequestMapping(value = "/saveRuins", method = RequestMethod.POST)
    public ResultMsg saveRuins(@ModelAttribute CjRuins cjRuins, @RequestParam(value = "file" , required = false) MultipartFile file) {
        SysUser sysUser = (SysUser) SystemUtil.getLoginUser();
        if (sysUser == null) {
            return new ResultMsg(ResultCode.CODE_10014, false);
        }
        boolean flag = cjRuinsService.saveRuins(cjRuins, file);
        return flag ? new ResultMsg(true) : new ResultMsg(ResultCode.CODE_600001, false);
    }

    @ApiOperation(value = "删除遗址点信息")
    @RequestMapping(value = "/delRuins", method = RequestMethod.POST)
    public ResultMsg delRuins(@RequestParam Long id) {
        return new ResultMsg(cjRuinsService.delRuins(id));
    }

    @ApiOperation(value = "分页获取遗址点信息")
    @RequestMapping(value = "/getRuinsList", method = RequestMethod.POST)
    public ResultMsg getRuinsList(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(cjRuinsService.getRuinsList(pageEntity));
    }

    @ApiOperation(value = "分页获取遗址点信息")
    @RequestMapping(value = "/getRuinsById", method = RequestMethod.POST)
    public ResultMsg getRuinsById(@RequestParam Long id) {
        return new ResultMsg(cjRuinsService.getRuinsById(id));
    }

    @ApiOperation(value = "遗址点统计信息")
    @RequestMapping(value = "/getRuinsTotal", method = RequestMethod.POST)
    public ResultMsg getRuinsTotal(@RequestBody CjRuins cjRuins) {
        return new ResultMsg(cjRuinsService.getRuinsTotal(cjRuins));
    }


}
