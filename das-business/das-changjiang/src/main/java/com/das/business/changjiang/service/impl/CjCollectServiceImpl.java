package com.das.business.changjiang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.entity.CjCollect;
import com.das.business.changjiang.entity.CjMark;
import com.das.business.changjiang.entity.CjReport;
import com.das.business.changjiang.mapper.CjCollectMapper;
import com.das.business.changjiang.service.ICjCollectService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.system.entity.SysUser;
import com.das.utils.common.SystemUtil;
import com.das.utils.response.PageEntity;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
@Service
@Transactional
@Slf4j
public class CjCollectServiceImpl extends ServiceImpl<CjCollectMapper, CjCollect> implements ICjCollectService {

    @Resource
    private CjCollectMapper cjCollectMapper;

    @Resource
    private CjReportServiceImpl cjReportService;

    @Override
    public IPage<CjCollect> getCjCollectList(PageEntity pageEntity) {
        SysUser sysUser = (SysUser)SystemUtil.getLoginUser();
        pageEntity.getParams().put("userId",sysUser.getId());
        return cjCollectMapper.getCjCollectList(new Page<>(pageEntity.getCurrent(),pageEntity.getSize()),
                pageEntity.getParams(),pageEntity.getSort(),pageEntity.getOrder());
    }

    @Override
    public boolean saveCjCollect(CjCollect collect) {
        SysUser sysUser = (SysUser) SystemUtil.getLoginUser();
        collect.setUserId(Integer.parseInt(sysUser.getId().toString()) );
        val wrapper = new QueryWrapper<CjCollect>();
        wrapper.eq("source_id",collect.getSourceId()).eq("user_id",sysUser.getId());
        List<CjCollect> list = list(wrapper);
        if(list.size() > 0){
            return removeById(list.get(0).getId());
        }
        return saveOrUpdate(collect);
    }

    @Override
    public boolean delCjCollect(Long id) {
        SysUser sysUser = (SysUser) SystemUtil.getLoginUser();
        Map map = new HashMap();
        map.put("source_id",id);
        map.put("user_id",sysUser.getId());
        return removeByMap(map);
    }

    @Override
    public CjCollect getCjCollectById(Long id) {
        return cjCollectMapper.getCjCollectById(id);
    }
}
