package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 遗址数字化成果实体
 * </p>
 *
 * <AUTHOR>
 * @since 2022-5-23
 */ 
@Data
public class CjRuinsDigit implements Serializable {

    // 主键ID
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(name = "id", value = "主键ID", required = true)
    private Long id;

    @ApiModelProperty(name = "ruinsId", value = "遗址id", required = false)
    private Long ruinsId;

    @ApiModelProperty(name = "name", value = "数字化成果名称", required = false)
    private String name;

    @ApiModelProperty(name = "type", value = "数字化成果类型", required = false)
    private Long type;

    @ApiModelProperty(name = "loadPath", value = "地址", required = false)
    private String loadPath;

    @ApiModelProperty(name = "status", value = "解压状态（0未转换,1转换中,2转换成功,3转换失败）", required = false)
    private Integer status;

    @ApiModelProperty(name = "model", value = "模型展示地址", required = false)
    private String model;

    @ApiModelProperty(name = "cover", value = "封面地址", required = false)
    private String cover;

    @ApiModelProperty(name = "thumbnail", value = "封面缩略图", required = false)
    private String thumbnail;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间", required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(name = "updateTime", value = "修改时间", required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 数字化成果类型名称
     */
    @TableField(exist = false)
    private String typeName;

    @TableField(exist = false)
    private List<CjFile> cjFiles;

}
