package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * 考古遗址实体-点线面实体
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Data
public class CjRuinsDetail extends Model<CjRuinsDetail> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private long id;
    private String name;
    @TableField(value = "`desc`")
    private String desc;
    private String excavateAge;
    private String ruinsAge;
    private String gps;
    private int type;
    private String lineSet;
    private String color;
    private Long parentId;

}
