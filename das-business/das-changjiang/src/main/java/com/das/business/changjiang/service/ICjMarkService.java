package com.das.business.changjiang.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.das.business.changjiang.entity.CjMark;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.utils.response.PageEntity;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
public interface ICjMarkService extends IService<CjMark> {

    IPage<CjMark> getCjMarkList(PageEntity pageEntity);

    boolean saveCjMark(CjMark cjMark);

    boolean delCjMark(Long id);

    CjMark getCjMarkById(Long id);
}
