package com.das.business.changjiang.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.business.changjiang.entity.CjRuinsInfoOld;
import com.das.utils.response.PageEntity;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【cj_ruins_info(考古遗址基础信息表)】的数据库操作Service
 * @createDate 2022-05-20 17:04:24
 */
public interface CjRuinsInfoOldService  {

    /**
     * 分页查询
     */
    IPage<CjRuinsInfoOld> getCjRuinsInfoList(PageEntity pageEntity);

    /**
     * 分页查询
     */
    void ExportCjRuinsInfoList(String level,String category,String age,String name,String[] ids, HttpServletResponse response);

    /**
     * 新增or修改考古遗址
     */
    boolean saveCjRuinsInfo(CjRuinsInfoOld cjRuinsInfo);

    /**
     * 删除考古遗址信息
     */
    boolean delCjRuinsInfo(List<Long> ids);

    /**
     * 根据id查询考古遗址信息
     */
    CjRuinsInfoOld getCjRuinsInfoById(Long id);

    /**
     * 获取考古遗址列表
     * @return
     */
    List<CjRuinsInfoOld> getCjRuinsInfoAll(Long type);

    /**
     * 根据树查询所有的集合数据
     * @param params
     * @return
     */
    List<CjRuinsInfoOld> getRuinsDetail(Map<String,String> params);

    /**
     * 全文检索考古遗址列表
     * @param keyword
     * @return
     */
    List<CjRuinsInfoOld> getCjRuinsInfo(String keyword);

}
