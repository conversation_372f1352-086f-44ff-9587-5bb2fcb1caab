package com.das.business.changjiang.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.business.changjiang.entity.CjPlaceNameData;
import com.das.system.entity.PageEntity;

/**
 * <p>
 * 地名资料表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
public interface ICjPlaceNameDataService extends IService<CjPlaceNameData> {

    /**
     * 分页查询地名资料列表
     *
     * @param pageEntity 分页查询参数
     * @return 分页结果
     */
    IPage<CjPlaceNameData> getCjPlaceNameDataList(PageEntity pageEntity);

    /**
     * 根据ID查询地名资料详情
     *
     * @param id 地名资料ID
     * @return 地名资料详情
     */
    CjPlaceNameData getCjPlaceNameDataById(Long id);

    /**
     * 保存或更新地名资料
     *
     * @param cjPlaceNameData 地名资料对象
     * @return 操作结果
     */
    boolean saveCjPlaceNameData(CjPlaceNameData cjPlaceNameData);

    /**
     * 删除地名资料
     *
     * @param id 地名资料ID
     * @return 操作结果
     */
    boolean deleteCjPlaceNameData(Long id);

    /**
     * 批量删除地名资料
     *
     * @param ids 地名资料ID数组
     * @return 操作结果
     */
    boolean batchDeleteCjPlaceNameData(Long[] ids);

    /**
     * 更新发布状态
     *
     * @param id            地名资料ID
     * @param publishStatus 发布状态
     * @return 操作结果
     */
    boolean updatePublishStatus(Long id, Integer publishStatus);
}
