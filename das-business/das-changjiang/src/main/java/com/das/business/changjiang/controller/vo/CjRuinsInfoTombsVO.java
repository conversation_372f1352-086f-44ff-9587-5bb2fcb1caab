package com.das.business.changjiang.controller.vo;

import com.das.business.changjiang.entity.CjRuinsInfoTombs;
import com.das.business.changjiang.entity.CjRuinsInfoTombsCoffin;
import com.das.business.changjiang.entity.CjRunisInfoFile;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 墓地基本信息对象
 *
 * <AUTHOR>
 */
@Data
public class CjRuinsInfoTombsVO extends CjRuinsInfoTombs implements Serializable {


    /**
     * 椁室大小
     */
    List<CjRuinsInfoTombsCoffin> coffinList;

    /**
     * 示意图片
     */
    private List<CjRunisInfoFile> imgList;

    /**
     * 随葬遗物列表，1铜器
     */
    List<CjRuinsInfoTombsFollowNameVO> follow1List;

    /**
     * 随葬遗物列表，2陶器
     */
    List<CjRuinsInfoTombsFollowNameVO> follow2List;

    /**
     * 随葬遗物列表，3漆木、竹器
     */
    List<CjRuinsInfoTombsFollowNameVO> follow3List;

    /**
     * 随葬遗物列表，4玉、石、料器
     */
    List<CjRuinsInfoTombsFollowNameVO> follow4List;

    /**
     * 随葬遗物列表，5骨、角器
     */
    List<CjRuinsInfoTombsFollowNameVO> follow5List;

    /**
     * 随葬遗物列表，6丝、麻、皮革
     */
    List<CjRuinsInfoTombsFollowNameVO> follow6List;

    /**
     * 随葬遗物列表，7其他
     */
    List<CjRuinsInfoTombsFollowNameVO> follow7List;

}
