package com.das.business.changjiang.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.business.changjiang.entity.CjRuinsDigit;
import com.das.business.changjiang.entity.responseEntity.TypeAndCount;
import com.das.utils.response.PageEntity;

import java.util.List;

/**
 * <p>
 * 服务类接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-5-23
 */
public interface ICjRuinsDigitService extends IService<CjRuinsDigit> {

    /**
     * 分页查询
     */
    IPage<CjRuinsDigit> getCjRuinsDigitList(PageEntity pageEntity);

    /**
     * 新增or修改考古遗址成果
     */
    boolean saveCjRuinsDigit(CjRuinsDigit cjRuinsDigit);

    /**
     * 删除考古遗址信息成果
     */
    boolean delCjRuinsDigit(List<Long> ids);

    /**
     * 根据id查询考古遗址信息成果
     */
    CjRuinsDigit getCjRuinsDigitById(Long id);

    /**
     * 根据id查询考古遗址成果分类
     *
     * @param ruinsId
     * @return
     */
    List<TypeAndCount> getTypeAndCount(Long ruinsId);

}
