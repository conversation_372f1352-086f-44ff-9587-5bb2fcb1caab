package com.das.business.changjiang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.entity.CjCollectionInfo;
import com.das.business.changjiang.entity.CjFile;
import com.das.business.changjiang.mapper.CjCollectionInfoMapper;
import com.das.business.changjiang.service.ICjCollectionInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.system.entity.SysCode;
import com.das.system.entity.entitywrap.PageEntity;
import com.das.system.mapper.SysCodeMapper;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Service
public class CjCollectionInfoServiceImpl extends ServiceImpl<CjCollectionInfoMapper, CjCollectionInfo> implements ICjCollectionInfoService {

    @Resource
    private CjCollectionInfoMapper cjCollectionInfoMapper;

    @Resource
    private CjFileServiceImpl cjFileService;


    @Override
    public IPage<SysCode> getByPage(PageEntity pageEntity) {
        //年代
        if(!"".equals(pageEntity.getParams().get("age")) && pageEntity.getParams().get("age") != null ){
            String[] ages = pageEntity.getParams().get("age").toString().split(",");
            pageEntity.getParams().put("age", Arrays.asList(ages));
        }else{
            pageEntity.getParams().put("age",null);
        }
        //级别
        if(!"".equals(pageEntity.getParams().get("level")) && pageEntity.getParams().get("level") != null ){
            String[] level = pageEntity.getParams().get("level").toString().split(",");
            pageEntity.getParams().put("level", Arrays.asList(level));
        }else{
            pageEntity.getParams().put("level",null);
        }
        //级别
        if(!"".equals(pageEntity.getParams().get("texture")) && pageEntity.getParams().get("texture") != null ){
            String[] texture = pageEntity.getParams().get("texture").toString().split(",");
            pageEntity.getParams().put("texture", Arrays.asList(texture));
        }else{
            pageEntity.getParams().put("texture",null);
        }
        IPage<SysCode> page = cjCollectionInfoMapper.getByPage(new Page<>(pageEntity.getCurrent(),pageEntity.getSize()),
                pageEntity.getParams(),pageEntity.getSort(),pageEntity.getOrder());
        return page;
    }

    @Override
    public boolean saveCjCollectionInfo(CjCollectionInfo cjCollectionInfo) {
        if (cjCollectionInfo.getCjFiles() != null && cjCollectionInfo.getCjFiles().size() > 0) {
            CjFile cjFile = cjCollectionInfo.getCjFiles().get(0);
            cjCollectionInfo.setFileName(cjFile.getName());
        }
        return saveOrUpdate(cjCollectionInfo);
    }

    @Override
    public boolean delCjCollectionInfo(Long id) {
        return removeById(id);
    }

    @Override
    public List<Map> getGroupByData() {
        return cjCollectionInfoMapper.getGroupByData();
    }

    @Override
    public CjCollectionInfo getInfoById(Long id) {
        return cjCollectionInfoMapper.getInfoById(id);
    }

    @Override
    public List<CjCollectionInfo> getInfoByRuinsId(Long id) {
        return cjCollectionInfoMapper.getInfoByRuinsId(id);
    }


}
