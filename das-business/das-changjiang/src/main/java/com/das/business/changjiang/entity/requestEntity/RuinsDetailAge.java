package com.das.business.changjiang.entity.requestEntity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.NonNull;

import javax.validation.constraints.NotNull;

/**
 * 时间轴查询
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
public class RuinsDetailAge {

    @NotNull
    private String start;

    @NotNull
    private String end;

}
