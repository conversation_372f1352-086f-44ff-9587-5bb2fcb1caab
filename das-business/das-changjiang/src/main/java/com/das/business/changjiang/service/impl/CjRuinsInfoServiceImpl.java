package com.das.business.changjiang.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.das.business.changjiang.controller.vo.CjRuinsInfoVO;
import com.das.business.changjiang.entity.CjRuinsInfoNew;
import com.das.business.changjiang.entity.CjRunisInfoFile;
import com.das.business.changjiang.service.ICjRuinsInfoNewService;
import com.das.business.changjiang.service.ICjRunisInfoFileService;
import com.das.utils.response.PageEntity;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;


import com.das.business.changjiang.mapper.CjRuinsInfoMapper;
import com.das.business.changjiang.service.CjRuinsInfoService;
import com.das.business.changjiang.entity.CjRuinsInfo;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2025-01-02
 */
@Service
@RequiredArgsConstructor
public class CjRuinsInfoServiceImpl extends ServiceImpl<CjRuinsInfoMapper, CjRuinsInfo> implements CjRuinsInfoService {

    private final CjRuinsInfoMapper cjRuinsInfoMapper;
    private final ICjRunisInfoFileService cjRunisInfoFileService;

    @Override
    public IPage<CjRuinsInfo> findByPage(PageEntity pageEntity) {
        IPage<CjRuinsInfo> res = cjRuinsInfoMapper.findByPage(new Page<>(pageEntity.getCurrent(), pageEntity.getSize()),
                pageEntity.getParams(), pageEntity.getSort(), pageEntity.getOrder());
        res.getRecords().forEach(item -> {
            item.setImgList(ObjectUtil.isNull(item.getLayoutPlanUrl()) ? null : JSONUtil.parseArray(item.getLayoutPlanUrl()));
        });
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(CjRuinsInfoVO cjRuinsInfoVO) {
        CjRuinsInfo cjRuinsInfo = convertCjRuinsInfo(cjRuinsInfoVO);
        if(ObjectUtil.isNotNull(cjRuinsInfoVO.getImgList())){
            cjRuinsInfo.setLayoutPlanUrl(JSONUtil.toJsonStr(cjRuinsInfoVO.getImgList()));
        }
        if (this.save(cjRuinsInfo)) {
            if(ObjectUtil.isNotNull(cjRuinsInfoVO.getImgList())){
                List<CjRunisInfoFile> fileList = cjRuinsInfoVO.getImgList().stream().map(item -> {
                    String suffix = item.getFileName().substring(item.getFileName().lastIndexOf(".") + 1);
                    return new CjRunisInfoFile(1, cjRuinsInfo.getId(), item.getFileName(), suffix, item.getFilePath());
                }).collect(Collectors.toList());
                cjRunisInfoFileService.saveBatch(fileList);
            }
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(CjRuinsInfoVO cjRuinsInfoVO) {
        CjRuinsInfo cjRuinsInfo = convertCjRuinsInfo(cjRuinsInfoVO);
        if(ObjectUtil.isNotEmpty(cjRuinsInfoVO.getImgList())){
            cjRuinsInfo.setLayoutPlanUrl(JSONUtil.toJsonStr(cjRuinsInfoVO.getImgList()));
        }
        if (this.updateById(cjRuinsInfo)) {
            if(ObjectUtil.isEmpty(cjRuinsInfoVO.getImgList())){
                return true;
            }
            List<CjRunisInfoFile> fileList = cjRuinsInfoVO.getImgList().stream().map(item -> {
                String suffix = "";
                if(!StringUtils.isEmpty(item.getFileName())){
                    suffix = item.getFileName().substring(item.getFileName().lastIndexOf(".") + 1);
                }else{
                    suffix = item.getFilePath().substring(item.getFilePath().lastIndexOf(".") + 1);
                }
                return new CjRunisInfoFile(1, cjRuinsInfo.getId(), item.getFileName(), suffix, item.getFilePath());
            }).collect(Collectors.toList());
            QueryWrapper<CjRunisInfoFile> del = new QueryWrapper<>();
            del.lambda().eq(CjRunisInfoFile::getRuinsId, cjRuinsInfo.getId());
            cjRunisInfoFileService.remove(del);
            cjRunisInfoFileService.saveBatch(fileList);
            return true;
        }
        return false;
    }

    private CjRuinsInfo convertCjRuinsInfo(CjRuinsInfoVO cjRuinsInfoVO) {
        CjRuinsInfo cjRuinsInfo = new CjRuinsInfo();
        cjRuinsInfo.setId(cjRuinsInfoVO.getId());
        //cjRuinsInfo.setParentId(cjRuinsInfoVO.getParentId());
        cjRuinsInfo.setName(cjRuinsInfoVO.getName());
        cjRuinsInfo.setCategory(cjRuinsInfoVO.getCategory());
        cjRuinsInfo.setCity(cjRuinsInfoVO.getCity());
        cjRuinsInfo.setCounty(cjRuinsInfoVO.getCounty());
        cjRuinsInfo.setAddress(cjRuinsInfoVO.getAddress());
        cjRuinsInfo.setLongitude(cjRuinsInfoVO.getLongitude());
        cjRuinsInfo.setLatitude(cjRuinsInfoVO.getLatitude());
        cjRuinsInfo.setAltitude(cjRuinsInfoVO.getAltitude());
        cjRuinsInfo.setDiscoverTombs(cjRuinsInfoVO.getDiscoverTombs());
        cjRuinsInfo.setProveTombs(cjRuinsInfoVO.getProveTombs());
        cjRuinsInfo.setDiscoverTime(cjRuinsInfoVO.getDiscoverTime());
        cjRuinsInfo.setAge(cjRuinsInfoVO.getAge());
        cjRuinsInfo.setDiscoverLog(cjRuinsInfoVO.getDiscoverLog());
        cjRuinsInfo.setTombsLayout(cjRuinsInfoVO.getTombsLayout());
        cjRuinsInfo.setEnvironment(cjRuinsInfoVO.getEnvironment());
        cjRuinsInfo.setSurroundingRuins(cjRuinsInfoVO.getSurroundingRuins());
        cjRuinsInfo.setReport(cjRuinsInfoVO.getReport());
        cjRuinsInfo.setOtherInfo(cjRuinsInfoVO.getOtherInfo());
        cjRuinsInfo.setLayoutPlanUrl(cjRuinsInfoVO.getLayoutPlanUrl());
        return cjRuinsInfo;
    }

}
