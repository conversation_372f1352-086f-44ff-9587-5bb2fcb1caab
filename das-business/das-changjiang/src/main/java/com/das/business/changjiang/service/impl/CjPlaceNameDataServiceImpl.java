package com.das.business.changjiang.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.business.changjiang.entity.CjPlaceNameData;
import com.das.business.changjiang.mapper.CjPlaceNameDataMapper;
import com.das.business.changjiang.service.ICjPlaceNameDataService;
import com.das.business.changjiang.service.ImportExcelUtil;
import com.das.system.entity.SysCode;
import com.das.system.mapper.SysCodeMapper;
import com.das.system.service.ISysCodeService;
import com.das.utils.response.PageEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 地名资料表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@Service
@Transactional
@Slf4j
public class CjPlaceNameDataServiceImpl extends ServiceImpl<CjPlaceNameDataMapper, CjPlaceNameData> implements ICjPlaceNameDataService {

    @Resource
    private CjPlaceNameDataMapper cjPlaceNameDataMapper;

    @Resource
    private SysCodeMapper sysCodeMapper;

    @Resource
    private ISysCodeService sysCodeService;

    @Override
    public IPage<CjPlaceNameData> getCjPlaceNameDataList(PageEntity pageEntity) {
        return cjPlaceNameDataMapper.getCjPlaceNameDataList(
                new Page<>(pageEntity.getCurrent(), pageEntity.getSize()),
                pageEntity.getParams(),
                pageEntity.getSort(),
                pageEntity.getOrder()
        );
    }

    @Override
    public CjPlaceNameData getCjPlaceNameDataById(Long id) {
        return cjPlaceNameDataMapper.getCjPlaceNameDataById(id);
    }

    @Override
    public boolean saveCjPlaceNameData(CjPlaceNameData cjPlaceNameData) {
        try {
            if (cjPlaceNameData.getId() == null) {
                // 新增
                cjPlaceNameData.setCreateTime(LocalDateTime.now());
                cjPlaceNameData.setUpdateTime(LocalDateTime.now());
                if (cjPlaceNameData.getPublishStatus() == null) {
                    cjPlaceNameData.setPublishStatus(0); // 默认未发布
                }
                return this.save(cjPlaceNameData);
            } else {
                // 更新
                cjPlaceNameData.setUpdateTime(LocalDateTime.now());
                return this.updateById(cjPlaceNameData);
            }
        } catch (Exception e) {
            log.error("保存地名资料失败", e);
            return false;
        }
    }

    @Override
    public boolean deleteCjPlaceNameData(Long id) {
        try {
            return this.removeById(id);
        } catch (Exception e) {
            log.error("删除地名资料失败", e);
            return false;
        }
    }

    @Override
    public boolean batchDeleteCjPlaceNameData(Long[] ids) {
        try {
            return this.removeByIds(Arrays.asList(ids));
        } catch (Exception e) {
            log.error("批量删除地名资料失败", e);
            return false;
        }
    }

    @Override
    public boolean updatePublishStatus(Long id, Integer publishStatus) {
        try {
            CjPlaceNameData placeNameData = new CjPlaceNameData();
            placeNameData.setId(id);
            placeNameData.setPublishStatus(publishStatus);
            placeNameData.setUpdateTime(LocalDateTime.now());
            return this.updateById(placeNameData);
        } catch (Exception e) {
            log.error("更新地名资料发布状态失败", e);
            return false;
        }
    }

    @Override
    public boolean importDataByExcel(MultipartFile file){
        //获取数据
        List<List<Object>> oList;
        try {
            oList = ImportExcelUtil.getListByExcel(file.getInputStream(), file.getOriginalFilename());
        }catch (Exception e){
            log.error("Excel解析失败！"+e.getMessage());
            throw new RuntimeException("Excel解析失败！");
        }

        if (!oList.isEmpty()) {
            Map<String,Long> typeMap = new HashMap<>();
            List<SysCode> typeList = sysCodeMapper.getSysCodeList("digitalMapType_1");
            if(CollectionUtil.isNotEmpty(typeList)){
                for(SysCode sysCode:typeList){
                    typeMap.put(sysCode.getDescription(),sysCode.getId());
                }
            }
            Map<String,Long> groupBig = new HashMap<>();
            List<SysCode> groupBigList = sysCodeMapper.getSysCodeList("digitalMapType_2");
            if(CollectionUtil.isNotEmpty(groupBigList)){
                for(SysCode sysCode:groupBigList){
                    groupBig.put(sysCode.getDescription(),sysCode.getId());
                }
            }

            Map<String,Long> groupSmall = new HashMap<>();
            List<SysCode> groupSmallList = sysCodeMapper.getSysCodeList("digitalMapType_3");
            if(CollectionUtil.isNotEmpty(groupSmallList)){
                for(SysCode sysCode:groupSmallList){
                    groupSmall.put(sysCode.getDescription(),sysCode.getId());
                }
            }

            Map<String,Long> ageMap = new HashMap<>();
            Map<String,String> fieldMap = new HashMap<>();
            fieldMap.put("field","MCR_AGE");
            List<Map> ageList = sysCodeService.getCodeByMap(fieldMap);
            // 此处需要将树形结构全部打平成list并且存到map中



            for (int i = 1; i < oList.size(); i++) {
                List<Object> list = oList.get(i);
                String excel1 = String.valueOf(list.get(1));
                String excel2 = String.valueOf(list.get(2));
                String excel3 = String.valueOf(list.get(3));
                String excel4 = String.valueOf(list.get(4));
                String excel5 = String.valueOf(list.get(5));
                String excel6 = String.valueOf(list.get(6));
                String excel7 = String.valueOf(list.get(7));
                String excel8 = String.valueOf(list.get(8));
                String excel9 = String.valueOf(list.get(9));
                String excel10 = String.valueOf(list.get(10));
                String excel11 = String.valueOf(list.get(11));
                String excel12 = String.valueOf(list.get(12));
                String excel13 = String.valueOf(list.get(13));
                String excel14 = String.valueOf(list.get(14));
                String excel15 = String.valueOf(list.get(15));
                String excel16 = String.valueOf(list.get(16));

                CjPlaceNameData cjPlaceNameData = new CjPlaceNameData();
                cjPlaceNameData.setName(excel1);
                cjPlaceNameData.setMapType(typeMap.get(excel2));
                cjPlaceNameData.setMapGroupBig(groupBig.get(excel3));
                cjPlaceNameData.setMapGroupSmall(groupSmall.get(excel4));
                cjPlaceNameData.setProvince(excel5);
                cjPlaceNameData.setCity(excel6);
                cjPlaceNameData.setDistrict(excel7);
                cjPlaceNameData.setGeographicLocation(excel8);
                cjPlaceNameData.setAncientRegionCategory(Integer.parseInt(excel9));
                cjPlaceNameData.setAge(excel10);
                cjPlaceNameData.setLongitude(excel11);
                cjPlaceNameData.setLatitude(excel12);
                cjPlaceNameData.setAdministrativeHistory(excel13);
                cjPlaceNameData.setIntroduction(excel14);
                cjPlaceNameData.setInfoSource(excel15);
                cjPlaceNameData.setRemarks(excel16);
                cjPlaceNameData.setCreateTime(LocalDateTime.now());
                cjPlaceNameData.setUpdateTime(LocalDateTime.now());
                try {
                    this.save(cjPlaceNameData);
                }catch (Exception e){
                    log.error("保存数据失败！{}", e.getMessage());
                }
            }
        }
        return true;
    }
}
