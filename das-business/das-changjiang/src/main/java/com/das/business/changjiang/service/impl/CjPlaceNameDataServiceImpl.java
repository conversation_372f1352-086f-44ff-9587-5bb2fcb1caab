package com.das.business.changjiang.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.business.changjiang.entity.CjPlaceNameData;
import com.das.business.changjiang.mapper.CjPlaceNameDataMapper;
import com.das.business.changjiang.service.ICjPlaceNameDataService;
import com.das.system.entity.PageEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * <p>
 * 地名资料表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@Service
@Transactional
@Slf4j
public class CjPlaceNameDataServiceImpl extends ServiceImpl<CjPlaceNameDataMapper, CjPlaceNameData> implements ICjPlaceNameDataService {

    @Resource
    private CjPlaceNameDataMapper cjPlaceNameDataMapper;

    @Override
    public IPage<CjPlaceNameData> getCjPlaceNameDataList(PageEntity pageEntity) {
        return cjPlaceNameDataMapper.getCjPlaceNameDataList(
                new Page<>(pageEntity.getCurrent(), pageEntity.getSize()),
                pageEntity.getParams(),
                pageEntity.getSort(),
                pageEntity.getOrder()
        );
    }

    @Override
    public CjPlaceNameData getCjPlaceNameDataById(Long id) {
        return cjPlaceNameDataMapper.getCjPlaceNameDataById(id);
    }

    @Override
    public boolean saveCjPlaceNameData(CjPlaceNameData cjPlaceNameData) {
        try {
            if (cjPlaceNameData.getId() == null) {
                // 新增
                cjPlaceNameData.setCreateTime(LocalDateTime.now());
                cjPlaceNameData.setUpdateTime(LocalDateTime.now());
                if (cjPlaceNameData.getPublishStatus() == null) {
                    cjPlaceNameData.setPublishStatus(0); // 默认未发布
                }
                return this.save(cjPlaceNameData);
            } else {
                // 更新
                cjPlaceNameData.setUpdateTime(LocalDateTime.now());
                return this.updateById(cjPlaceNameData);
            }
        } catch (Exception e) {
            log.error("保存地名资料失败", e);
            return false;
        }
    }

    @Override
    public boolean deleteCjPlaceNameData(Long id) {
        try {
            return this.removeById(id);
        } catch (Exception e) {
            log.error("删除地名资料失败", e);
            return false;
        }
    }

    @Override
    public boolean batchDeleteCjPlaceNameData(Long[] ids) {
        try {
            return this.removeByIds(Arrays.asList(ids));
        } catch (Exception e) {
            log.error("批量删除地名资料失败", e);
            return false;
        }
    }

    @Override
    public boolean updatePublishStatus(Long id, Integer publishStatus) {
        try {
            CjPlaceNameData placeNameData = new CjPlaceNameData();
            placeNameData.setId(id);
            placeNameData.setPublishStatus(publishStatus);
            placeNameData.setUpdateTime(LocalDateTime.now());
            return this.updateById(placeNameData);
        } catch (Exception e) {
            log.error("更新地名资料发布状态失败", e);
            return false;
        }
    }
}
