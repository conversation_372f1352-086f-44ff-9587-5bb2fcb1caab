package com.das.business.changjiang.controller;


import com.das.annotation.RequestSecurity;
import com.das.business.changjiang.entity.CjReport;
import com.das.business.changjiang.service.impl.CjReportServiceImpl;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
@Api(value = "长江GIS文献接口", description = "长江GIS文献接口")
@RestController
@RequestMapping("/cjReport")
public class CjReportController {

    @Resource
    private CjReportServiceImpl cjReportService;

    /**
     * 分页查询（前端网页）
     */
    @ApiOperation(value = "分页查询文献信息")
    @RequestSecurity(value = "/getCjReportListWeb",method = RequestMethod.POST)
    public ResultMsg getCjReportListWeb(@RequestBody PageEntity pageEntity){
        pageEntity.getParams().put("publish","1");//查询发布数据
        return new ResultMsg(cjReportService.getCjReportList(pageEntity));
    }


    /**
     * 分页查询
     */
    @ApiOperation(value = "分页查询文献信息")
    @RequestSecurity(value = "/getCjReportList",method = RequestMethod.POST)
    public ResultMsg getCjReportList(@RequestBody PageEntity pageEntity){
        return new ResultMsg(cjReportService.getCjReportList(pageEntity));
    }

    /**
     * 获取最新的前5条数据
     */
    @ApiOperation(value = "获取最新的前5条数据")
    @RequestSecurity(value = "/getCjReportTop",method = RequestMethod.GET)
    public ResultMsg getCjReportTop(){
        return new ResultMsg(cjReportService.getCjReportTop());
    }

    /**
     * 新增or修改
     */
    @ApiOperation(value = "新增或修改文献信息")
    @RequestSecurity(value = "/saveCjReport",method = RequestMethod.POST)
    public ResultMsg saveCjReport(@RequestBody CjReport cjReport){
        return new ResultMsg(cjReportService.saveCjReport(cjReport));
    }

    /**
     * 删除信息
     */
    @ApiOperation(value = "删除文献信息")
    @RequestSecurity(value = "/delCjReport",method = RequestMethod.POST)
    public ResultMsg delCjReport(@RequestParam Long id){
        return new ResultMsg(cjReportService.delCjReport(id));
    }

    /**
     * 根据ID查询信息
     */
    @ApiOperation(value = "根据ID查询文献信息")
    @RequestSecurity(value = "/getCjReportById",method = RequestMethod.POST)
    public ResultMsg getCjReportById(@RequestParam Long id){
        return new ResultMsg(cjReportService.getCjReportById(id));
    }

    /**
     * 考古报告发布
     */
    @ApiOperation(value = "考古报告发布")
    @RequestSecurity(value = "/publishInfo",method = RequestMethod.POST)
    public ResultMsg publishInfo(@RequestBody CjReport cjReport){
        return new ResultMsg(cjReportService.publishInfo(cjReport));
    }

}
