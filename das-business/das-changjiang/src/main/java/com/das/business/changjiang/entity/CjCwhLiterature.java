package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 文献资料信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-02
 */
@Data
public class CjCwhLiterature extends Model<CjCwhLiterature> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 标题
     */
    private String name;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 作者
     */
    private String author;

    /**
     * 署名单位
     */
    private String signUnit;

    /**
     * 出版年份
     */
    private String publicationYear;

    /**
     * 出土单位
     */
    private String unearthedUnit;

    /**
     * 出版社/期刊名
     */
    private String press;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

}
