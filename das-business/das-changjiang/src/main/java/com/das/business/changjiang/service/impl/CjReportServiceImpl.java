package com.das.business.changjiang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.entity.CjFile;
import com.das.business.changjiang.entity.CjReport;
import com.das.business.changjiang.mapper.CjReportMapper;
import com.das.business.changjiang.service.ICjReportService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.system.entity.SysCode;
import com.das.system.entity.SysUser;
import com.das.utils.common.CangPConstant;
import com.das.utils.common.DateUtil;
import com.das.utils.common.SystemUtil;
import com.das.utils.response.PageEntity;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.poi.util.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
@Service
@Transactional
@Slf4j
public class CjReportServiceImpl extends ServiceImpl<CjReportMapper, CjReport> implements ICjReportService {

    @Resource
    private CjReportMapper cjReportMapper;

    @Resource
    private CjFileServiceImpl cjFileService;

    @Override
    public IPage<CjReport> getCjReportList(PageEntity pageEntity) {
        SysUser sysUser = (SysUser) SystemUtil.getLoginUser();
        pageEntity.getParams().put("userId", sysUser != null ? sysUser.getId() : null);
        formatParams(pageEntity);
        return cjReportMapper.getCjReportList(new Page<>(pageEntity.getCurrent(), pageEntity.getSize()),
                pageEntity.getParams(), pageEntity.getSort(), pageEntity.getOrder());
    }

    static void formatParams(PageEntity pageEntity) {
        if (pageEntity.getParams().containsKey("isDownload")) {
            pageEntity.getParams().put("isDownload", pageEntity.getParams().get("isDownload").equals(true) ? 1 : 0);
        }

        if (pageEntity.getParams().get("theme") instanceof String) {
            if (!"".equals(pageEntity.getParams().get("theme")) && pageEntity.getParams().get("theme") != null) {
                String[] theme = pageEntity.getParams().get("theme").toString().split(",");
                pageEntity.getParams().put("theme", Arrays.asList(theme));
            } else {
                pageEntity.getParams().put("theme", null);
            }
        }
        if (pageEntity.getParams().get("range") instanceof String) {
            if (!"".equals(pageEntity.getParams().get("range")) && pageEntity.getParams().get("range") != null) {
                String[] range = pageEntity.getParams().get("range").toString().split(",");
                pageEntity.getParams().put("range", Arrays.asList(range));
            } else {
                pageEntity.getParams().put("range", null);
            }
        }
        if (pageEntity.getParams().get("age") instanceof String) {
            if (!"".equals(pageEntity.getParams().get("age")) && pageEntity.getParams().get("age") != null) {
                String[] age = pageEntity.getParams().get("age").toString().split(",");
                pageEntity.getParams().put("age", Arrays.asList(age));
            } else {
                pageEntity.getParams().put("age", null);
            }
        }
        if (pageEntity.getParams().get("dataType") instanceof String) {
            if (!"".equals(pageEntity.getParams().get("dataType")) && pageEntity.getParams().get("dataType") != null) {
                String[] dataType = pageEntity.getParams().get("dataType").toString().split(",");
                pageEntity.getParams().put("dataType", Arrays.asList(dataType));
            } else {
                pageEntity.getParams().put("dataType", null);
            }
        }
    }

    @Override
    public List<CjReport> getCjReportTop() {
        val wrapper = new QueryWrapper<CjReport>();
        wrapper.eq("publish", "1");
        wrapper.orderByDesc("update_time").last("limit 0,5");
        return list(wrapper);
    }

    @Override
    public boolean saveCjReport(CjReport cjReport) {
        ArrayList<CjFile> fileList = new ArrayList<>();
        cjReport.setUpdateTime(DateUtil.getDay());
        if (saveOrUpdate(cjReport)) {
            val q = new QueryWrapper<CjFile>();
            q.eq("res_id", cjReport.getId());
            cjFileService.remove(q);
            int size = 0;
            if (cjReport.getCjFiles() != null && cjReport.getCjFiles().size() > 0) {
                for (int i = 0; i < cjReport.getCjFiles().size(); i++) {
                    CjFile cjFile = (cjReport.getCjFiles().get(i));
                    cjFile.setResId(cjReport.getId());
                    cjFile.setFileType(0);
                    fileList.add(cjFile);
                    if(cjFile.getFileSize()==null) {
                        break;
                    }
                    size += Math.round(cjFile.getFileSize() / 1024);
                }
            }
            cjFileService.saveBatch(fileList);
            if (size > 0) {
                val wrapper = Wrappers.<CjReport>lambdaUpdate().set(CjReport::getFileSize, size).eq(CjReport::getId, cjReport.getId());
                update(wrapper);
            }
        }
        return true;
    }

    @Override
    public boolean delCjReport(Long id) {
        val q = new QueryWrapper<CjFile>();
        q.eq("res_id", id);
        cjFileService.remove(q);
        return removeById(id);
    }

    @Override
    public CjReport getCjReportById(Long id) {
        SysUser sysUser = (SysUser) SystemUtil.getLoginUser();

        CjReport cjReport = cjReportMapper.getCjReportById(id, sysUser == null ? null : sysUser.getId());
        //更新访问量
        val wrapper = Wrappers.<CjReport>lambdaUpdate().
                set(CjReport::getPageView, cjReport.getPageView() + 1).eq(CjReport::getId, id);
        update(wrapper);
        return cjReport;
    }

    @Override
    public boolean publishInfo(CjReport cjReport) {
        CjReport cj = getById(cjReport.getId());
        if ((cj.getPublish() == 1 && cjReport.getFlag() == CangPConstant.PUBLISH) || (cj.getPublish() == 0 && cjReport.getFlag() == CangPConstant.UNPUBLISH)) {
            return false;
        }
        val wrapper = Wrappers.<CjReport>lambdaUpdate()
                .set(CjReport::getPublish, cjReport.getFlag() == 1 ? 1 : 0)
                .set(CjReport::getPublishTime, DateUtil.getDay())
                .eq(CjReport::getId, cjReport.getId());
        return update(wrapper);
    }

    @Override
    public List<CjReport> getCjReportByRuinsId(Long id) {
        SysUser sysUser = (SysUser) SystemUtil.getLoginUser();

        List<CjReport> cjReport = cjReportMapper.getCjReportByRuinsId(id, sysUser == null ? null : sysUser.getId());
        if (cjReport == null) {
            return new ArrayList();
        }
        //更新访问量
        cjReport.forEach(item->{
            val wrapper = Wrappers.<CjReport>lambdaUpdate().
                    set(CjReport::getPageView, item.getPageView() + 1).eq(CjReport::getId, id);
            update(wrapper);
        });
        return cjReport;
    }
}
