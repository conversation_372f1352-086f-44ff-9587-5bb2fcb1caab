package com.das.business.changjiang.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.das.business.changjiang.entity.CjDictionaries;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.business.changjiang.entity.CjMark;
import com.das.utils.response.PageEntity;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-26
 */
public interface ICjDictionariesService extends IService<CjDictionaries> {

    IPage<CjDictionaries> getCjDictionariesList(PageEntity pageEntity);

    List<Map> getCjDictionariesTree(String class1);

    boolean saveOrUpdateDic(CjDictionaries dictionaries);

    boolean removeDic(List<Long> ids);

}
