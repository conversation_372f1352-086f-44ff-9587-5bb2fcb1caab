package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 遗址发掘资料附件实体
 * </p>
 *
 * <AUTHOR>
 * @since 2022-5-23
 */ 
@Data
public class CjUnearthedMaterialFile implements Serializable {

    // 主键ID
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(name = "id", value = "主键ID", required = true)
    private Long id;

    @ApiModelProperty(name = "materialId", value = "发掘资料id", required = false)
    private Long materialId;

    @ApiModelProperty(name = "fileName", value = "文件名称", required = false)
    private String fileName;

    @ApiModelProperty(name = "fileType", value = "文件类型", required = false)
    private String fileType;

    @ApiModelProperty(name = "filePath", value = "文件路径", required = false)
    private String filePath;

    @ApiModelProperty(name = "createTime", value = "创建时间", required = false)
    private String createTime;

}
