package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
public class CjArchaeologyAge extends Model<CjArchaeologyAge> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 年代名称
     */
    private String name;

    /**
     * 内容
     */
    private String content;

    /**
     * 开始时间
     */
    private Integer start;

    /**
     * 结束时间
     */
    private Integer end;

    /**
     * 标题
     */
    private String title;


}
