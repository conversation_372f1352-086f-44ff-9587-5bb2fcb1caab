package com.das.business.changjiang.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.business.changjiang.entity.CjProblemFeedback;
import com.das.utils.response.PageEntity;

/**
 * <p>
 * 问题反馈表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
public interface ICjProblemFeedbackService extends IService<CjProblemFeedback> {

    /**
     * 分页查询问题反馈列表
     *
     * @param pageEntity 分页查询参数
     * @return 分页结果
     */
    IPage<CjProblemFeedback> getCjProblemFeedbackList(PageEntity pageEntity);

    /**
     * 根据ID查询问题反馈详情
     *
     * @param id 问题反馈ID
     * @return 问题反馈详情
     */
    CjProblemFeedback getCjProblemFeedbackById(Long id);

    /**
     * 保存或更新问题反馈
     *
     * @param cjProblemFeedback 问题反馈对象
     * @return 操作结果
     */
    boolean saveCjProblemFeedback(CjProblemFeedback cjProblemFeedback);

    /**
     * 删除问题反馈
     *
     * @param id 问题反馈ID
     * @return 操作结果
     */
    boolean deleteCjProblemFeedback(Long id);

    /**
     * 批量删除问题反馈
     *
     * @param ids 问题反馈ID数组
     * @return 操作结果
     */
    boolean batchDeleteCjProblemFeedback(Long[] ids);

    /**
     * 回复问题反馈
     *
     * @param id           问题反馈ID
     * @param replyContent 回复内容
     * @return 操作结果
     */
    boolean replyProblemFeedback(Long id, String replyContent);

    /**
     * 更新回复状态
     *
     * @param id         问题反馈ID
     * @param backStatus 回复状态
     * @return 操作结果
     */
    boolean updateBackStatus(Long id, Integer backStatus);
}
