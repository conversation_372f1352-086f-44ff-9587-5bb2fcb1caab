package com.das.business.changjiang.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.das.business.changjiang.entity.CjCollect;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.business.changjiang.entity.CjMark;
import com.das.utils.response.PageEntity;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
public interface ICjCollectService extends IService<CjCollect> {

    IPage<CjCollect> getCjCollectList(PageEntity pageEntity);

    boolean saveCjCollect(CjCollect collect);

    boolean delCjCollect(Long id);

    CjCollect getCjCollectById(Long id);

}
