package com.das.business.changjiang.controller;

import com.das.business.changjiang.entity.CjDigitalMaps;
import com.das.business.changjiang.service.ICjDigitalMapsService;

import com.das.annotation.RequestSecurity;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 数字地图管理
 */
@RestController
@RequestMapping("/cjDigitalMaps")
@Slf4j
public class CjDigitalMapsController {

    @Resource
    private ICjDigitalMapsService cjDigitalMapsService;

    /**
     * 分页查询数字地图列表
     */
    @RequestSecurity(value = "/getCjDigitalMapsList", method = RequestMethod.POST)
    public ResultMsg getCjDigitalMapsList(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(cjDigitalMapsService.getCjDigitalMapsList(pageEntity));
    }

    /**
     * 分页查询数字地图列表（前端网页）
     */
    @RequestSecurity(value = "/getCjDigitalMapsListWeb", method = RequestMethod.POST)
    public ResultMsg getCjDigitalMapsListWeb(@RequestBody PageEntity pageEntity) {
        pageEntity.getParams().put("publishStatus", "1"); // 查询已发布数据
        return new ResultMsg(cjDigitalMapsService.getCjDigitalMapsList(pageEntity));
    }

    /**
     * 根据ID查询数字地图详情
     */
    @RequestSecurity(value = "/getCjDigitalMapsById", method = RequestMethod.GET)
    public ResultMsg getCjDigitalMapsById(@RequestParam Long id) {
        return new ResultMsg(cjDigitalMapsService.getCjDigitalMapsById(id));
    }

    /**
     * 新增或修改数字地图
     */
    @RequestSecurity(value = "/saveCjDigitalMaps", method = RequestMethod.POST)
    public ResultMsg saveCjDigitalMaps(@RequestBody CjDigitalMaps cjDigitalMaps) {
        return cjDigitalMapsService.saveCjDigitalMaps(cjDigitalMaps);
    }

    /**
     * 删除数字地图
     */
    @RequestSecurity(value = "/deleteCjDigitalMaps", method = RequestMethod.DELETE)
    public ResultMsg deleteCjDigitalMaps(@RequestParam Long id) {
        return new ResultMsg(cjDigitalMapsService.deleteCjDigitalMaps(id));
    }

    /**
     * 批量删除数字地图
     */
    @RequestSecurity(value = "/batchDeleteCjDigitalMaps", method = RequestMethod.DELETE)
    public ResultMsg batchDeleteCjDigitalMaps(@RequestBody Long[] ids) {
        return new ResultMsg(cjDigitalMapsService.batchDeleteCjDigitalMaps(ids));
    }

    /**
     * 更新发布状态
     */
    @RequestSecurity(value = "/updatePublishStatus", method = RequestMethod.PUT)
    public ResultMsg updatePublishStatus(@RequestParam Long id, @RequestParam Integer publishStatus) {
        return new ResultMsg(cjDigitalMapsService.updatePublishStatus(id, publishStatus));
    }
}
