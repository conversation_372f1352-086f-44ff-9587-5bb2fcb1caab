package com.das.business.changjiang.controller;

import com.das.business.changjiang.entity.CjDigitalMaps;
import com.das.business.changjiang.service.ICjDigitalMapsService;
import com.das.common.annotation.DasController;
import com.das.common.annotation.RequestSecurity;
import com.das.system.entity.PageEntity;
import com.das.system.entity.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 数字地图表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@Api(value = "数字地图接口", description = "数字地图接口")
@DasController("/cjDigitalMaps")
public class CjDigitalMapsController {

    @Resource
    private ICjDigitalMapsService cjDigitalMapsService;

    /**
     * 分页查询数字地图列表
     */
    @ApiOperation(value = "分页查询数字地图列表")
    @RequestSecurity(value = "/getCjDigitalMapsList", method = RequestMethod.POST)
    public ResultMsg getCjDigitalMapsList(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(cjDigitalMapsService.getCjDigitalMapsList(pageEntity));
    }

    /**
     * 分页查询数字地图列表（前端网页）
     */
    @ApiOperation(value = "分页查询数字地图列表（前端网页）")
    @RequestSecurity(value = "/getCjDigitalMapsListWeb", method = RequestMethod.POST)
    public ResultMsg getCjDigitalMapsListWeb(@RequestBody PageEntity pageEntity) {
        pageEntity.getParams().put("publishStatus", "1"); // 查询已发布数据
        return new ResultMsg(cjDigitalMapsService.getCjDigitalMapsList(pageEntity));
    }

    /**
     * 根据ID查询数字地图详情
     */
    @ApiOperation(value = "根据ID查询数字地图详情")
    @RequestSecurity(value = "/getCjDigitalMapsById", method = RequestMethod.GET)
    public ResultMsg getCjDigitalMapsById(@RequestParam Long id) {
        return new ResultMsg(cjDigitalMapsService.getCjDigitalMapsById(id));
    }

    /**
     * 新增或修改数字地图
     */
    @ApiOperation(value = "新增或修改数字地图")
    @RequestSecurity(value = "/saveCjDigitalMaps", method = RequestMethod.POST)
    public ResultMsg saveCjDigitalMaps(@RequestBody CjDigitalMaps cjDigitalMaps) {
        boolean result = cjDigitalMapsService.saveCjDigitalMaps(cjDigitalMaps);
        if (result) {
            return new ResultMsg("操作成功");
        } else {
            return new ResultMsg(500, "操作失败");
        }
    }

    /**
     * 删除数字地图
     */
    @ApiOperation(value = "删除数字地图")
    @RequestSecurity(value = "/deleteCjDigitalMaps", method = RequestMethod.DELETE)
    public ResultMsg deleteCjDigitalMaps(@RequestParam Long id) {
        boolean result = cjDigitalMapsService.deleteCjDigitalMaps(id);
        if (result) {
            return new ResultMsg("删除成功");
        } else {
            return new ResultMsg(500, "删除失败");
        }
    }

    /**
     * 批量删除数字地图
     */
    @ApiOperation(value = "批量删除数字地图")
    @RequestSecurity(value = "/batchDeleteCjDigitalMaps", method = RequestMethod.DELETE)
    public ResultMsg batchDeleteCjDigitalMaps(@RequestBody Long[] ids) {
        boolean result = cjDigitalMapsService.batchDeleteCjDigitalMaps(ids);
        if (result) {
            return new ResultMsg("批量删除成功");
        } else {
            return new ResultMsg(500, "批量删除失败");
        }
    }

    /**
     * 更新发布状态
     */
    @ApiOperation(value = "更新发布状态")
    @RequestSecurity(value = "/updatePublishStatus", method = RequestMethod.PUT)
    public ResultMsg updatePublishStatus(@RequestParam Long id, @RequestParam Integer publishStatus) {
        boolean result = cjDigitalMapsService.updatePublishStatus(id, publishStatus);
        if (result) {
            return new ResultMsg("状态更新成功");
        } else {
            return new ResultMsg(500, "状态更新失败");
        }
    }
}
