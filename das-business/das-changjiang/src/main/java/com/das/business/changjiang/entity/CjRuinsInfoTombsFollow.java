package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * <p>
 * 随葬遗物
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CjRuinsInfoTombsFollow extends Model<CjRuinsInfoTombsFollow> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 遗物类型(1铜器;2陶器;3漆木、竹器;4玉、石、料器;5骨、角器;6丝、麻、皮革;7其他)
     */
    private Integer type;

    /**
     * 名称
     */
    private String name;

    /**
     * 数量
     */
    private Integer number;

    /**
     * 所属墓葬ID
     */
    private Long tombsId;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String updateTime;

    public CjRuinsInfoTombsFollow(Integer type, String name, Integer number, Long tombsId) {
        this.type = type;
        this.name = name;
        this.number = number;
        this.tombsId = tombsId;
    }

}
