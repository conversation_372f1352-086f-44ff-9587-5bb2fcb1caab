package com.das.business.changjiang.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.entity.CjMark;
import com.das.business.changjiang.mapper.CjMarkMapper;
import com.das.business.changjiang.service.ICjMarkService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.das.system.entity.SysUser;
import com.das.utils.common.SystemUtil;
import com.das.utils.response.PageEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
@Service
@Transactional
@Slf4j
public class CjMarkServiceImpl extends ServiceImpl<CjMarkMapper, CjMark> implements ICjMarkService {

    @Resource
    private CjMarkMapper cjMarkMapper;

    @Override
    public IPage<CjMark> getCjMarkList(PageEntity pageEntity) {
        SysUser sysUser = (SysUser) SystemUtil.getLoginUser();
        pageEntity.getParams().put("userId",sysUser.getId());
        return cjMarkMapper.getCjMarkList(new Page<>(pageEntity.getCurrent(),pageEntity.getSize()),
                pageEntity.getParams(),pageEntity.getSort(),pageEntity.getOrder());
    }

    @Override
    public boolean saveCjMark(CjMark cjMark) {
        SysUser sysUser = (SysUser)SystemUtil.getLoginUser();
        cjMark.setUserId(sysUser.getId());
        //去掉转义字符
        String content = cjMark.getContent().replaceAll("\\\\\"","\\\"");
        //去掉在[]中的首尾引号，并转成数组
        String[] str = content.substring(2,content.length()-2).split("}\\\",\\\"");
        //将数组拼接成json格式
        cjMark.setContent("["+StringUtils.join(str,"},")+"]");
        return saveOrUpdate(cjMark);
    }

    @Override
    public boolean delCjMark(Long id) {
        return removeById(id);
    }

    @Override
    public CjMark getCjMarkById(Long id) {
        return cjMarkMapper.getCjMarkById(id);
    }
}
