package com.das.business.changjiang.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.business.changjiang.entity.CjCwhLiterature;
import com.das.utils.response.PageEntity;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 文献资料信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-02
 */
public interface ICjCwhLiteratureService extends IService<CjCwhLiterature> {

    IPage<CjCwhLiterature> findByPage(PageEntity pageEntity);

    boolean importExcel(MultipartFile file);

}
