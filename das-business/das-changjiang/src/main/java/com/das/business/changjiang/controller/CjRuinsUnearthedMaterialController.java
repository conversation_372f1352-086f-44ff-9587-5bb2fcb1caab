package com.das.business.changjiang.controller;


import com.das.annotation.RequestSecurity;
import com.das.business.changjiang.entity.CjRuinsUnearthedMaterial;
import com.das.business.changjiang.service.ICjRuinsUnearthedMaterialService;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 考古遗址发掘资料模块
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
@Api(value = "考古遗址发掘资料模块", description = "考古遗址发掘资料模块")
@RestController
@RequestMapping("/cjRuinsMaterial")
public class CjRuinsUnearthedMaterialController {

    @Resource
    private ICjRuinsUnearthedMaterialService cjRuinsMaterialService;

    /**
     * 分页查询
     */
    @ApiOperation(value = "分页查询考古遗址发掘资料信息")
    @RequestSecurity(value = "/getCjRuinsMaterialList", method = RequestMethod.POST)
    public ResultMsg getCjRuinsMaterialList(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(cjRuinsMaterialService.getCjRuinsMaterialList(pageEntity));
    }

    /**
     * 新增or修改考古遗址发掘资料
     */
    @ApiOperation(value = "新增or修改考古遗址发掘资料")
    @RequestSecurity(value = "/saveCjRuinsMaterial", method = RequestMethod.POST)
    public ResultMsg saveCjRuinsMaterial(@RequestBody CjRuinsUnearthedMaterial cjRuinsMaterial) {
        return new ResultMsg(cjRuinsMaterialService.saveCjRuinsMaterial(cjRuinsMaterial));
    }

    /**
     * 删除考古遗址发掘资料
     */
    @ApiOperation(value = "删除考古遗址发掘资料信息")
    @RequestSecurity(value = "/delCjRuinsMaterial", method = RequestMethod.POST)
    public ResultMsg delCjRuinsMaterial(@RequestBody List<Long> ids) {
        return new ResultMsg(cjRuinsMaterialService.delCjRuinsMaterial(ids));
    }

    /**
     * 根据id查询考古遗址发掘资料
     */
    @ApiOperation(value = "根据id查询考古遗址发掘资料")
    @RequestSecurity(value = "/getCjRuinsMaterialById", method = RequestMethod.POST)
    public ResultMsg getCjRuinsMaterialById(@RequestParam Long id) {
        return new ResultMsg(cjRuinsMaterialService.getCjRuinsMaterialById(id));
    }

    /**
     * 根据id查询考古遗址发掘资料分类
     */
    @ApiOperation(value = "根据id查询考古遗址发掘资料分类")
    @RequestSecurity(value = "/getTypeAndCount", method = RequestMethod.POST)
    public ResultMsg getTypeAndCount(@RequestParam Long ruinsId) {
        return new ResultMsg(cjRuinsMaterialService.getTypeAndCount(ruinsId));
    }

}
