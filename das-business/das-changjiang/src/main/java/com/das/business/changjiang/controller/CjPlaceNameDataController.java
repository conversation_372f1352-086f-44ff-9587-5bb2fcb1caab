package com.das.business.changjiang.controller;

import com.das.annotation.RequestSecurity;
import com.das.business.changjiang.entity.CjPlaceNameData;
import com.das.business.changjiang.service.ICjPlaceNameDataService;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultMsg;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 地名资料管理
 */
@RestController
@RequestMapping("/cjPlaceNameData")
public class CjPlaceNameDataController {

    @Resource
    private ICjPlaceNameDataService cjPlaceNameDataService;

    /**
     * 分页查询地名资料列表
     */
    @RequestSecurity(value = "/getCjPlaceNameDataList", method = RequestMethod.POST)
    public ResultMsg getCjPlaceNameDataList(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(cjPlaceNameDataService.getCjPlaceNameDataList(pageEntity));
    }

    /**
     * 分页查询地名资料列表（前端网页）
     */
    @RequestSecurity(value = "/getCjPlaceNameDataListWeb", method = RequestMethod.POST)
    public ResultMsg getCjPlaceNameDataListWeb(@RequestBody PageEntity pageEntity) {
        pageEntity.getParams().put("publishStatus", "1"); // 查询已发布数据
        return new ResultMsg(cjPlaceNameDataService.getCjPlaceNameDataList(pageEntity));
    }

    /**
     * 根据ID查询地名资料详情
     */
    @RequestSecurity(value = "/getCjPlaceNameDataById", method = RequestMethod.GET)
    public ResultMsg getCjPlaceNameDataById(@RequestParam Long id) {
        return new ResultMsg(cjPlaceNameDataService.getCjPlaceNameDataById(id));
    }

    /**
     * 新增或修改地名资料
     */
    @RequestSecurity(value = "/saveCjPlaceNameData", method = RequestMethod.POST)
    public ResultMsg saveCjPlaceNameData(@RequestBody CjPlaceNameData cjPlaceNameData) {
        return new ResultMsg(cjPlaceNameDataService.saveCjPlaceNameData(cjPlaceNameData));
    }

    /**
     * 删除地名资料
     */
    @RequestSecurity(value = "/deleteCjPlaceNameData", method = RequestMethod.DELETE)
    public ResultMsg deleteCjPlaceNameData(@RequestParam Long id) {
        return new ResultMsg(cjPlaceNameDataService.deleteCjPlaceNameData(id));
    }

    /**
     * 批量删除地名资料
     */
    @RequestSecurity(value = "/batchDeleteCjPlaceNameData", method = RequestMethod.DELETE)
    public ResultMsg batchDeleteCjPlaceNameData(@RequestBody Long[] ids) {
        return new ResultMsg(cjPlaceNameDataService.batchDeleteCjPlaceNameData(ids));
    }

    /**
     * 更新发布状态
     */
    @RequestSecurity(value = "/updatePublishStatus", method = RequestMethod.PUT)
    public ResultMsg updatePublishStatus(@RequestParam Long id, @RequestParam Integer publishStatus) {
        return new ResultMsg(cjPlaceNameDataService.updatePublishStatus(id, publishStatus));
    }

    /**
     * 根据excel模板导入数据
     * @param    file
     **/
    @PostMapping(value = "/importDataByExcel")
    public ResultMsg importDataByExcel(@RequestParam("file") MultipartFile file){
        String fileName = file.getOriginalFilename();
        if (StringUtils.isBlank(fileName)) {
            throw new RuntimeException("未获取到有效文件名称！");
        }
        String fileFormat = fileName.substring(fileName.lastIndexOf('.') + 1);
        // 限制文件上传类型
        if(!"xlsx".equals(fileFormat)){
            throw new RuntimeException("请使用固定xlsx文件模板上传！");
        }
        return new ResultMsg(cjPlaceNameDataService.importDataByExcel(file));
    }
}
