package com.das.business.changjiang.controller;

import com.das.business.changjiang.entity.CjPlaceNameData;
import com.das.business.changjiang.service.ICjPlaceNameDataService;
import com.das.common.annotation.DasController;
import com.das.common.annotation.RequestSecurity;
import com.das.system.entity.PageEntity;
import com.das.system.entity.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 地名资料表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@Api(value = "地名资料接口", description = "地名资料接口")
@DasController("/cjPlaceNameData")
public class CjPlaceNameDataController {

    @Resource
    private ICjPlaceNameDataService cjPlaceNameDataService;

    /**
     * 分页查询地名资料列表
     */
    @ApiOperation(value = "分页查询地名资料列表")
    @RequestSecurity(value = "/getCjPlaceNameDataList", method = RequestMethod.POST)
    public ResultMsg getCjPlaceNameDataList(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(cjPlaceNameDataService.getCjPlaceNameDataList(pageEntity));
    }

    /**
     * 分页查询地名资料列表（前端网页）
     */
    @ApiOperation(value = "分页查询地名资料列表（前端网页）")
    @RequestSecurity(value = "/getCjPlaceNameDataListWeb", method = RequestMethod.POST)
    public ResultMsg getCjPlaceNameDataListWeb(@RequestBody PageEntity pageEntity) {
        pageEntity.getParams().put("publishStatus", "1"); // 查询已发布数据
        return new ResultMsg(cjPlaceNameDataService.getCjPlaceNameDataList(pageEntity));
    }

    /**
     * 根据ID查询地名资料详情
     */
    @ApiOperation(value = "根据ID查询地名资料详情")
    @RequestSecurity(value = "/getCjPlaceNameDataById", method = RequestMethod.GET)
    public ResultMsg getCjPlaceNameDataById(@RequestParam Long id) {
        return new ResultMsg(cjPlaceNameDataService.getCjPlaceNameDataById(id));
    }

    /**
     * 新增或修改地名资料
     */
    @ApiOperation(value = "新增或修改地名资料")
    @RequestSecurity(value = "/saveCjPlaceNameData", method = RequestMethod.POST)
    public ResultMsg saveCjPlaceNameData(@RequestBody CjPlaceNameData cjPlaceNameData) {
        boolean result = cjPlaceNameDataService.saveCjPlaceNameData(cjPlaceNameData);
        if (result) {
            return new ResultMsg("操作成功");
        } else {
            return new ResultMsg(500, "操作失败");
        }
    }

    /**
     * 删除地名资料
     */
    @ApiOperation(value = "删除地名资料")
    @RequestSecurity(value = "/deleteCjPlaceNameData", method = RequestMethod.DELETE)
    public ResultMsg deleteCjPlaceNameData(@RequestParam Long id) {
        boolean result = cjPlaceNameDataService.deleteCjPlaceNameData(id);
        if (result) {
            return new ResultMsg("删除成功");
        } else {
            return new ResultMsg(500, "删除失败");
        }
    }

    /**
     * 批量删除地名资料
     */
    @ApiOperation(value = "批量删除地名资料")
    @RequestSecurity(value = "/batchDeleteCjPlaceNameData", method = RequestMethod.DELETE)
    public ResultMsg batchDeleteCjPlaceNameData(@RequestBody Long[] ids) {
        boolean result = cjPlaceNameDataService.batchDeleteCjPlaceNameData(ids);
        if (result) {
            return new ResultMsg("批量删除成功");
        } else {
            return new ResultMsg(500, "批量删除失败");
        }
    }

    /**
     * 更新发布状态
     */
    @ApiOperation(value = "更新发布状态")
    @RequestSecurity(value = "/updatePublishStatus", method = RequestMethod.PUT)
    public ResultMsg updatePublishStatus(@RequestParam Long id, @RequestParam Integer publishStatus) {
        boolean result = cjPlaceNameDataService.updatePublishStatus(id, publishStatus);
        if (result) {
            return new ResultMsg("状态更新成功");
        } else {
            return new ResultMsg(500, "状态更新失败");
        }
    }
}
