package com.das.business.changjiang.controller;


import com.das.annotation.DasController;
import com.das.business.changjiang.entity.CjCwhUnearthedRelic;
import com.das.business.changjiang.service.ICjCwhUnearthedRelicService;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 出土文物信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-02
 */
@Api(value = "出土文物信息表", description = "出土文物信息表")
@DasController("/cjCwhUnearthedRelic")
public class CjCwhUnearthedRelicController {

    @Resource
    ICjCwhUnearthedRelicService unearthedRelicService;

    @ApiOperation(value = "分页查询")
    @RequestMapping(value = "/findByPage", method = RequestMethod.POST)
    public ResultMsg findByPage(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(unearthedRelicService.findByPage(pageEntity));
    }

    @ApiOperation(value = "根据ID查询详情")
    @RequestMapping(value = "/getById", method = RequestMethod.POST)
    public ResultMsg getById(@RequestBody CjCwhUnearthedRelic cjCwhUnearthedRelic) {
        return new ResultMsg(unearthedRelicService.getById(cjCwhUnearthedRelic.getId()));
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "/save")
    public ResultMsg save(@RequestBody CjCwhUnearthedRelic cjCwhUnearthedRelic) {
        return new ResultMsg(unearthedRelicService.save(cjCwhUnearthedRelic));
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "/updateById")
    public ResultMsg updateById(@RequestBody CjCwhUnearthedRelic cjCwhUnearthedRelic) {
        return new ResultMsg(unearthedRelicService.updateById(cjCwhUnearthedRelic));
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "/removeByIds")
    public ResultMsg removeByIds(@RequestBody List<Long> ids) {
        return new ResultMsg(unearthedRelicService.removeByIds(ids));
    }

    @ApiOperation(value = "导入")
    @PostMapping(value = "/importExcel")
    public ResultMsg importExcel(MultipartFile file) {
        return new ResultMsg(unearthedRelicService.importExcel(file));
    }


}
