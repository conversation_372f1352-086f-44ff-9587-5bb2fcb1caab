package com.das.business.changjiang.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.das.business.changjiang.controller.vo.CjRuinsInfoTombsVO;
import com.das.business.changjiang.entity.CjRuinsInfoTombs;
import com.baomidou.mybatisplus.extension.service.IService;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultMsg;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * 墓地基本信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
public interface ICjRuinsInfoTombsService extends IService<CjRuinsInfoTombs> {

    /**
     * 查询详情
     * @param id
     * @return
     */
    CjRuinsInfoTombsVO getDetailById(Long id);

    /**
     * 分页查询
     * @param pageEntity
     * @return
     */
    IPage<CjRuinsInfoTombs> findByPage(PageEntity pageEntity);

    /**
     * 新增墓葬
     * @param cjRuinsInfoTombsVO
     * @return
     */
    boolean add(CjRuinsInfoTombsVO cjRuinsInfoTombsVO);

    /**
     * 修改
     * @param cjRuinsInfoTombsVO
     * @return
     */
    boolean update(CjRuinsInfoTombsVO cjRuinsInfoTombsVO);

}
