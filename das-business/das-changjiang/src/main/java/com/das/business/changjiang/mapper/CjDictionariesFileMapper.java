package com.das.business.changjiang.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.das.business.changjiang.entity.CjDictionaries;
import com.das.business.changjiang.entity.CjDictionariesFile;
import com.das.system.entity.SysCode;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
public interface CjDictionariesFileMapper extends BaseMapper<CjDictionariesFile> {

    @Delete("delete from cj_dictionaries_file where dic_id = #{dicId}")
    boolean delFile(Long dicId);

}
