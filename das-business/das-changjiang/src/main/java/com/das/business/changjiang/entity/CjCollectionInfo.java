package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Data
public class CjCollectionInfo extends Model<CjCollectionInfo> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 藏品名称
     */
    private String name;

    /**
     * 藏品级别
     */
    private Integer level;

    /**
     * 藏品类别
     */
    private Integer type;

    /**
     * 文物分类(1:可移动文物；2:不可移动文物)
     */
    private Integer classify;


    /**
     * 出土位置
     */
    private String excavated;

    /**
     * 出土时间
     */
    private String excavatedTime;

    /**
     * 所在地址
     */
    private String address;

    /**
     * 基本信息
     */
    private String information;

    /**
     * 年代
     */
    private Integer age;

    /**
     * 具体年代
     */
    private String ageDetail;

    /**
     * 颜色
     */
    private String color;

    /**
     * 作者
     */
    private String author;

    /**
     * 附属物说明
     */
    private String appendages;

    /**
     * 长(厘米)
     */
    private String length;

    /**
     * 宽(厘米)
     */
    private String width;

    /**
     * 高(厘米)
     */
    private String height;

    /**
     * 腹围(厘米)
     */
    private String presence;

    /**
     * 口径(厘米)
     */
    private String bore;

    /**
     * 底径（厘米）
     */
    private String bootBore;

    /**
     * 完残程度
     */
    private Integer complete;

    /**
     * 质地
     */
    private Integer texture;


    /**
     * 质地备注
     */
    private String textureInfo;

    /**
     * 图片地址
     */
    private String imgUrl;

    /**
     * 三维地址
     */
    private String threeUrl;

    @ApiModelProperty(name = "status", value = "解压状态（0未转换,1转换中,2转换成功,3转换失败）", required = false)
    private Integer status;

    @ApiModelProperty(name = "model", value = "模型展示地址", required = false)
    private String model;

    /**
     * 三维名称
     */
    private String fileName;

    /**
     * GPS
     */
    private String gps;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 创建时间
     */
    private String createTime;


    @TableField(exist = false)
    private String typeName;

    @TableField(exist = false)
    private String levelName;

    @TableField(exist = false)
    private String ageName;

    @TableField(exist = false)
    private String completeName;

    @TableField(exist = false)
    private String textureName;

    @TableField(exist = false)
    private String classifyName;

    @TableField(exist = false)
    private List<CjFile> cjFiles;

}
