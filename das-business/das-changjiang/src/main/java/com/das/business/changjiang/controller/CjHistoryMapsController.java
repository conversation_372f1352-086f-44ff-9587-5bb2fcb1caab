package com.das.business.changjiang.controller;

import com.das.business.changjiang.entity.CjHistoryMaps;
import com.das.business.changjiang.service.ICjHistoryMapsService;
import com.das.common.annotation.DasController;
import com.das.common.annotation.RequestSecurity;
import com.das.system.entity.PageEntity;
import com.das.system.entity.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 历史地图表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@Api(value = "历史地图接口", description = "历史地图接口")
@DasController("/cjHistoryMaps")
public class CjHistoryMapsController {

    @Resource
    private ICjHistoryMapsService cjHistoryMapsService;

    /**
     * 分页查询历史地图列表
     */
    @ApiOperation(value = "分页查询历史地图列表")
    @RequestSecurity(value = "/getCjHistoryMapsList", method = RequestMethod.POST)
    public ResultMsg getCjHistoryMapsList(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(cjHistoryMapsService.getCjHistoryMapsList(pageEntity));
    }

    /**
     * 分页查询历史地图列表（前端网页）
     */
    @ApiOperation(value = "分页查询历史地图列表（前端网页）")
    @RequestSecurity(value = "/getCjHistoryMapsListWeb", method = RequestMethod.POST)
    public ResultMsg getCjHistoryMapsListWeb(@RequestBody PageEntity pageEntity) {
        pageEntity.getParams().put("publishStatus", "1"); // 查询已发布数据
        return new ResultMsg(cjHistoryMapsService.getCjHistoryMapsList(pageEntity));
    }

    /**
     * 根据ID查询历史地图详情
     */
    @ApiOperation(value = "根据ID查询历史地图详情")
    @RequestSecurity(value = "/getCjHistoryMapsById", method = RequestMethod.GET)
    public ResultMsg getCjHistoryMapsById(@RequestParam Long id) {
        return new ResultMsg(cjHistoryMapsService.getCjHistoryMapsById(id));
    }

    /**
     * 新增或修改历史地图
     */
    @ApiOperation(value = "新增或修改历史地图")
    @RequestSecurity(value = "/saveCjHistoryMaps", method = RequestMethod.POST)
    public ResultMsg saveCjHistoryMaps(@RequestBody CjHistoryMaps cjHistoryMaps) {
        boolean result = cjHistoryMapsService.saveCjHistoryMaps(cjHistoryMaps);
        if (result) {
            return new ResultMsg("操作成功");
        } else {
            return new ResultMsg(500, "操作失败");
        }
    }

    /**
     * 删除历史地图
     */
    @ApiOperation(value = "删除历史地图")
    @RequestSecurity(value = "/deleteCjHistoryMaps", method = RequestMethod.DELETE)
    public ResultMsg deleteCjHistoryMaps(@RequestParam Long id) {
        boolean result = cjHistoryMapsService.deleteCjHistoryMaps(id);
        if (result) {
            return new ResultMsg("删除成功");
        } else {
            return new ResultMsg(500, "删除失败");
        }
    }

    /**
     * 批量删除历史地图
     */
    @ApiOperation(value = "批量删除历史地图")
    @RequestSecurity(value = "/batchDeleteCjHistoryMaps", method = RequestMethod.DELETE)
    public ResultMsg batchDeleteCjHistoryMaps(@RequestBody Long[] ids) {
        boolean result = cjHistoryMapsService.batchDeleteCjHistoryMaps(ids);
        if (result) {
            return new ResultMsg("批量删除成功");
        } else {
            return new ResultMsg(500, "批量删除失败");
        }
    }

    /**
     * 更新发布状态
     */
    @ApiOperation(value = "更新发布状态")
    @RequestSecurity(value = "/updatePublishStatus", method = RequestMethod.PUT)
    public ResultMsg updatePublishStatus(@RequestParam Long id, @RequestParam Integer publishStatus) {
        boolean result = cjHistoryMapsService.updatePublishStatus(id, publishStatus);
        if (result) {
            return new ResultMsg("状态更新成功");
        } else {
            return new ResultMsg(500, "状态更新失败");
        }
    }
}
