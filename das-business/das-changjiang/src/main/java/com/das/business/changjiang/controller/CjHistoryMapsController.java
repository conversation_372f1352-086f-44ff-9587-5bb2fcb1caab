package com.das.business.changjiang.controller;

import com.das.annotation.RequestSecurity;
import com.das.business.changjiang.entity.CjHistoryMaps;
import com.das.business.changjiang.service.ICjHistoryMapsService;
import com.das.utils.response.PageEntity;
import com.das.utils.response.ResultMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 历史地图管理
 */
@RestController
@RequestMapping("/cjHistoryMaps")
public class CjHistoryMapsController {

    @Resource
    private ICjHistoryMapsService cjHistoryMapsService;

    /**
     * 分页查询历史地图列表
     */
    @RequestSecurity(value = "/getCjHistoryMapsList", method = RequestMethod.POST)
    public ResultMsg getCjHistoryMapsList(@RequestBody PageEntity pageEntity) {
        return new ResultMsg(cjHistoryMapsService.getCjHistoryMapsList(pageEntity));
    }

    /**
     * 分页查询历史地图列表（前端网页）
     */
    @RequestSecurity(value = "/getCjHistoryMapsListWeb", method = RequestMethod.POST)
    public ResultMsg getCjHistoryMapsListWeb(@RequestBody PageEntity pageEntity) {
        pageEntity.getParams().put("publishStatus", "1"); // 查询已发布数据
        return new ResultMsg(cjHistoryMapsService.getCjHistoryMapsList(pageEntity));
    }

    /**
     * 根据ID查询历史地图详情
     */
    @RequestSecurity(value = "/getCjHistoryMapsById", method = RequestMethod.GET)
    public ResultMsg getCjHistoryMapsById(@RequestParam Long id) {
        return new ResultMsg(cjHistoryMapsService.getCjHistoryMapsById(id));
    }

    /**
     * 新增或修改历史地图
     */
    @RequestSecurity(value = "/saveCjHistoryMaps", method = RequestMethod.POST)
    public ResultMsg saveCjHistoryMaps(@RequestBody CjHistoryMaps cjHistoryMaps) {
        return new ResultMsg(cjHistoryMapsService.saveCjHistoryMaps(cjHistoryMaps));
    }

    /**
     * 删除历史地图
     */
    @RequestSecurity(value = "/deleteCjHistoryMaps", method = RequestMethod.DELETE)
    public ResultMsg deleteCjHistoryMaps(@RequestParam Long id) {
        return new ResultMsg(cjHistoryMapsService.deleteCjHistoryMaps(id));
    }

    /**
     * 批量删除历史地图
     */
    @RequestSecurity(value = "/batchDeleteCjHistoryMaps", method = RequestMethod.DELETE)
    public ResultMsg batchDeleteCjHistoryMaps(@RequestBody Long[] ids) {
        return new ResultMsg(cjHistoryMapsService.batchDeleteCjHistoryMaps(ids));
    }

    /**
     * 更新发布状态
     */
    @RequestSecurity(value = "/updatePublishStatus", method = RequestMethod.PUT)
    public ResultMsg updatePublishStatus(@RequestParam Long id, @RequestParam Integer publishStatus) {
        return new ResultMsg(cjHistoryMapsService.updatePublishStatus(id, publishStatus));
    }
}
