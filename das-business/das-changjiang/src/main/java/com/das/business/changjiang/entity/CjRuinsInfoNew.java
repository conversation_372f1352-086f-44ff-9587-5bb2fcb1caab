package com.das.business.changjiang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * 考古遗址基础信息表-20241225调整业务后新表
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
public class CjRuinsInfoNew extends Model<CjRuinsInfoNew> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属父类
     */
    private Long parentId;

    /**
     * 文物名称
     */
    private String name;

    /**
     * 类别
     */
    private Long category;

    /**
     * 市
     */
    private String city;

    /**
     * 县
     */
    private String county;

    /**
     * 地址
     */
    private String address;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 海拔高度
     */
    private String altitude;

    /**
     * 发掘墓葬数量（座）
     */
    private Integer discoverTombs;

    /**
     * 探明墓葬数量（座）
     */
    private Integer proveTombs;

    /**
     * 发掘时间
     */
    private String discoverTime;

    /**
     * 时代
     */
    private String age;

    /**
     * 发掘经过
     */
    private String discoverLog;

    /**
     * 墓葬分布情况
     */
    private String tombsLayout;

    /**
     * 地理、环境描述
     */
    private String environment;

    /**
     * 周边遗迹描述
     */
    private String surroundingRuins;

    /**
     * 报告、简报
     */
    private String report;

    /**
     * 其他信息
     */
    private String otherInfo;

    /**
     * 墓地平面布局图(存储压缩的小图、加载大图默认名称去掉_min后加载)
     */
    private String layoutPlanUrl;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String updateTime;

}
